/**
 * 通用工具函数库
 * 包含时间处理、HTML转义等常用功能
 */

// 时间处理函数 - 统一所有时间显示为北京时间
const TimeUtils = {
  /**
   * 格式化日期时间为北京时间
   * @param {string} dateTimeStr - UTC时间字符串
   * @returns {string} 格式化后的北京时间字符串
   */
  formatDateTime: function(dateTimeStr) {
    if (!dateTimeStr) return '-';
    
    try {
      // 确保时间字符串被解释为UTC
      let utcDateString = dateTimeStr;
      // 如果时间字符串没有时区信息（不包含Z或+/-），添加Z表示UTC
      if (!dateTimeStr.includes('Z') && !dateTimeStr.includes('+') && !dateTimeStr.includes('-', 10)) {
        utcDateString = dateTimeStr + 'Z';
      }
      
      const date = new Date(utcDateString);
      if (isNaN(date.getTime())) {
        // 如果解析失败，尝试原始字符串
        const fallbackDate = new Date(dateTimeStr);
        if (isNaN(fallbackDate.getTime())) return dateTimeStr;
        return this._formatToBeijing(fallbackDate);
      }
      
      return this._formatToBeijing(date);
    } catch (e) {
      console.error("日期格式化错误:", e, dateTimeStr);
      return dateTimeStr;
    }
  },

  /**
   * 内部方法：将Date对象格式化为北京时间字符串
   * @private
   */
  _formatToBeijing: function(date) {
    // 使用 toLocaleString 指定北京时区
    const beijingTimeStr = date.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    // 将格式从 "2024/01/15, 18:30:00" 转换为 "2024/01/15 18:30:00"
    return beijingTimeStr.replace(/,\s*/, ' ');
  },

  /**
   * 将UTC时间转换为北京时间的ISO字符串（用于datetime-local输入框）
   * @param {string} utcDateString - UTC时间字符串
   * @returns {string} YYYY-MM-DDTHH:MM 格式的北京时间
   */
  toBeijingISOString: function(utcDateString) {
    if (!utcDateString) return '';
    try {
      // 确保时间字符串被解释为UTC
      let dateStr = utcDateString;
      if (!utcDateString.includes('Z') && !utcDateString.includes('+') && !utcDateString.includes('-', 10)) {
        dateStr = utcDateString + 'Z';
      }
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      // 转换为北京时间
      const beijingOffset = 8 * 60; // 北京时间是UTC+8
      const localOffset = date.getTimezoneOffset(); // 本地时区偏移（分钟）
      const totalOffset = beijingOffset + localOffset;
      
      const beijingDate = new Date(date.getTime() + totalOffset * 60 * 1000);
      
      // 格式化为 YYYY-MM-DDTHH:MM
      const year = beijingDate.getFullYear();
      const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
      const day = String(beijingDate.getDate()).padStart(2, '0');
      const hours = String(beijingDate.getHours()).padStart(2, '0');
      const minutes = String(beijingDate.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (e) {
      console.error('toBeijingISOString 错误:', e);
      return '';
    }
  },

  /**
   * 将北京时间的ISO字符串转换为UTC（用于提交到后端）
   * @param {string} beijingDateTimeString - 北京时间字符串
   * @returns {string|null} UTC ISO字符串
   */
  beijingToUTCString: function(beijingDateTimeString) {
    if (!beijingDateTimeString) return null;
    try {
      // 处理可能缺少秒数的日期时间字符串
      const formattedStr = beijingDateTimeString.includes(':') ? 
        (beijingDateTimeString.split(':').length >= 3 ? beijingDateTimeString : `${beijingDateTimeString}:00`) : 
        `${beijingDateTimeString}:00:00`;
      
      // 将输入视为北京时间
      const beijingDate = new Date(formattedStr);
      if (isNaN(beijingDate.getTime())) return null;
      
      // 转换为UTC
      const beijingOffset = 8 * 60; // 北京时间是UTC+8
      const utcDate = new Date(beijingDate.getTime() - beijingOffset * 60 * 1000);
      
      return utcDate.toISOString();
    } catch (e) {
      console.error('beijingToUTCString 错误:', e);
      return null;
    }
  }
};

// HTML处理函数
const HtmlUtils = {
  /**
   * HTML转义，防止XSS攻击
   * @param {string} str - 需要转义的字符串
   * @returns {string} 转义后的字符串
   */
  escapeHtml: function(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
  }
};

// Toast通知函数
const NotificationUtils = {
  /**
   * 显示 Toast 通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 ('info', 'success', 'warning', 'danger')
   * @param {number} duration - 持续时间（毫秒）
   */
  showToast: function(message, type = 'info', duration = 5000) {
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
      console.warn('Toast容器不存在');
      return;
    }

    const toastId = 'toast-' + Date.now();
    const formattedMessage = message.replace(/\n/g, '<br>');
    const toastHtml = `
      <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'info' ? 'primary' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body" style="white-space: pre-wrap;">
            ${formattedMessage}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: duration });
    toast.show();

    // 自动清理DOM
    toastElement.addEventListener('hidden.bs.toast', function () {
      toastElement.remove();
    });
  }
};

// 导出工具函数（兼容性考虑，同时支持全局变量）
if (typeof window !== 'undefined') {
  // 将主要函数挂载到window对象，保持向后兼容
  window.formatDateTime = TimeUtils.formatDateTime.bind(TimeUtils);
  window.toBeijingISOString = TimeUtils.toBeijingISOString.bind(TimeUtils);
  window.beijingToUTCString = TimeUtils.beijingToUTCString.bind(TimeUtils);
  window.escapeHtml = HtmlUtils.escapeHtml.bind(HtmlUtils);
  window.showToast = NotificationUtils.showToast.bind(NotificationUtils);
  
  // 兼容旧代码中的函数名
  window.toLocalISOString = TimeUtils.toBeijingISOString.bind(TimeUtils);
  window.toUTCISOString = TimeUtils.beijingToUTCString.bind(TimeUtils);
  
  // 同时导出工具对象
  window.TimeUtils = TimeUtils;
  window.HtmlUtils = HtmlUtils;
  window.NotificationUtils = NotificationUtils;
  
  // 添加测试函数
  window.testTimeConversion = function() {
    const testCases = [
      '2024-01-15T10:30:00Z',
      '2024-01-15T10:30:00.000Z',
      '2024-01-15T10:30:00+00:00',
      '2025-05-25T07:47:00.129500',
      new Date().toISOString()
    ];
    
    console.log('时间转换测试:');
    testCases.forEach(testTime => {
      console.log(`UTC: ${testTime} -> 北京时间: ${formatDateTime(testTime)}`);
    });
  };
}
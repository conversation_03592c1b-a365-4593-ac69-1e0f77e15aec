/************** 全局变量 **************/
// 仅在变量未定义时声明全局变量
// 统一管理全局数据缓存
var allDevices = [];
var allUsers = [];
var allSubscriptionTypes = [];
var allAdspowerAccounts = [];
var allSubscriptions = [];
var allSubscriptionInstances = []; // 新增: 存储所有订阅实例
var allOrders = []; // 新增: 存储所有订单

// 搜索状态
var currentDeviceSearchTerm = ''; // 当前设备搜索词

// 图表实例
var growthChart = null;
var subscriptionPieChart = null;
var dashboardTimeRange = 'month'; // 默认时间范围

// 新增：用于存储当前排序状态
const sortState = {
  devices: { column: null, direction: 'ascending' },
  users: { column: null, direction: 'ascending' },
  subscriptions: { column: null, direction: 'ascending' },
  adspower: { column: null, direction: 'ascending' },
  subscriptionInstances: { column: null, direction: 'ascending' } // 新增: 订阅实例排序状态
};

// 时间处理函数已移至 utils.js

// 新增：ARIA live region for announcements
const announcer = document.createElement('div');
announcer.setAttribute('aria-live', 'polite');
announcer.setAttribute('aria-atomic', 'true');
announcer.className = 'visually-hidden'; // Hide it visually but make it available to screen readers
document.body.appendChild(announcer);

// Function to make announcements
function announce(message) {
  announcer.textContent = message;
  // Clear the text after a short delay so it can be re-announced if needed
  setTimeout(() => {
    announcer.textContent = '';
  }, 1000);
}

// 安全设置元素文本内容的辅助函数
function safeSetTextContent(elementId, text) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = text;
  } else {
    console.warn(`元素 '${elementId}' 不存在，无法设置文本内容`);
  }
  return element;
}

// --- 新增：处理未授权响应的辅助函数 ---
function handleUnauthorizedAdminResponse(response) {
    if (response.status === 401) {
        showToast('您的登录已过期或无权访问，请重新登录。', 'danger');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // 延迟一小段时间以便用户看到 toast，然后重定向
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
        return true; // 表示已处理
    }
    return false; // 表示未处理（非401错误）
}

/************** 公共函数 **************/

// showToast 函数已移至 utils.js，这里使用全局函数

// 设置按钮加载状态
function setButtonLoading(buttonElement, isLoading, loadingText = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...') {
  if (!buttonElement) return;
  if (isLoading) {
    buttonElement.dataset.originalHtml = buttonElement.innerHTML; // 保存原始 HTML
    buttonElement.innerHTML = loadingText;
    buttonElement.disabled = true;
  } else {
    // 恢复原始 HTML 或默认值
    buttonElement.innerHTML = buttonElement.dataset.originalHtml || '操作'; 
    buttonElement.disabled = false;
    delete buttonElement.dataset.originalHtml; // 清理
  }
}

function getSubscriptionTypeName(type) {
  const typeMap = {
    monthly: "月付会员",
    quarterly: "季付会员",
    annual: "年付会员",
    student: "学生会员",
    trial: "试用会员",
    basic: "基础会员",
    premium: "高级会员",
    enterprise: "企业会员",
  };
  return typeMap[type] || "未知";
}
// formatDateTime 函数已移至 utils.js，这里使用全局函数
function getSubscriptionBadgeClass(status) {
  // Normalize status to lowercase for broader matching if needed, though current API seems to provide consistent Chinese strings.
  const lowerStatus = typeof status === 'string' ? status.toLowerCase() : '';

  switch (lowerStatus) {
    case "active":
    case "活跃":
    case "active": // Assuming '活跃' is the Chinese equivalent of active
      return "bg-success";
    case "expired":
    case "已过期":
    case "expired": // Assuming '已过期' is the Chinese equivalent of expired
      return "bg-danger";
    case "pending":
    case "待处理": // Example, add more Chinese statuses if they exist
      return "bg-warning";
    case "canceled":
    case "已取消": // Example
      return "bg-secondary";
    case "即将过期": // Directly handle Chinese status from user data if present
      return "bg-warning"; 
    default:
      return "bg-secondary"; // Default for unknown or other statuses
  }
}

/************** 导航与页面切换 **************/
async function showSection(sectionName) { // Declare as async function
  // console.log("切换到部分:", sectionName);

  document.querySelectorAll(".section-content").forEach((section) => {
    section.style.display = "none";
  });
  const targetSection = document.getElementById("section-" + sectionName);
        if (targetSection) {
    targetSection.style.display = "block";

    // 清除侧边导航 active 状态
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.classList.remove("active");
    });
    // 给当前的 link 加上 active
    const navLink = document.querySelector(
      `.nav-link[href="#${sectionName}"]`
    );
    if (navLink) navLink.classList.add("active");

    // 更新URL hash
    history.pushState(null, null, "#" + sectionName);

    // 动态加载数据
    if (sectionName === "dashboard") {
      loadDashboardStats();
    } else if (sectionName === "devices") {
      loadDevices(1, '');
    } else if (sectionName === "users") {
      loadUsers();
    } else if (sectionName === "adspower") {
      loadAdspowerAccounts();
    } else if (sectionName === "subscription-types") {
      loadSubscriptionTypes();
    } else if (sectionName === "subscriptions") { // New section
      loadSubscriptions();
    } else if (sectionName === "subscription-instances") { // 新增: 订阅实例
      await loadSubscriptionTypes(); // 确保订阅类型已加载
      loadSubscriptionInstances();
    } else if (sectionName === "orders") { // 订单管理
      loadOrders();
      loadOrderStatistics();
    } else if (sectionName === "device-audits") { // 设备审计
      loadDeviceAudits();
      loadDeviceAuditStats();
    }
  }
}

/************** 页面初始化 **************/
document.addEventListener("DOMContentLoaded", function () {
  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");
        if (!token || !user.id || !user.is_admin) {
            // 改用 toast 提示，并延迟跳转
            showToast("您没有权限访问管理后台，即将跳转到首页。", "danger");
            setTimeout(() => {
    window.location.href = "/";
            }, 3000); // 延迟3秒跳转
            return;
        }
  // Display user info in topbar
  const userDisplay = document.getElementById("user-display");
  if (userDisplay) {
    if (user.email) {
      userDisplay.textContent = "管理员: " + user.email;
    } else if (user.username) {
      userDisplay.textContent = "管理员: " + user.username;
    } else {
      userDisplay.textContent = "管理员";
    }
  }
  loadDashboardStats();

  // 先加载 AdsPower账号列表、订阅类型等（可按需）
  // loadAdspowerAccounts(); // <-- Remove this line
  // loadSubscriptionTypes(); // <-- Remove this line
  setInterval(loadDashboardStats, 60000);

  initEventListeners();

  // 根据hash跳转到相应页面
  const hash = window.location.hash.substring(1);
  if (hash) showSection(hash);
  else showSection("dashboard");
  
  // 更新账号按钮事件监听
  document.getElementById("update-account-btn").addEventListener("click", function() {
    // 获取表单数据
    const accountId = document.getElementById("edit-account-id").value;
    const token = localStorage.getItem("token");
    
    // 创建更新的数据对象
    const formData = {
      username: document.getElementById("edit-username").value,
      max_devices: parseInt(document.getElementById("edit-max_devices").value)
    };
    
    // 只有在有值的情况下才添加这些字段，避免清空原有数据
    const password = document.getElementById("edit-password").value;
    if (password) {
      formData.password = password;
    }
    
    const totpSecret = document.getElementById("edit-totp_secret").value;
    if (totpSecret) {
      formData.totp_secret = totpSecret;
    }
    
    const subscriptionInstanceId = document.getElementById("edit-subscription_instance").value;
    if (subscriptionInstanceId) {
      formData.subscription_instance_id = subscriptionInstanceId;
    } else {
      formData.subscription_instance_id = null; // 明确设置为null表示要解除关联
    }
    
    const cookies = document.getElementById("edit-cookies").value;
    if (cookies) {
      formData.cookies = cookies;
    }
    
    // 发送更新请求
    fetch(`/api/admin/accounts/adspower/${accountId}`, {
      method: "PUT",
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(formData)
    })
      .then(response => {
        console.log("服务器响应状态:", response.status);
        return response.json();
      })
      .then(data => {
        console.log("服务器响应数据:", data);
        if (data.success) {
          // alert("更新账号成功");
          showToast("更新账号成功", "success");
          
          // 关闭模态框
          const modal = bootstrap.Modal.getInstance(document.getElementById("editAccountModal"));
          modal.hide();
          
          // 重新加载列表
          loadAdspowerAccounts();
        } else {
          // alert(`更新账号失败: ${data.message || '未知错误'}`);
          showToast(`更新账号失败: ${data.message || '未知错误'}`, "danger");
        }
      })
      .catch(error => {
        console.error("更新账号失败:", error);
        // alert(`更新账号时出错: ${error.message}`);
        showToast(`更新账号时出错: ${error.message}`, "danger");
      });
  });
  
  // 批量设置订阅类型功能已删除

  // 加载订阅类型
  loadSubscriptionTypesForEdit();

  // 订阅类型改变事件
  const editSubscriptionTypeSelect = document.getElementById('edit-user-subscription-type');
  if (editSubscriptionTypeSelect) {
    editSubscriptionTypeSelect.addEventListener('change', async function() {
      const typeId = this.value;
      const instanceSelect = document.getElementById('edit-user-subscription-instance');
      const daysInput = document.getElementById('edit-user-subscription-days');
      
      if (!typeId) {
        instanceSelect.innerHTML = '<option value="">请先选择订阅类型...</option>';
        return;
      }
      
      // 更新默认天数
      const selectedOption = this.options[this.selectedIndex];
      daysInput.value = selectedOption.dataset.days || 30;
      
      // 加载该类型的实例
      await loadSubscriptionInstancesForType(typeId, instanceSelect);
    });
  }

  // 密码字段已默认显示

  // 保存用户更改按钮事件
  document.getElementById("save-user-changes-btn").addEventListener("click", function() {
    updateUser();
  });

  // 在编辑模态框中删除用户按钮事件
  const deleteUserInEditBtn = document.getElementById('delete-user-in-edit-btn');
  if (deleteUserInEditBtn) {
    deleteUserInEditBtn.addEventListener('click', function() {
      const userId = document.getElementById('edit-user-id').value;
      const userEmail = document.getElementById('edit-user-email').value;
      const adminUser = JSON.parse(localStorage.getItem("user") || "{}");

      if (parseInt(userId) === adminUser.id) {
        showToast("无法删除自己的账户。", "warning");
        return;
      }

      if (confirm(`确定要删除用户 ${userEmail} (ID: ${userId}) 吗？此操作将删除用户及其所有关联数据，且不可恢复。`)) {
        // 先关闭编辑模态框
        const editModal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
        editModal.hide();
        
        // 调用删除API
        callDeleteUserApi(userId);
      }
    });
  }

  // 新增：保存订阅更改按钮事件
  document.getElementById("save-subscription-changes-btn").addEventListener("click", function() {
    handleUpdateSubscription();
  });

  // 新增: 订阅实例管理相关按钮事件 (在 DOMContentLoaded 中确保元素存在)
  const saveInstanceBtn = document.getElementById("save-instance-btn");
  if (saveInstanceBtn) {
    saveInstanceBtn.addEventListener("click", handleAddSubscriptionInstance);
  }

  const updateInstanceBtn = document.getElementById("update-instance-btn");
  if (updateInstanceBtn) {
    updateInstanceBtn.addEventListener("click", handleUpdateSubscriptionInstance);
  }

  // 新增：为添加订阅实例模态框中的订阅类型下拉列表添加事件监听，以自动填充默认容量
  const addInstanceSubTypeSelect = document.getElementById('add-instance-subscription-type');
  const addInstanceCapacityField = document.getElementById('add-instance-capacity');

  if (addInstanceSubTypeSelect && addInstanceCapacityField) {
    addInstanceSubTypeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption && selectedOption.dataset.defaultCapacity) {
            addInstanceCapacityField.value = selectedOption.dataset.defaultCapacity;
        }
        // 如果选择的是"请选择订阅类型"或选中的类型没有默认容量，不清空字段，允许用户手动输入或保留之前的值
    });
  }
  
  // 添加AdsPower账号到实例的按钮事件
  const addAdspowerToInstanceBtn = document.getElementById('add-adspower-to-instance-btn');
  if (addAdspowerToInstanceBtn) {
    addAdspowerToInstanceBtn.addEventListener('click', addAdspowerToInstance);
  }

  // 为表格添加排序事件监听
  addSortEventListeners();
});

/************** 仪表盘、设备、用户数据加载 **************/
function loadDashboardStats() {
  const token = localStorage.getItem("token");
  
  // 定义安全设置文本的辅助函数
  const safeSetText = (id, text) => {
    const elem = document.getElementById(id);
    if (elem) elem.textContent = text;
  };
  
  Promise.all([
    fetch("/api/admin/users", {
      headers: { Authorization: `Bearer ${token}` },
    }).then((r) => {
        if (handleUnauthorizedAdminResponse(r)) return null;
        return r.json();
    }),
    fetch("/api/admin/devices", {
      headers: { Authorization: `Bearer ${token}` },
    }).then((r) => {
        if (handleUnauthorizedAdminResponse(r)) return null;
        return r.json();
    }),
    fetch("/api/admin/accounts/adspower", {
      headers: { Authorization: `Bearer ${token}` },
    }).then((r) => {
        if (handleUnauthorizedAdminResponse(r)) return null;
        return r.json();
    }),
    fetch("/api/admin/subscription-instances", {
      headers: { Authorization: `Bearer ${token}` },
    }).then((r) => {
        if (handleUnauthorizedAdminResponse(r)) return null;
        return r.json();
    }),
    fetch("/api/admin/subscriptions", {
      headers: { Authorization: `Bearer ${token}` },
    }).then((r) => {
        if (handleUnauthorizedAdminResponse(r)) return null;
        return r.json();
    }),
  ])
    .then(([usersData, devicesData, accountsData, instancesData, subscriptionsData]) => {
      if (!usersData && !devicesData && !accountsData && !instancesData) return; // All calls might have been handled by 401

      const users = usersData && usersData.data ? usersData.data.users || [] : [];
      const subscriptions = subscriptionsData && subscriptionsData.data ? subscriptionsData.data.subscriptions || [] : [];
      
      // 更新基础数据
      const activeUsers = users.filter((u) => u.is_active).length;
      
      // 更新核心业务指标
      safeSetText("metric-active-users", activeUsers);
      safeSetText("active-users", activeUsers);
      safeSetText("total-users", users.length);
      // 正确获取设备总数，优先使用 API 返回的 total
      const totalDeviceCount = devicesData.data ? devicesData.data.total || 0 : 0;
      safeSetText("total-devices", totalDeviceCount);
      
      // 更新最近登录逻辑 (如果需要，基于返回的 devices 列表)
      if (devicesData.data && devicesData.data.devices && devicesData.data.devices.length > 0) {
         const devicesForLogin = devicesData.data.devices;
         const sortedDevices = devicesForLogin
           .filter((d) => d.last_login)
           .sort((a, b) => new Date(b.last_login) - new Date(a.last_login));
         if (sortedDevices.length > 0) {
           safeSetText("latest-login", utcToBeijingTime(sortedDevices[0].last_login));
         }
       } else {
           safeSetText("latest-login", '-');
       }

      const activeSubscriptions = users.filter(
        (u) => u.subscription_status === "活跃"
      ).length;
      safeSetText("active-subscriptions", activeSubscriptions);
      
      const accounts = accountsData.accounts || [];
      safeSetText("total-adspower-accounts", accounts.length);
      
      let totalCapacity = 0, usedCapacity = 0;
      accounts.forEach((account) => {
        totalCapacity += account.max_devices || 0;
        usedCapacity += account.current_devices || 0;
      });
      safeSetText("used-capacity", `${usedCapacity} / ${totalCapacity}`);
      
      // 处理订阅实例数据
      if (instancesData && instancesData.success && instancesData.data && instancesData.data.subscription_instances) {
        const instances = instancesData.data.subscription_instances;
        safeSetText("total-instances", instances.length);
        
        const activeInstances = instances.filter(instance => instance.is_active).length;
        safeSetText("active-instances", activeInstances);
        
        // 计算实例总容量和已使用容量
        let totalInstanceCapacity = 0;
        let usedInstanceCapacity = 0;
        
        instances.forEach(instance => {
          totalInstanceCapacity += instance.capacity || 0;
          usedInstanceCapacity += instance.active_users_count || 0;
        });
        
        safeSetText("instances-capacity", `${usedInstanceCapacity} / ${totalInstanceCapacity}`);
      } else {
        // 处理没有获取到实例数据的情况
        safeSetText("total-instances", '0');
        safeSetText("active-instances", '0');
        safeSetText("instances-capacity", '0 / 0');
      }
      
      // 更新完数据后，初始化或更新图表
      updateDashboardCharts();
      
      // 更新其他仪表板元素
      updateDashboardMetrics(devicesData, instancesData);
      
      // 更新最后更新时间（北京时间）
      safeSetText("last-update", new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }));
    })
    .catch((error) => {
      console.error("Error loading dashboard stats:", error);
      
      // 使用safeSetText来安全地设置错误状态
      const errorIds = [
        "total-users", "total-devices", "total-adspower-accounts",
        "active-subscriptions", "active-users", "used-capacity",
        "latest-login", "total-instances", "active-instances",
        "instances-capacity", "metric-active-users", "metric-monthly-revenue",
        "metric-conversion-rate", "metric-retention-rate"
      ];
      
      errorIds.forEach(id => safeSetText(id, '错误'));
      
      showToast("加载仪表盘数据失败，请稍后重试", "warning");
    });
}

// 计算本月收入
function calculateMonthlyRevenue(subscriptions) {
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  return subscriptions
    .filter(sub => {
      const startDate = new Date(sub.start_date);
      return startDate.getMonth() === currentMonth && 
             startDate.getFullYear() === currentYear &&
             sub.status === 'active';
    })
    .reduce((total, sub) => total + (sub.price || 0), 0);
}

// UTC时间转换为北京时间字符串
function utcToBeijingTime(utcDateStr) {
  if (!utcDateStr) return '-';
  const date = new Date(utcDateStr);
  return date.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
}

// 更新仪表板其他指标
function updateDashboardMetrics(devicesData, instancesData) {
  // 计算设备使用率 - 基于有关联AdsPower账号的设备
  if (devicesData && devicesData.data) {
    const totalDevices = devicesData.data.total || 0;
    const devicesWithAccount = devicesData.data.devices ? 
      devicesData.data.devices.filter(d => d.adspower_account_id).length : 0;
    const deviceUsageRate = totalDevices > 0 ? 
      ((devicesWithAccount / totalDevices) * 100).toFixed(1) : 0;
    safeSetTextContent("device-usage-rate", `${deviceUsageRate}%`);
  }
  
  // 计算实例容量使用率
  if (instancesData && instancesData.data && instancesData.data.subscription_instances) {
    const instances = instancesData.data.subscription_instances;
    let totalCapacity = 0;
    let usedCapacity = 0;
    
    instances.forEach(instance => {
      totalCapacity += instance.capacity || 0;
      usedCapacity += instance.active_users_count || 0;
    });
    
    const capacityRate = totalCapacity > 0 ? 
      ((usedCapacity / totalCapacity) * 100).toFixed(1) : 0;
    safeSetTextContent("instance-capacity-rate", `${capacityRate}%`);
  }
}

// 初始化或更新图表
function updateDashboardCharts() {
  // 获取仪表板统计数据
  const token = localStorage.getItem("token");
  
  fetch(`/api/admin/dashboard/stats?range=${dashboardTimeRange}`, {
    headers: { Authorization: `Bearer ${token}` }
  })
    .then(response => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      return response.json();
    })
    .then(data => {
      if (!data || !data.success) return;
      
      const stats = data.data;
      
      // 更新核心业务指标
      if (stats.core_metrics) {
        safeSetTextContent("metric-active-users", stats.core_metrics.active_users);
        safeSetTextContent("metric-monthly-revenue", `¥${stats.core_metrics.monthly_revenue.toFixed(2)}`);
        safeSetTextContent("metric-conversion-rate", `${stats.core_metrics.conversion_rate}%`);
        safeSetTextContent("metric-retention-rate", `${stats.core_metrics.retention_rate}%`);
      }
      
      // 更新运营指标
      if (stats.operational_metrics) {
        safeSetTextContent("total-users", stats.operational_metrics.total_users);
        safeSetTextContent("active-subscriptions", stats.operational_metrics.active_subscriptions);
        safeSetTextContent("device-usage-rate", `${stats.operational_metrics.device_usage_rate}%`);
        safeSetTextContent("instance-capacity-rate", `${stats.operational_metrics.instance_capacity_rate}%`);
      }
      
      // 更新系统健康状态
      if (stats.system_health) {
        safeSetTextContent("response-time", `${stats.system_health.response_time}ms`);
        safeSetTextContent("error-rate", `${stats.system_health.error_rate}%`);
      }
      
      // 初始化图表
      if (stats.charts) {
        initGrowthChart(stats.charts.growth_trend);
        initSubscriptionPieChart(stats.charts.subscription_distribution);
      }
      
      // 加载活动时间线
      if (stats.activities) {
        loadActivityTimeline(stats.activities);
      }
      
      // 加载智能洞察
      if (stats.insights) {
        loadInsights(stats.insights);
      }
    })
    .catch(error => {
      console.error("加载仪表板统计数据失败:", error);
      showToast("加载统计数据失败，请稍后重试", "warning");
    });
}

// 初始化增长趋势图
function initGrowthChart(trendData) {
  const ctx = document.getElementById('growthChart');
  if (!ctx) return;
  
  // 如果图表已存在，先销毁
  if (growthChart) {
    growthChart.destroy();
  }
  
  // 如果没有数据，不创建图表
  if (!trendData || trendData.length === 0) {
    return;
  }
  
  const labels = trendData.map(item => item.label);
  const userData = trendData.map(item => item.users);
  const revenueData = trendData.map(item => item.revenue);
  const deviceData = trendData.map(item => item.devices);
  
  growthChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: '用户数',
        data: userData,
        borderColor: '#667eea',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        tension: 0.4,
        yAxisID: 'y-users'
      }, {
        label: '收入(¥)',
        data: revenueData,
        borderColor: '#0fd850',
        backgroundColor: 'rgba(15, 216, 80, 0.1)',
        tension: 0.4,
        hidden: true,
        yAxisID: 'y-revenue'
      }, {
        label: '设备数',
        data: deviceData,
        borderColor: '#f9c74f',
        backgroundColor: 'rgba(249, 199, 79, 0.1)',
        tension: 0.4,
        hidden: true,
        yAxisID: 'y-devices'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          }
        },
        'y-users': {
          type: 'linear',
          display: true,
          position: 'left',
          beginAtZero: true,
          grid: {
            borderDash: [3, 3]
          },
          title: {
            display: true,
            text: '用户数'
          }
        },
        'y-revenue': {
          type: 'linear',
          display: true,
          position: 'right',
          beginAtZero: true,
          grid: {
            drawOnChartArea: false
          },
          title: {
            display: true,
            text: '收入(¥)'
          }
        },
        'y-devices': {
          type: 'linear',
          display: false,
          position: 'left',
          beginAtZero: true,
          grid: {
            borderDash: [3, 3]
          },
          title: {
            display: true,
            text: '设备数'
          }
        }
      }
    }
  });
}

// 初始化订阅分布饼图
function initSubscriptionPieChart(distributionData) {
  const ctx = document.getElementById('subscriptionPieChart');
  if (!ctx) return;
  
  // 如果图表已存在，先销毁
  if (subscriptionPieChart) {
    subscriptionPieChart.destroy();
  }
  
  // 如果没有数据，不创建图表
  if (!distributionData || distributionData.length === 0) {
    return;
  }
  
  const subscriptionData = {
    labels: distributionData.map(item => item.label),
    data: distributionData.map(item => item.value)
  };
  
  subscriptionPieChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: subscriptionData.labels,
      datasets: [{
        data: subscriptionData.data,
        backgroundColor: [
          '#667eea',
          '#0fd850',
          '#f9c74f',
          '#4facfe',
          '#ff6b6b'
        ],
        borderWidth: 0
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 15,
            usePointStyle: true
          }
        }
      }
    }
  });
}


// 加载活动时间线
function loadActivityTimeline(activities) {
  const timeline = document.getElementById('activity-timeline');
  if (!timeline) return;
  
  // 如果没有活动数据，显示空状态
  if (!activities || activities.length === 0) {
    timeline.innerHTML = `
      <div class="text-center text-muted py-3">
        <i class="bi bi-inbox fs-3"></i>
        <p class="mt-2">暂无最近活动</p>
      </div>
    `;
    return;
  }
  
  let html = '';
  activities.forEach(activity => {
    html += `
      <div class="timeline-item ${activity.type === 'danger' ? 'danger' : activity.type === 'warning' ? 'warning' : ''}">
        <div class="d-flex justify-content-between align-items-start">
          <div>
            <i class="bi ${activity.icon} me-2"></i>
            <span>${activity.text}</span>
          </div>
          <small class="text-muted">${activity.time}</small>
        </div>
      </div>
    `;
  });
  
  timeline.innerHTML = html;
}

// 加载智能洞察
function loadInsights(insights) {
  const container = document.getElementById('insights-container');
  if (!container) return;
  
  // 如果没有洞察数据，显示默认信息
  if (!insights || insights.length === 0) {
    container.innerHTML = `
      <div class="insight-item">
        <i class="bi bi-info-circle"></i>
        <span>暂无洞察数据</span>
      </div>
    `;
    return;
  }
  
  let html = '';
  insights.forEach(insight => {
    html += `
      <div class="insight-item">
        <i class="bi ${insight.icon}"></i>
        <span>${insight.text}</span>
      </div>
    `;
  });
  
  container.innerHTML = html;
}

function loadDevices(page = 1, searchTerm = '') {
  const token = localStorage.getItem("token");
  const tableBodyId = "devices-table-body";
  const colspan = 6; // 设备表有6列（ID, 用户, 名称, 类型, IP, 操作）

  currentDevicesPage = page;
  currentDeviceSearchTerm = searchTerm; // 保存当前搜索词

  // 显示加载状态
  // document.getElementById("devices-table-body").innerHTML =
  //  '<tr><td colspan="6" class="text-center"><span class="spinner-border spinner-border-sm" role="status"></span> 加载中...</td></tr>'; // Correct colspan
  showTableLoading(tableBodyId, true, colspan);

  // 构建URL，如果有搜索词就添加search参数
  let url = `/api/admin/devices?page=${page}&per_page=${devicesPerPage}`;
  if (searchTerm) {
    url += `&search=${encodeURIComponent(searchTerm)}`;
  }

  fetch(url, {
    method: "GET",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (!response.ok) throw new Error("获取设备列表失败");
      return response.json();
    })
    .then((data) => {
      if (!data) return; // Handled by unauthorized check
      // Check the structure of data and if 'data.devices' array exists and is populated
      if (data.success && data.data && Array.isArray(data.data.devices)) { // Adjusted to check data.data.devices
        allDevices = data.data.devices; // 存储数据到全局变量
        // document.getElementById("total-devices").textContent = data.data.total; // Use total from pagination data - 元素不存在
        totalDevicesPages = data.data.pages || 1;
        currentDevicesPage = data.data.page || 1;
        showTableLoading(tableBodyId, false, colspan); // 隐藏加载
        updateDevicesTable(allDevices); // 使用全局数据更新表格
        updateDevicesPagination(); // 更新分页
      } else {
        allDevices = []; // 清空数据
        showTableLoading(tableBodyId, false, colspan); // 隐藏加载
        // API succeeded but returned no devices or unexpected format
        document.getElementById(tableBodyId).innerHTML =
          '<tr><td colspan="6" class="text-center">暂无设备数据</td></tr>'; // Correct colspan
      }
    })
    .catch((error) => {
      console.error("Error loading devices:", error);
      showTableLoading(tableBodyId, false, colspan); // 隐藏加载
      document.getElementById(tableBodyId).innerHTML = `<tr><td colspan="${colspan}" class="text-center text-danger">加载设备失败: ${error.message}</td></tr>`;
      showToast(`加载设备失败: ${error.message}`, "danger"); // 添加 Toast 提示
    });
}

function updateDevicesTable(devices) {
  const tbody = document.getElementById("devices-table-body");
  tbody.innerHTML = ""; // 清空表格

  if (!devices || devices.length === 0) {
    tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无设备数据</td></tr>'; // Adjusted colspan
    return;
  }

  devices.forEach((device) => {
    const tr = document.createElement("tr");

    // 正确访问数据并处理 null/undefined
    const ipDisplay = device.device_ip || "N/A"; // Use device_ip and provide default
    const userEmail = device.user && device.user.email ? device.user.email : "未知用户";
    const deviceType = device.device_type || "未知";

    tr.innerHTML = `
      <td>${device.id}</td>
      <td>${userEmail}</td>
      <td>${device.device_name || "未知设备"}</td>
      <td>${deviceType}</td>
      <td>${ipDisplay}</td>
      <td>
        <button class="btn btn-sm btn-primary view-device-btn" data-id="${device.id}" title="查看详情">
          <i class="bi bi-eye"></i>
        </button>
        <button class="btn btn-sm btn-danger delete-device-btn" data-device-id="${device.id}" title="删除设备">
          <i class="bi bi-trash"></i>
        </button>
      </td>
    `;
    tbody.appendChild(tr);
    
    // --- 为新按钮绑定事件 --- 
    const viewBtn = tr.querySelector(".view-device-btn");
    if (viewBtn) {
        viewBtn.addEventListener("click", () => fetchDeviceDetails(device.id));
    }
    const deleteBtn = tr.querySelector(".delete-device-btn");
    if (deleteBtn) {
        // 将 deleteBtn 元素传递给 deleteDevice 函数
        deleteBtn.addEventListener("click", () => {
            if (confirm(`确定要删除设备 \"${device.device_name || `设备 #${device.id}`}\" 吗？`)) {
                deleteDevice(device.id, deleteBtn); // 确保传递了 deleteBtn
            }
        });
    }
    // --- 事件绑定结束 --- 
  });

  // 重新计算总设备数 (可选, 但如果过滤了就不对了，最好用 allDevices.length)
  // document.getElementById("total-devices").textContent = devices.length;
}

// 设备管理分页函数
function updateDevicesPagination() {
  const paginationContainer = document.getElementById('devices-pagination');
  if (!paginationContainer) return;
  
  paginationContainer.innerHTML = '';
  
  // 如果只有一页，不显示分页
  if (totalDevicesPages <= 1) {
    return;
  }
  
  // 上一页按钮
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentDevicesPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `<a class="page-link" href="#" aria-label="上一页"><span aria-hidden="true">&laquo;</span></a>`;
  if (currentDevicesPage > 1) {
    prevLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadDevices(currentDevicesPage - 1, currentDeviceSearchTerm);
    });
  }
  paginationContainer.appendChild(prevLi);
  
  // 页码按钮
  const maxPagesToShow = 5;
  let startPage = Math.max(1, currentDevicesPage - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalDevicesPages, startPage + maxPagesToShow - 1);
  
  // 调整起始页
  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }
  
  // 如果起始页不是1，显示第一页和省略号
  if (startPage > 1) {
    const firstLi = document.createElement('li');
    firstLi.className = 'page-item';
    firstLi.innerHTML = '<a class="page-link" href="#">1</a>';
    firstLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadDevices(1);
    });
    paginationContainer.appendChild(firstLi);
    
    if (startPage > 2) {
      const ellipsisLi = document.createElement('li');
      ellipsisLi.className = 'page-item disabled';
      ellipsisLi.innerHTML = '<a class="page-link" href="#">...</a>';
      paginationContainer.appendChild(ellipsisLi);
    }
  }
  
  // 显示页码
  for (let i = startPage; i <= endPage; i++) {
    const pageLi = document.createElement('li');
    pageLi.className = `page-item ${i === currentDevicesPage ? 'active' : ''}`;
    pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
    
    if (i !== currentDevicesPage) {
      pageLi.addEventListener('click', (e) => {
        e.preventDefault();
        loadDevices(i, currentDeviceSearchTerm);
      });
    }
    
    paginationContainer.appendChild(pageLi);
  }
  
  // 如果结束页不是最后一页，显示省略号和最后一页
  if (endPage < totalDevicesPages) {
    if (endPage < totalDevicesPages - 1) {
      const ellipsisLi = document.createElement('li');
      ellipsisLi.className = 'page-item disabled';
      ellipsisLi.innerHTML = '<a class="page-link" href="#">...</a>';
      paginationContainer.appendChild(ellipsisLi);
    }
    
    const lastLi = document.createElement('li');
    lastLi.className = 'page-item';
    lastLi.innerHTML = `<a class="page-link" href="#">${totalDevicesPages}</a>`;
    lastLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadDevices(totalDevicesPages, currentDeviceSearchTerm);
    });
    paginationContainer.appendChild(lastLi);
  }
  
  // 下一页按钮
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentDevicesPage === totalDevicesPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `<a class="page-link" href="#" aria-label="下一页"><span aria-hidden="true">&raquo;</span></a>`;
  if (currentDevicesPage < totalDevicesPages) {
    nextLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadDevices(currentDevicesPage + 1, currentDeviceSearchTerm);
    });
  }
  paginationContainer.appendChild(nextLi);
}

// 全局变量用于分页
let currentDevicesPage = 1;
let totalDevicesPages = 1;
let devicesPerPage = 20;

let currentUsersPage = 1;
let totalUsersPages = 1;
let usersPerPage = 20;
let currentUserSearchQuery = '';

function loadUsers(page = 1) {
  loadUsersWithSearch('', page);
}

function loadUsersWithSearch(searchQuery = '', page = 1) {
  const token = localStorage.getItem("token");
  const tableBodyId = "users-table-body";
  const colspan = 10; // 用户表有10列
  currentUsersPage = page;
  currentUserSearchQuery = searchQuery;

  // 显示加载状态
  showTableLoading(tableBodyId, true, colspan);

  // 构建URL
  let url = `/api/admin/users?page=${page}&per_page=${usersPerPage}`;
  if (searchQuery) {
    url += `&search=${encodeURIComponent(searchQuery)}`;
  }

  // 使用分页参数
  fetch(url, { 
    headers: { Authorization: `Bearer ${token}` } 
  })
    .then((response) => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then((data) => {
            if (!data) return; // Handled by unauthorized check
            if (data.success && data.data) {
                // 处理分页数据
                if (data.data.pagination) {
                    currentUsersPage = data.data.pagination.page;
                    totalUsersPages = data.data.pagination.total_pages;
                    safeSetTextContent("total-users", data.data.pagination.total_items);
                    
                    // 更新分页UI
                    updateUsersPagination();
                }
                
                if (data.data.users && data.data.users.length > 0) {
                    allUsers = data.data.users; // 存储数据到全局变量
                    showTableLoading(tableBodyId, false, colspan); // 隐藏加载
                    updateUsersTable(allUsers); // 使用全局数据更新表格
                } else {
                    allUsers = []; // 清空数据
                    showTableLoading(tableBodyId, false, colspan); // 隐藏加载
                    document.getElementById(tableBodyId).innerHTML =
                      '<tr><td colspan="10" class="text-center">没有用户数据</td></tr>';
                }
            } else {
                allUsers = []; // 清空数据
                showTableLoading(tableBodyId, false, colspan); // 隐藏加载
                document.getElementById(tableBodyId).innerHTML =
                  '<tr><td colspan="10" class="text-center">没有用户数据</td></tr>';
            }
        })
    .catch((error) => {
      console.error("Error fetching users:", error);
      showTableLoading(tableBodyId, false, colspan); // 隐藏加载
      document.getElementById(tableBodyId).innerHTML = `<tr><td colspan="${colspan}" class="text-center text-danger">加载用户失败: ${error.message}</td></tr>`;
      showToast(`加载用户失败: ${error.message}`, "danger");
        });
    }

// 更新用户分页UI
function updateUsersPagination() {
  const paginationContainer = document.getElementById('users-pagination');
  if (!paginationContainer) return;
  
  paginationContainer.innerHTML = '';
  
  // 如果只有一页，不显示分页
  if (totalUsersPages <= 1) {
    return;
  }
  
  // 上一页按钮
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentUsersPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `<a class="page-link" href="#" aria-label="上一页"><span aria-hidden="true">&laquo;</span></a>`;
  if (currentUsersPage > 1) {
    prevLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadUsersWithSearch(currentUserSearchQuery, currentUsersPage - 1);
    });
  }
  paginationContainer.appendChild(prevLi);
  
  // 页码按钮
  const maxPagesToShow = 5;
  let startPage = Math.max(1, currentUsersPage - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalUsersPages, startPage + maxPagesToShow - 1);
  
  // 调整起始页
  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }
  
  // 如果起始页不是1，显示第一页和省略号
  if (startPage > 1) {
    const firstLi = document.createElement('li');
    firstLi.className = 'page-item';
    firstLi.innerHTML = '<a class="page-link" href="#">1</a>';
    firstLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadUsersWithSearch(currentUserSearchQuery, 1);
    });
    paginationContainer.appendChild(firstLi);
    
    if (startPage > 2) {
      const ellipsisLi = document.createElement('li');
      ellipsisLi.className = 'page-item disabled';
      ellipsisLi.innerHTML = '<a class="page-link" href="#">...</a>';
      paginationContainer.appendChild(ellipsisLi);
    }
  }
  
  // 显示页码
  for (let i = startPage; i <= endPage; i++) {
    const pageLi = document.createElement('li');
    pageLi.className = `page-item ${i === currentUsersPage ? 'active' : ''}`;
    pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
    
    if (i !== currentUsersPage) {
      pageLi.addEventListener('click', (e) => {
        e.preventDefault();
        loadUsersWithSearch(currentUserSearchQuery, i);
      });
    }
    
    paginationContainer.appendChild(pageLi);
  }
  
  // 如果结束页不是最后一页，显示省略号和最后一页
  if (endPage < totalUsersPages) {
    if (endPage < totalUsersPages - 1) {
      const ellipsisLi = document.createElement('li');
      ellipsisLi.className = 'page-item disabled';
      ellipsisLi.innerHTML = '<a class="page-link" href="#">...</a>';
      paginationContainer.appendChild(ellipsisLi);
    }
    
    const lastLi = document.createElement('li');
    lastLi.className = 'page-item';
    lastLi.innerHTML = `<a class="page-link" href="#">${totalUsersPages}</a>`;
    lastLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadUsersWithSearch(currentUserSearchQuery, totalUsersPages);
    });
    paginationContainer.appendChild(lastLi);
  }
  
  // 下一页按钮
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentUsersPage === totalUsersPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `<a class="page-link" href="#" aria-label="下一页"><span aria-hidden="true">&raquo;</span></a>`;
  if (currentUsersPage < totalUsersPages) {
    nextLi.addEventListener('click', (e) => {
      e.preventDefault();
      loadUsersWithSearch(currentUserSearchQuery, currentUsersPage + 1);
    });
  }
  paginationContainer.appendChild(nextLi);
}
    
    function updateUsersTable(users) {
  const tableBody = document.getElementById("users-table-body");
  tableBody.innerHTML = ""; // Clear previous content
  
  if (!users || users.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="10" class="text-center">暂无用户数据</td></tr>'; // Updated colspan
    return;
  }
  
  users.forEach((user) => {
    const row = document.createElement("tr");
    
    // Helper function to determine badge class based on subscription status text
    function getSubscriptionStatusClass(status) {
      if (status === '活跃') return 'bg-success';
      if (status === '已过期') return 'bg-danger';
      if (status === '即将过期') return 'bg-warning'; // Example: Add more statuses if needed
      return 'bg-secondary'; // Default for '无订阅' or other statuses
    }
      
  // Format dates or show placeholder
    const subscriptionEndDate = user.subscription_end_date ? formatDateTime(user.subscription_end_date) : '-';
    const lastLogin = user.last_login ? formatDateTime(user.last_login) : '从未';
    const createdAt = user.created_at ? formatDateTime(user.created_at) : '未知';
    
    // Determine activation button text and style
    const toggleButtonText = user.is_active ? '禁用' : '启用';
    const toggleButtonIcon = user.is_active ? 'bi-pause-circle' : 'bi-play-circle';
    const toggleButtonClass = user.is_active ? 'btn-warning' : 'btn-success'; // Use warning for disable, success for enable

    row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.email}</td>
                <td>
                  <span class="badge ${user.is_active ? 'bg-success' : 'bg-secondary'}">
                    ${user.is_active ? '已激活' : '未激活'}
                  </span>
                </td>
                <td>
                  <span class="badge ${user.is_admin ? 'bg-primary' : 'bg-light text-dark border'}">
                     ${user.is_admin ? '是' : '否'}
                  </span>
                </td>
                <td>${user.subscription_name || '-'}</td>
                <td>
                   <span class="badge ${getSubscriptionStatusClass(user.subscription_status)}">${user.subscription_status || '无订阅'}</span> 
                </td>
                <td>${subscriptionEndDate}</td>
                <td>${lastLogin}</td>
                <td>${createdAt}</td>
                <td>
                  <button class="btn btn-sm btn-outline-primary view-user-btn me-1" data-user-id="${user.id}" title="查看详情">
                    <i class="bi bi-eye"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-secondary edit-user-btn" data-user-id="${user.id}" title="编辑/管理">
                    <i class="bi bi-gear"></i>
                  </button>
                </td>
        `;
    tableBody.appendChild(row);
  });
  
  // Add event listeners AFTER appending rows
  document.querySelectorAll(".view-user-btn").forEach((button) => {
    button.addEventListener("click", (e) => {
      const userId = e.currentTarget.getAttribute("data-user-id");
      // Assuming viewUserDetails function exists and fetches details to show modal
      viewUserDetails(userId); 
    });
  });
  
  document.querySelectorAll(".edit-user-btn").forEach((button) => {
    button.addEventListener("click", (e) => {
      const userId = e.currentTarget.getAttribute("data-user-id");
      // Assuming editUser function exists, likely calls loadUserForEdit
      editUser(userId); 
    });
  });
  
}

// --- Placeholder for the toggle function ---
// We will implement the API call logic later if needed
function toggleUserActiveStatus(userId, currentIsActive, buttonElement) {
  const action = currentIsActive ? "禁用" : "启用";
  if (!confirm(`确定要 ${action} 用户 ID: ${userId} 吗？`)) {
    return;
  }
  
  const token = localStorage.getItem("token");
  const targetStatus = !currentIsActive; // The desired new status
  
  // Show loading state on the button
  const originalHtml = buttonElement.innerHTML;
  buttonElement.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>`;
  buttonElement.disabled = true;

  // API Call (Example using PUT /api/admin/users/{user_id})
  fetch(`/api/admin/users/${userId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    // Send only the field to be updated
    body: JSON.stringify({ is_active: targetStatus }) 
  })
  .then(response => {
    if (handleUnauthorizedAdminResponse(response)) return null;
    if (!response.ok) {
      // Try to get error message from response body
      return response.json().then(err => { throw new Error(err.message || `HTTP error ${response.status}`) });
    }
    return response.json();
  })
  .then(data => {
    // Restore button AFTER processing result
    buttonElement.innerHTML = originalHtml;
    buttonElement.disabled = false;
    if (data.success) {
      // alert(`用户 ${userId} 已成功 ${action}`);
      showToast(`用户 ${userId} 已成功 ${action}`, "success");
      loadUsers(); // Refresh the user list to show the updated status
    } else {
      // alert(`${action} 用户失败: ${data.message || '未知错误'}`);
      showToast(`${action} 用户失败: ${data.message || '未知错误'}`, "danger");
      // Restore button on failure (already done above)
    }
  })
  .catch(error => {
    console.error(`Error ${action} user:`, error);
    // alert(`${action} 用户时出错: ${error.message}`);
    showToast(`${action} 用户时出错: ${error.message}`, "danger");
    // Restore button on error
    buttonElement.innerHTML = originalHtml;
    buttonElement.disabled = false;
  });
}

function fetchDeviceDetails(deviceId) {
  const token = localStorage.getItem("token");

  fetch(`/api/admin/devices/${deviceId}`, {
    method: "GET",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (!response.ok) throw new Error("获取设备详情失败");
      return response.json();
    })
    .then((data) => showDeviceDetails(data))
    .catch((error) => { 
        // alert(`错误: ${error.message}`);
        showToast(`获取设备详情失败: ${error.message}`, "danger"); 
    });
}

/************** ADSpower账号管理相关函数 **************/
function loadAdspowerAccounts() {
  const token = localStorage.getItem("token");
  const tableBodyId = "accounts-table-body";
  const colspan = 8; // AdsPower账号表有8列

  // 显示加载状态
  // document.getElementById("accounts-table-body").innerHTML =
  //  '<tr><td colspan="8" class="text-center"><span class="spinner-border spinner-border-sm" role="status"></span> 加载中...</td></tr>'; // Colspan 8
  showTableLoading(tableBodyId, true, colspan);

  fetch("/api/admin/accounts/adspower", {
    method: "GET",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (!response.ok) throw new Error("获取账号列表失败");
      return response.json();
    })
    .then((data) => {
      if (!data) return; // Handled by unauthorized check
      if (data.success && data.data && data.data.accounts) {
        allAdspowerAccounts = data.data.accounts; // 存储数据
        showTableLoading(tableBodyId, false, colspan); // 隐藏加载
        updateAdspowerAccountsTable(allAdspowerAccounts); // 更新表格
            } else {
        allAdspowerAccounts = []; // 清空数据
        showTableLoading(tableBodyId, false, colspan); // 隐藏加载
        document.getElementById(tableBodyId).innerHTML =
          '<tr><td colspan="8" class="text-center">暂无账号数据</td></tr>'; // Colspan 8
      }
    })
    .catch((error) => {
      console.error("Error loading accounts:", error);
      showTableLoading(tableBodyId, false, colspan); // 隐藏加载
      document.getElementById(tableBodyId).innerHTML = `<tr><td colspan="${colspan}" class="text-center text-danger">加载账号失败: ${error.message}</td></tr>`;
      showToast(`加载账号失败: ${error.message}`, "danger"); // Toast 提示
    });
}

function updateAdspowerAccountsTable(accounts) {
  const tableBody = document.getElementById("accounts-table-body");
  tableBody.innerHTML = "";

  if (!accounts || accounts.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="8" class="text-center">暂无账号数据</td></tr>'; // Updated colspan to 8
    return;
  }

  accounts.forEach((account) => {
    const row = document.createElement("tr");
    
    // 获取订阅实例信息
    let instanceDisplay = '未关联';
    if (account.subscription_instance) {
        instanceDisplay = `${account.subscription_instance.name} ${account.subscription_instance.is_active ? '' : '(已禁用)'}`;
    }
    
    // 获取健康状态信息
    let healthStatusBadge = '';
    let healthStatusText = '未知';
    if (account.health_status === 'healthy') {
        healthStatusBadge = 'bg-success';
        healthStatusText = '健康';
    } else if (account.health_status === 'unhealthy') {
        healthStatusBadge = 'bg-danger';
        healthStatusText = '异常';
    } else if (account.health_status === 'warning') {
        healthStatusBadge = 'bg-warning';
        healthStatusText = '警告';
    } else if (account.health_status === 'checking') {
        healthStatusBadge = 'bg-info';
        healthStatusText = '检查中';
    } else if (account.health_status === 'disabled') {
        healthStatusBadge = 'bg-secondary';
        healthStatusText = '已禁用';
    } else {
        healthStatusBadge = 'bg-secondary';
        healthStatusText = '未检查';
    }
    
    // 健康状态详情
    let healthTooltip = '';
    if (account.health_message) {
        healthTooltip = `title="${account.health_message}" data-bs-toggle="tooltip"`;
    }

            row.innerHTML = `
      <td>${account.id}</td>
      <td>${account.username}</td>
      <td><span class="badge ${
        account.is_active ? "bg-success" : "bg-secondary"
      }">${account.is_active ? "正常" : "禁用"}</span></td>
      <td>
        <span class="badge ${healthStatusBadge}" ${healthTooltip}>
          ${healthStatusText}
        </span>
        ${account.error_count > 0 ? `<small class="text-danger d-block">错误: ${account.error_count}</small>` : ''}
        ${account.last_check_ago ? `<small class="text-muted d-block">${account.last_check_ago}</small>` : ''}
      </td>
      <td class="${(account.max_devices - account.current_devices) <= 2 ? 'text-danger fw-bold' : ''}">${account.current_devices || 0}</td>
      <td>${account.max_devices || 0}</td>
      <td>${instanceDisplay}</td>
      <td>
        <div class="btn-group">
          <button class="btn btn-sm btn-success view-account-detail" data-id="${
            account.id
          }">
            <i class="bi bi-eye"></i> 查看
          </button>
          <button class="btn btn-sm btn-primary edit-account" data-id="${
            account.id
          }">
            <i class="bi bi-pencil"></i> 编辑
                    </button>
          <button class="btn btn-sm btn-warning toggle-status" data-id="${
            account.id
          }" data-status="${account.is_active}">
            ${
              account.is_active
                ? '<i class="bi bi-pause-fill"></i> 禁用'
                : '<i class="bi bi-play-fill"></i> 启用'
            }
          </button>
          <button class="btn btn-sm btn-danger delete-account" data-id="${
            account.id
          }">
            <i class="bi bi-trash"></i> 删除
          </button>
        </div>
                </td>
            `;
            
    tableBody.appendChild(row);

    // 绑定行事件
    row.querySelector(".view-account-detail").addEventListener("click", function () {
      viewAdspowerAccountDetail(account.id);
    });

    row.querySelector(".edit-account").addEventListener("click", function () {
      loadAccountForEdit(account.id);
    });

    row.querySelector(".toggle-status").addEventListener("click", function () {
      toggleAccountStatus(account.id, account.is_active);
    });

    row.querySelector(".delete-account").addEventListener("click", function () {
      deleteAccount(account.id);
    });
  });
}

// 查看AdsPower账号详情
function viewAdspowerAccountDetail(accountId) {
  const token = localStorage.getItem("token");
  
  // 显示模态框并加载状态
  const modal = new bootstrap.Modal(document.getElementById("viewAdspowerAccountModal"));
  modal.show();
  
  // 显示加载状态
  document.getElementById("adspower-account-detail-loading").style.display = "block";
  document.getElementById("adspower-account-detail-content").style.display = "none";
  
  // 获取账号详情
  fetch(`/api/admin/accounts/adspower/${accountId}`, {
    method: "GET",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (!response.ok) throw new Error("获取账号详情失败");
      return response.json();
    })
    .then((data) => {
      if (!data) return;
      if (data.success && data.data) {
        // 隐藏加载状态，显示内容
        document.getElementById("adspower-account-detail-loading").style.display = "none";
        document.getElementById("adspower-account-detail-content").style.display = "block";
        
        const account = data.data;
        
        // 填充基本信息
        safeSetTextContent("adspower-detail-id", account.id || '-');
        safeSetTextContent("adspower-detail-username", account.username || '-');
        
        // 账号状态
        const statusElement = document.getElementById("adspower-detail-status");
        if (statusElement) {
          statusElement.innerHTML = account.is_active 
            ? '<span class="badge bg-success">正常</span>' 
            : '<span class="badge bg-secondary">禁用</span>';
        }
        
        // 健康状态
        const healthStatusElement = document.getElementById("adspower-detail-health-status");
        if (healthStatusElement) {
          let healthBadge = 'bg-secondary';
          let healthText = '未知';
          
          switch(account.health_status) {
            case 'healthy':
              healthBadge = 'bg-success';
              healthText = '健康';
              break;
            case 'unhealthy':
              healthBadge = 'bg-danger';
              healthText = '异常';
              break;
            case 'warning':
              healthBadge = 'bg-warning';
              healthText = '警告';
              break;
            case 'checking':
              healthBadge = 'bg-info';
              healthText = '检查中';
              break;
            case 'disabled':
              healthBadge = 'bg-secondary';
              healthText = '已禁用';
              break;
          }
          
          healthStatusElement.innerHTML = `<span class="badge ${healthBadge}">${healthText}</span>`;
        }
        
        // 设备数量
        safeSetTextContent("adspower-detail-current-devices", account.current_devices || '0');
        safeSetTextContent("adspower-detail-max-devices", account.max_devices || '0');
        
        // 所属订阅实例
        let instanceText = '未关联';
        if (account.subscription_instance) {
          instanceText = `${account.subscription_instance.name}`;
          if (!account.subscription_instance.is_active) {
            instanceText += ' (已禁用)';
          }
        }
        safeSetTextContent("adspower-detail-instance", instanceText);
        
        // 时间信息
        safeSetTextContent("adspower-detail-created-at", 
          account.created_at ? formatDateTime(account.created_at) : '-');
        
        // 健康状态详情
        if (account.health_message) {
          document.getElementById("adspower-detail-health-message-row").style.display = "block";
          safeSetTextContent("adspower-detail-health-message", account.health_message);
        } else {
          document.getElementById("adspower-detail-health-message-row").style.display = "none";
        }
        
        // 描述信息
        if (account.description) {
          document.getElementById("adspower-detail-description-row").style.display = "block";
          safeSetTextContent("adspower-detail-description", account.description);
        } else {
          document.getElementById("adspower-detail-description-row").style.display = "none";
          safeSetTextContent("adspower-detail-description", "无描述");
        }
        
        // 关联设备列表
        const devicesTableBody = document.getElementById("adspower-detail-devices-table");
        const deviceCount = document.getElementById("adspower-detail-device-count");
        
        if (account.devices && account.devices.length > 0) {
          deviceCount.textContent = account.devices.length;
          devicesTableBody.innerHTML = '';
          
          account.devices.forEach(device => {
            const row = document.createElement("tr");
            row.innerHTML = `
              <td>${device.id}</td>
              <td>${device.device_name || '-'}</td>
              <td>${device.device_type || '-'}</td>
              <td>${device.user ? device.user.email : '-'}</td>
            `;
            devicesTableBody.appendChild(row);
          });
        } else {
          deviceCount.textContent = '0';
          devicesTableBody.innerHTML = '<tr><td colspan="4" class="text-center">暂无关联设备</td></tr>';
        }
        
        // 重置Cookies显示状态
        document.getElementById("adspower-detail-cookies-content").style.display = "none";
        document.getElementById("adspower-detail-cookies-placeholder").style.display = "block";
        
        // 绑定查看Cookies按钮事件
        const viewCookiesBtn = document.getElementById("adspower-detail-view-cookies-btn");
        if (viewCookiesBtn) {
          // 移除旧的事件监听器（如果有）
          viewCookiesBtn.replaceWith(viewCookiesBtn.cloneNode(true));
          const newViewCookiesBtn = document.getElementById("adspower-detail-view-cookies-btn");
          
          newViewCookiesBtn.addEventListener("click", function() {
            loadAdspowerAccountCookies(accountId);
          });
        }
      } else {
        throw new Error(data.message || "获取账号详情失败");
      }
    })
    .catch((error) => {
      console.error("Error fetching account detail:", error);
      document.getElementById("adspower-account-detail-loading").style.display = "none";
      document.getElementById("adspower-account-detail-content").style.display = "block";
      document.getElementById("adspower-account-detail-content").innerHTML = 
        `<div class="alert alert-danger">加载账号详情失败: ${error.message}</div>`;
      showToast(`加载账号详情失败: ${error.message}`, "danger");
    });
}

// 在详情模态框中加载Cookies
function loadAdspowerAccountCookies(accountId) {
  const token = localStorage.getItem("token");
  const viewCookiesBtn = document.getElementById("adspower-detail-view-cookies-btn");
  
  // 设置按钮加载状态
  setButtonLoading(viewCookiesBtn, true, '<span class="spinner-border spinner-border-sm" role="status"></span> 加载中...');
  
  fetch(`/api/admin/accounts/adspower/${accountId}/cookies`, {
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      return response.json();
    })
    .then((data) => {
      if (!data) return;
      
      // 恢复按钮状态
      setButtonLoading(viewCookiesBtn, false);
      
      if (data.success && data.data && data.data.cookies) {
        // 显示Cookies内容
        document.getElementById("adspower-detail-cookies-json").textContent = JSON.stringify(
          data.data.cookies,
          null,
          2
        );
        document.getElementById("adspower-detail-cookies-placeholder").style.display = "none";
        document.getElementById("adspower-detail-cookies-content").style.display = "block";
        
        // 更改按钮文本为"隐藏Cookies"
        viewCookiesBtn.innerHTML = '<i class="bi bi-eye-slash"></i> 隐藏Cookies';
        
        // 重新绑定事件为隐藏功能
        viewCookiesBtn.replaceWith(viewCookiesBtn.cloneNode(true));
        const newBtn = document.getElementById("adspower-detail-view-cookies-btn");
        newBtn.addEventListener("click", function() {
          // 隐藏Cookies
          document.getElementById("adspower-detail-cookies-placeholder").style.display = "block";
          document.getElementById("adspower-detail-cookies-content").style.display = "none";
          newBtn.innerHTML = '<i class="bi bi-eye"></i> 查看Cookies';
          
          // 重新绑定为显示功能
          newBtn.replaceWith(newBtn.cloneNode(true));
          const showBtn = document.getElementById("adspower-detail-view-cookies-btn");
          showBtn.addEventListener("click", function() {
            loadAdspowerAccountCookies(accountId);
          });
        });
      } else {
        showToast("没有找到该账号的Cookies信息", "info");
        document.getElementById("adspower-detail-cookies-json").textContent = "无Cookies信息";
        document.getElementById("adspower-detail-cookies-placeholder").style.display = "none";
        document.getElementById("adspower-detail-cookies-content").style.display = "block";
      }
    })
    .catch((error) => {
      console.error("Error fetching cookies:", error);
      setButtonLoading(viewCookiesBtn, false);
      showToast("获取Cookies失败，请稍后再试", "danger");
    });
}


function toggleAccountStatus(accountId, isActive) {
  if (!confirm(`确定要${isActive ? "禁用" : "启用"}此账号吗？`)) {
            return;
        }
        
  const token = localStorage.getItem("token");
  fetch(`/api/admin/accounts/adspower/${accountId}/toggle-status`, {
    method: "POST",
            headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ is_active: !isActive }),
  })
    .then((response) => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then((data) => {
      if (!data) return; // Handled by unauthorized check
      if (data.success) {
        // alert(data.message || "账号状态已更新");
        showToast(data.message || "账号状态已更新", "success");
        loadAdspowerAccounts(); // 刷新账号列表
      } else {
        // alert(data.message || "更新账号状态失败");
        showToast(data.message || "更新账号状态失败", "danger");
      }
    })
    .catch((error) => {
      console.error("Error toggling account status:", error);
      // alert("更新账号状态失败，请稍后再试");
      showToast("更新账号状态失败，请稍后再试", "danger");
    });
}

function deleteAccount(accountId) {
  if (!confirm("确定要删除此账号吗？这将同时删除所有关联的Profile和设备。")) return;
  const token = localStorage.getItem("token");
  fetch(`/api/admin/accounts/adspower/${accountId}`, {
    method: "DELETE",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then((data) => {
      if (!data) return; // Handled by unauthorized check
      if (data.success) {
        // alert(data.message || "账号已成功删除");
        showToast(data.message || "账号已成功删除", "success");
        loadAdspowerAccounts();
      } else {
        // alert(data.message || "删除账号失败");
        showToast(data.message || "删除账号失败", "danger");
      }
    })
    .catch((error) => {
      console.error("Error deleting account:", error);
      // alert("删除账号失败，请稍后再试");
      showToast("删除账号失败，请稍后再试", "danger");
    });
}

// refreshDevicesCount 函数已移除

/************** 初始化事件监听 **************/
function initEventListeners() {
  document.getElementById("logout-btn").addEventListener("click", async function () {
    const authType = localStorage.getItem('auth_type');
    const token = localStorage.getItem('token');
    
    if (authType === 'sso' && token) {
        // SSO登录，需要调用后端接口获取Keycloak登出URL
        try {
            const response = await fetch('/api/auth/keycloak/logout', {
                headers: {
                    'Authorization': 'Bearer ' + token
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                
                // 先获取id_token，再清除本地存储
                const idToken = localStorage.getItem('id_token');
                
                // 清除本地存储
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                localStorage.removeItem('auth_type');
                localStorage.removeItem('id_token');
                localStorage.removeItem('lastLoginEmail');
                
                // 重定向到Keycloak登出URL
                if (data.data && data.data.keycloak_logout_url) {
                    // 构建完整的登出URL，包含post_logout_redirect_uri
                    const logoutUrl = new URL(data.data.keycloak_logout_url);
                    
                    if (idToken) {
                        logoutUrl.searchParams.append('id_token_hint', idToken);
                    }
                    
                    // 设置登出后的重定向URL（返回到首页）
                    logoutUrl.searchParams.append('post_logout_redirect_uri', window.location.origin);
                    
                    window.location.href = logoutUrl.toString();
                } else {
                    // 如果无法获取Keycloak登出URL，至少返回首页
                    window.location.href = '/';
                }
            } else {
                // 请求失败，仍然清除本地存储并返回首页
                console.error('登出请求失败');
                localStorage.clear();
                window.location.href = '/';
            }
        } catch (error) {
            console.error('登出过程出错:', error);
            localStorage.clear();
            window.location.href = '/';
        }
    } else {
        // 本地登录或无法确定登录类型，执行简单登出
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('auth_type');
        localStorage.removeItem('lastLoginEmail');
        window.location.href = '/';
    }
  });
  document
    .getElementById("refresh-devices-btn")
    .addEventListener("click", function () {
    const btn = this;
    // 使用辅助函数
    setButtonLoading(btn, true, '<i class="bi bi-arrow-clockwise"></i> 刷新中...');
      loadDevices(1, currentDeviceSearchTerm); // 刷新时保持搜索词
    setTimeout(() => {
      // 恢复按钮
      setButtonLoading(btn, false);
    }, 1000); // 延迟确保加载函数已启动
  });

  // 侧边栏导航点击事件
  document.querySelectorAll(".nav-link").forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();
      const section = e.target.getAttribute("href").substring(1);
      showSection(section);
    });
  });

  // 加载订阅类型选择框
  loadSubscriptionTypeOptions();
  
  // 为"添加新账号"按钮添加事件监听器 (AdsPower账号管理)
  const addAdspowerAccountBtn = document.getElementById("add-adspower-account-btn");
  if (addAdspowerAccountBtn) {
    addAdspowerAccountBtn.addEventListener("click", function() {
      // 清理表单（如果需要）
      const accountForm = document.getElementById("account-form");
      if (accountForm) {
        accountForm.reset();
        accountForm.classList.remove('was-validated');
        // 清除自定义 cookies 验证状态
        const cookiesInput = document.getElementById("cookies");
        if (cookiesInput) {
            cookiesInput.setCustomValidity(""); 
            cookiesInput.classList.remove('is-invalid');
        }
      }
      // 加载订阅实例选项
      loadSubscriptionInstancesForSelect('subscription_instance');
      // 显示模态框
      const addAccountModal = new bootstrap.Modal(document.getElementById('addAccountModal'));
      addAccountModal.show();
    });
  }
  
  // 添加账号表单提交事件
  document.getElementById("save-account-btn").addEventListener("click", function() {
    const form = document.getElementById("account-form");
    const token = localStorage.getItem("token");

    // 移除之前的 was-validated 状态，以防用户修正后再次点击
    form.classList.remove('was-validated');
    // 清除自定义 cookies 验证状态
    const cookiesInput = document.getElementById("cookies");
    cookiesInput.setCustomValidity(""); 
    cookiesInput.classList.remove('is-invalid');

    // 检查 Cookies JSON 格式 (基础检查)
    let cookiesValue = cookiesInput.value.trim();
    let cookiesData = null;
    if (cookiesValue) {
        try {
            cookiesData = JSON.parse(cookiesValue);
            if (!Array.isArray(cookiesData)) {
                throw new Error("Cookies 必须是 JSON 数组格式。");
            }
            // Optional: Add more checks for required fields within cookies array items
            cookiesInput.setCustomValidity(""); // Clear validity if parsed
        } catch (e) {
            cookiesInput.setCustomValidity("请输入有效的 JSON 数组格式的 Cookies。错误: " + e.message);
            document.getElementById('cookies-invalid-feedback').textContent = "请输入有效的 JSON 数组格式的 Cookies。错误: " + e.message;
            // Mark the field as invalid manually for Bootstrap styling
            cookiesInput.classList.add('is-invalid'); 
        }
    }

    // 执行 Bootstrap 验证
    if (!form.checkValidity()) {
      form.classList.add('was-validated');
      return; // 阻止提交
    }

    // 表单验证通过，继续提交
    const formData = {
      username: document.getElementById("username").value,
      password: document.getElementById("password").value,
      totp_secret: document.getElementById("totp_secret").value,
      max_devices: parseInt(document.getElementById("max_devices").value),
      subscription_instance_id: document.getElementById("subscription_instance").value || null, // 订阅实例ID
      cookies: cookiesData // Send parsed data (or null if empty/invalid was handled)
    };
    
    fetch("/api/admin/accounts/adspower", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(formData)
    })
      .then(response => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        console.log("服务器响应状态:", response.status);
        return response.json();
      })
      .then(data => {
        if (!data) return; // Handled by unauthorized check
        if (data.success) {
          showToast("添加账号成功", "success");
          
          // 重置表单
          document.getElementById("account-form").reset();
          
          // 关闭模态框
          const modal = bootstrap.Modal.getInstance(document.getElementById("addAccountModal"));
          modal.hide();
          
          // 清理可能残留的模态框遮罩
          setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
              if (backdrop && !document.querySelector('.modal.show')) {
                backdrop.remove();
              }
            });
            // 确保body的modal-open类被正确处理
            if (!document.querySelector('.modal.show')) {
              document.body.classList.remove('modal-open');
              document.body.style.removeProperty('overflow');
              document.body.style.removeProperty('padding-right');
            }
          }, 300);
          
          // 重新加载列表
          loadAdspowerAccounts();
        } else {
          showToast(`添加账号失败: ${data.message || '未知错误'}`, "danger");
        }
      })
      .catch(error => {
        console.error("添加账号失败:", error);
        showToast(`添加账号时出错: ${error.message}`, "danger");
      });
  });

  // 搜索功能事件监听
  // 设备搜索
  const deviceSearchInput = document.getElementById('device-search');
  const deviceSearchBtn = document.getElementById('device-search-btn');
  const deviceClearBtn = document.getElementById('device-clear-btn');
  
  if (deviceSearchInput && deviceSearchBtn && deviceClearBtn) {
    // 搜索按钮点击事件
    deviceSearchBtn.addEventListener('click', () => {
      const searchTerm = deviceSearchInput.value.trim();
      loadDevices(1, searchTerm); // 从第一页开始搜索
      deviceClearBtn.style.display = searchTerm ? 'inline-block' : 'none';
    });
    
    // 输入框回车搜索
    deviceSearchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        const searchTerm = deviceSearchInput.value.trim();
        loadDevices(1, searchTerm);
        deviceClearBtn.style.display = searchTerm ? 'inline-block' : 'none';
      }
    });
    
    // 输入时只显示/隐藏清除按钮，不触发搜索
    deviceSearchInput.addEventListener('input', () => {
      const searchTerm = deviceSearchInput.value.trim();
      deviceClearBtn.style.display = searchTerm ? 'inline-block' : 'none';
    });
    
    // 清除按钮点击事件
    deviceClearBtn.addEventListener('click', () => {
      deviceSearchInput.value = '';
      deviceClearBtn.style.display = 'none';
      loadDevices(1, ''); // 加载所有设备
    });
  }

  // 用户搜索（使用后端搜索）
  const userSearchInput = document.getElementById('user-search');
  const userSearchBtn = document.getElementById('user-search-btn');
  const userClearBtn = document.getElementById('user-clear-btn');
  if (userSearchInput && userSearchBtn && userClearBtn) {
    // 搜索按钮点击
    userSearchBtn.addEventListener('click', () => {
      const searchQuery = userSearchInput.value.trim();
      loadUsersWithSearch(searchQuery, 1); // 搜索时重置到第一页
      userClearBtn.style.display = searchQuery ? 'block' : 'none';
    });
    
    // 输入框回车
    userSearchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        const searchQuery = userSearchInput.value.trim();
        loadUsersWithSearch(searchQuery, 1); // 搜索时重置到第一页
        userClearBtn.style.display = searchQuery ? 'block' : 'none';
      }
    });
    
    // 清除按钮
    userClearBtn.addEventListener('click', () => {
      userSearchInput.value = '';
      userClearBtn.style.display = 'none';
      loadUsers(1); // 重置到第一页
    });
  }
  
  // 订阅搜索
  const subscriptionSearchInput = document.getElementById('subscription-search');
  const subscriptionSearchBtn = document.getElementById('subscription-search-btn');
  const subscriptionClearBtn = document.getElementById('subscription-clear-btn');
  if (subscriptionSearchInput && subscriptionSearchBtn && subscriptionClearBtn) {
    subscriptionSearchBtn.addEventListener('click', () => filterTable('subscription-search', 'subscription-clear-btn', 'subscriptions-table-body', allSubscriptions, updateSubscriptionsTable, ['user_email', 'subscription_name', 'status_display', 'status']));
    subscriptionSearchInput.addEventListener('input', () => filterTable('subscription-search', 'subscription-clear-btn', 'subscriptions-table-body', allSubscriptions, updateSubscriptionsTable, ['user_email', 'subscription_name', 'status_display', 'status']));
    subscriptionClearBtn.addEventListener('click', () => {
      subscriptionSearchInput.value = '';
      filterTable('subscription-search', 'subscription-clear-btn', 'subscriptions-table-body', allSubscriptions, updateSubscriptionsTable, ['user_email', 'subscription_name', 'status_display', 'status']);
    });
  }

  // 订阅实例搜索
  const instanceSearchInput = document.getElementById('instance-search');
  const instanceSearchBtn = document.getElementById('instance-search-btn');
  const instanceClearBtn = document.getElementById('instance-clear-btn');
  if (instanceSearchInput && instanceSearchBtn && instanceClearBtn) {
    instanceSearchBtn.addEventListener('click', () => filterTable('instance-search', 'instance-clear-btn', 'subscription-instances-table-body', allSubscriptionInstances, updateSubscriptionInstancesTable, ['id', 'name', 'subscription_type_name', 'status_display']));
    instanceSearchInput.addEventListener('input', () => filterTable('instance-search', 'instance-clear-btn', 'subscription-instances-table-body', allSubscriptionInstances, updateSubscriptionInstancesTable, ['id', 'name', 'subscription_type_name', 'status_display']));
    instanceClearBtn.addEventListener('click', () => {
      instanceSearchInput.value = '';
      filterTable('instance-search', 'instance-clear-btn', 'subscription-instances-table-body', allSubscriptionInstances, updateSubscriptionInstancesTable, ['id', 'name', 'subscription_type_name', 'status_display']);
    });
  }

  // 刷新按钮事件监听
  const refreshUsersBtn = document.getElementById('refresh-users-btn');
  if (refreshUsersBtn) {
    refreshUsersBtn.addEventListener('click', () => loadUsers(1));
  }

  const refreshSubscriptionsBtn = document.getElementById('refresh-subscriptions-btn');
  if (refreshSubscriptionsBtn) {
    refreshSubscriptionsBtn.addEventListener('click', loadSubscriptions);
  }
  
  // 新增：仪表板相关事件监听器
  // 刷新仪表板按钮
  const refreshDashboardBtn = document.getElementById('refresh-dashboard-btn');
  if (refreshDashboardBtn) {
    refreshDashboardBtn.addEventListener('click', function() {
      setButtonLoading(this, true, '<i class="bi bi-arrow-clockwise"></i> 刷新中...');
      loadDashboardStats();
      setTimeout(() => {
        setButtonLoading(this, false);
        showToast('仪表板数据已刷新', 'success');
      }, 1000);
    });
  }
  
  // 时间范围选择
  document.querySelectorAll('[data-range]').forEach(item => {
    item.addEventListener('click', function(e) {
      e.preventDefault();
      dashboardTimeRange = this.getAttribute('data-range');
      
      // 更新按钮文本
      const dropdownBtn = this.closest('.dropdown').querySelector('.dropdown-toggle');
      if (dropdownBtn) {
        dropdownBtn.innerHTML = `<i class="bi bi-calendar3"></i> ${this.textContent}`;
      }
      
      // 重新加载图表数据
      updateDashboardCharts();
      showToast(`已切换到${this.textContent}视图`, 'info');
    });
  });
  
  // 图表切换按钮
  document.querySelectorAll('[data-chart]').forEach(btn => {
    btn.addEventListener('click', function() {
      const chartType = this.getAttribute('data-chart');
      
      // 更新按钮状态
      this.parentElement.querySelectorAll('.btn').forEach(b => b.classList.remove('active'));
      this.classList.add('active');
      
      // 切换图表显示的数据集和Y轴
      if (growthChart && growthChart.data.datasets) {
        growthChart.data.datasets.forEach((dataset, index) => {
          if (chartType === 'users' && index === 0) {
            dataset.hidden = false;
            // 显示用户Y轴
            growthChart.options.scales['y-users'].display = true;
            growthChart.options.scales['y-revenue'].display = false;
            growthChart.options.scales['y-devices'].display = false;
          } else if (chartType === 'revenue' && index === 1) {
            dataset.hidden = false;
            // 显示收入Y轴
            growthChart.options.scales['y-users'].display = false;
            growthChart.options.scales['y-revenue'].display = true;
            growthChart.options.scales['y-devices'].display = false;
          } else if (chartType === 'devices' && index === 2) {
            dataset.hidden = false;
            // 显示设备Y轴
            growthChart.options.scales['y-users'].display = false;
            growthChart.options.scales['y-revenue'].display = false;
            growthChart.options.scales['y-devices'].display = true;
          } else {
            dataset.hidden = true;
          }
        });
        growthChart.update();
      }
    });
  });
  
  // 分配订阅相关的事件监听器
  // 订阅类型改变时加载对应的实例
  const assignSubscriptionType = document.getElementById('assign-subscription-type');
  if (assignSubscriptionType) {
    assignSubscriptionType.addEventListener('change', async function() {
      const typeId = this.value;
      const instanceSelect = document.getElementById('assign-subscription-instance');
      const daysInput = document.getElementById('assign-subscription-days');
      const priceInput = document.getElementById('assign-subscription-price');
      
      if (!typeId) {
        instanceSelect.innerHTML = '<option value="">请先选择订阅类型...</option>';
        return;
      }
      
      // 更新默认天数和价格
      const selectedOption = this.options[this.selectedIndex];
      daysInput.value = selectedOption.dataset.days || 30;
      priceInput.value = selectedOption.dataset.price || 0;
      
      // 加载该类型的实例
      const token = localStorage.getItem("token");
      
      try {
        const response = await fetch(`/api/admin/subscription-types/${typeId}/instances`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (!response.ok) throw new Error('获取订阅实例失败');
        
        const result = await response.json();
        
        if (result.success && result.data && result.data.instances) {
          instanceSelect.innerHTML = '<option value="">请选择订阅实例...</option>';
          
          result.data.instances.forEach(instance => {
            if (instance.is_active) {
              const option = document.createElement('option');
              option.value = instance.id;
              const availableSlots = instance.capacity - (instance.current_users || 0);
              option.textContent = `${instance.name} (剩余容量: ${availableSlots}/${instance.capacity})`;
              option.disabled = availableSlots <= 0;
              instanceSelect.appendChild(option);
            }
          });
          
          if (instanceSelect.options.length === 1) {
            instanceSelect.innerHTML = '<option value="">该类型暂无可用实例</option>';
          }
        }
      } catch (error) {
        showToast(`加载订阅实例失败: ${error.message}`, 'danger');
        console.error('Error loading subscription instances:', error);
      }
    });
  }

  // 确认分配订阅
  const confirmAssignSubscription = document.getElementById('confirmAssignSubscription');
  if (confirmAssignSubscription) {
    confirmAssignSubscription.addEventListener('click', async function() {
      const userId = document.getElementById('assign-subscription-user-id').textContent;
      const typeId = document.getElementById('assign-subscription-type').value;
      const instanceId = document.getElementById('assign-subscription-instance').value;
      const days = document.getElementById('assign-subscription-days').value;
      const price = document.getElementById('assign-subscription-price').value;
      const remark = document.getElementById('assign-subscription-remark').value;
      
      if (!typeId || !instanceId || !days) {
        showToast('请填写所有必填项', 'warning');
        return;
      }
      
      const token = localStorage.getItem("token");
      
      try {
        const response = await fetch(`/api/admin/users/${userId}/assign-subscription`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            subscription_type_id: parseInt(typeId),
            subscription_instance_id: parseInt(instanceId),
            days: parseInt(days),
            price: parseFloat(price),
            remark: remark
          })
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
          showToast('订阅分配成功', 'success');
          // 关闭模态框
          bootstrap.Modal.getInstance(document.getElementById('assignSubscriptionModal')).hide();
          // 刷新用户列表
          loadUsers();
        } else {
          showToast(result.message || '分配订阅失败', 'danger');
        }
      } catch (error) {
        showToast(`分配订阅时发生错误: ${error.message}`, 'danger');
        console.error('Error assigning subscription:', error);
      }
    });
  }
}

// --- 新增：通用表格过滤函数 ---
function filterTable(inputId, clearButtonId, tableBodyId, dataArray, updateFunction, searchableFields) {
  const inputElement = document.getElementById(inputId);
  const clearButton = document.getElementById(clearButtonId);
  const searchTerm = inputElement.value.toLowerCase().trim();

  // 控制清除按钮的显示/隐藏
  clearButton.style.display = searchTerm ? 'inline-block' : 'none';

  if (!dataArray) return; // 如果数据还没加载好

  const filteredData = dataArray.filter(item => {
    if (!searchTerm) return true; // 如果搜索词为空，则显示所有项

    return searchableFields.some(field => {
      // 处理嵌套属性，例如 'user.email'
      const value = field.split('.').reduce((o, k) => (o || {})[k], item);
      return value && String(value).toLowerCase().includes(searchTerm);
    });
  });

  updateFunction(filteredData); // 使用过滤后的数据更新表格
}

function deleteDevice(deviceId, buttonElement) {
  const token = localStorage.getItem("token");
  const originalButtonContent = buttonElement.innerHTML; // Store original content
  // 使用辅助函数
  setButtonLoading(buttonElement, true, '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...');

  fetch(`/api/admin/devices/${deviceId}`, {
    method: "DELETE",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
        // Always try to parse JSON, but check status code first
        if (!response.ok) {
             // Try to get error message from JSON body if possible
             return response.json().then(errData => {
                 throw new Error(errData.message || `HTTP error! status: ${response.status}`);
             }).catch(() => {
                 // Fallback if parsing error JSON fails
                 throw new Error(`HTTP error! status: ${response.status}`);
             });
        }
        return response.json();
    })
    .then((data) => {
      if (data.success) {
        showToast(data.message || "设备已成功删除", "success");
        // 恢复按钮状态
        setButtonLoading(buttonElement, false);
        loadDevices(currentDevicesPage); // 刷新当前页设备列表
        
        // 如果用户详情模态框是打开的，重新获取并显示用户详情
        const userDetailsModal = document.getElementById('userDetailsModal');
        if (userDetailsModal && userDetailsModal.classList.contains('show')) {
          const currentUserId = document.getElementById('user-detail-id').textContent;
          if (currentUserId && currentUserId !== 'undefined' && currentUserId !== '') {
            // 使用viewUserDetails函数重新加载用户详情
            viewUserDetails(parseInt(currentUserId));
          }
        }
      } else {
        // 恢复按钮
        setButtonLoading(buttonElement, false);
        showToast(data.message || "删除设备失败", "danger");
      }
    })
    .catch((error) => {
      // 恢复按钮
      setButtonLoading(buttonElement, false);
      console.error("Error deleting device:", error);
      showToast("删除设备失败：" + error.message, "danger");
    });
}

function showDeviceDetails(data) {
  // 检查传入的数据结构是否是 { success: true, data: {...} }
  const device = data.success ? data.data : data; 

  // 安全访问数据，提供默认值
  safeSetTextContent("device-detail-id", device.id || "未知");
  safeSetTextContent("device-detail-name", device.device_name || "未知设备");
  safeSetTextContent("device-detail-ip", device.device_ip || "未知"); // Changed from device.ip_address
  safeSetTextContent("device-detail-type", device.device_type || "未知");
  safeSetTextContent("device-detail-status", device.is_active ? "活跃" : "离线");
  safeSetTextContent("device-detail-created-at", device.created_at ? formatDateTime(device.created_at) : "未知");
  
  // Access nested user information
  safeSetTextContent("device-detail-user", (device.user && device.user.email) ? device.user.email : "未知用户"); 
  
  // Access nested AdsPower account information
  safeSetTextContent("device-detail-adspower", (device.adspower_account && device.adspower_account.username) ? device.adspower_account.username : "未分配"); 

  // 显示模态框
  const deviceModal = new bootstrap.Modal(document.getElementById("deviceDetailsModal"));
  deviceModal.show();
}
function showUserDetails(user) {
  // 获取用户详细信息
  const token = localStorage.getItem("token");
  fetch(`/api/admin/users/${user.id}/details`, {
    method: "GET",
    headers: { Authorization: `Bearer ${token}` },
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (!response.ok) throw new Error("获取用户详情失败");
      return response.json();
    })
    .then((data) => {
      if (!data) return; // Handled by unauthorized check
      if (!data.success) {
        throw new Error(data.message || "获取用户详情失败");
      }
      
      const userDetails = data.data.user;
      
      // 填充基本信息
      document.getElementById("user-detail-id").textContent = userDetails.id || "未知";
      document.getElementById("user-detail-email").textContent = userDetails.email || "未知";
      document.getElementById("user-detail-is-admin").innerHTML = userDetails.is_admin ? 
        '<span class="badge bg-primary">是</span>' : 
        '<span class="badge bg-light text-dark border">否</span>';
      document.getElementById("user-detail-is-active").innerHTML = userDetails.is_active ? 
        '<span class="badge bg-success">已激活</span>' : 
        '<span class="badge bg-secondary">未激活</span>';
      // document.getElementById("user-detail-is-email-verified").innerHTML = userDetails.is_email_verified ? 
      //   '<span class="badge bg-success">已验证</span>' : 
      //   '<span class="badge bg-warning">未验证</span>';
      document.getElementById("user-detail-last-login").textContent = userDetails.last_login ? 
        formatDateTime(userDetails.last_login) : "从未登录";
      document.getElementById("user-detail-created-at").textContent = userDetails.created_at ? 
        formatDateTime(userDetails.created_at) : "未知";
      
      // 填充订阅信息
      if (data.data.subscriptions && data.data.subscriptions.length > 0) {
        const currentSubscription = data.data.subscriptions[0]; // 使用第一个订阅
        
        document.getElementById("user-detail-subscription-table").style.display = "table";
        document.getElementById("user-detail-no-subscription").style.display = "none";
        
        document.getElementById("user-detail-subscription-plan").textContent = 
          currentSubscription.subscription_name || "未知";
        
        const statusElement = document.getElementById("user-detail-subscription-status");
        const endDate = new Date(currentSubscription.end_date);
        const isExpired = new Date() > endDate;
        const statusText = isExpired ? '已过期' : '活跃';
        const statusClass = isExpired ? 'danger' : 'success';
        statusElement.innerHTML = `<span class="badge bg-${statusClass}">${statusText}</span>`;
        
        document.getElementById("user-detail-subscription-start-date").textContent = 
          currentSubscription.start_date ? formatDateTime(currentSubscription.start_date).split(' ')[0] : "未知";
        document.getElementById("user-detail-subscription-end-date").textContent = 
          currentSubscription.end_date ? formatDateTime(currentSubscription.end_date).split(' ')[0] : "未知";
        document.getElementById("user-detail-subscription-max-devices").textContent = 
          currentSubscription.max_devices || "未限制";
      } else {
        document.getElementById("user-detail-subscription-table").style.display = "none";
        document.getElementById("user-detail-no-subscription").style.display = "block";
      }
      
      // 填充设备列表
      const devicesTableBody = document.getElementById("user-detail-devices-table-body");
      devicesTableBody.innerHTML = "";
      document.getElementById("user-detail-device-count").textContent = data.data.devices ? data.data.devices.length : 0;
      
      if (data.data.devices && data.data.devices.length > 0) {
        data.data.devices.forEach(device => {
          const row = document.createElement("tr");
          row.innerHTML = `
            <td>${device.id}</td>
            <td>${device.name || device.device_name || "未知设备"}</td>
            <td>${device.type || device.device_type || "未知"}</td>
            <td>${device.ip_address || device.device_ip || "未知"}</td>
            <td>
              <button class="btn btn-sm btn-danger delete-device-btn" data-device-id="${device.id}" data-device-name="${device.name || device.device_name || '未知设备'}" title="删除设备">
                <i class="bi bi-trash"></i>
              </button>
            </td>
          `;
          devicesTableBody.appendChild(row);
          
          // 绑定删除设备按钮事件
          row.querySelector(".delete-device-btn").addEventListener("click", (e) => {
            const deviceId = e.currentTarget.getAttribute("data-device-id");
            const deviceName = e.currentTarget.getAttribute("data-device-name");
            if (confirm(`确定要删除设备 "${deviceName}" 吗？`)) {
              deleteDevice(deviceId, e.currentTarget);
            }
          });
        });
      } else {
        devicesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">该用户没有设备</td></tr>';
      }
      
      // 显示模态框
      const userModal = new bootstrap.Modal(document.getElementById("userDetailsModal"));
      userModal.show();
    })
    .catch((error) => {
      console.error("获取用户详情失败:", error);
      showToast(`获取用户详情失败: ${error.message}`, "danger");
    });
}
function toggleUserStatus(userId, enable) {
  if (!confirm(`确定要${enable ? "启用" : "禁用"}此用户吗？`)) {
    return;
  }
  
  const token = localStorage.getItem("token");
  fetch(`/api/admin/users/${userId}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ is_active: enable }),
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (response.success) {
        showToast(`用户已${enable ? "启用" : "禁用"}`, "success");
        loadUsers(); // 刷新用户列表
      } else {
        showToast(data.message || `${enable ? "启用" : "禁用"}用户失败`, "danger");
      }
    })
    .catch((error) => {
      console.error("更新用户状态时出错:", error);
      showToast(`${enable ? "启用" : "禁用"}用户失败，请稍后再试`, "danger");
    });
}
function deleteUser(userId) {
  if (!confirm("确认删除该用户？")) return;
  showToast("用户删除功能待实现", "info"); // 提醒功能未实现
}

async function loadSubscriptionTypes() { // Make it async
  const token = localStorage.getItem("token");
  const tableBodyId = "subscription-types-table-body";
  const colspan = 7; // 订阅类型表调整为7列 (移除了代码列)

  const sectionElement = document.getElementById("section-subscription-types");
  const tableBodyElement = document.getElementById(tableBodyId);

  // Only show table loading if the subscription types section is visible
  // and the table body element exists.
  if (tableBodyElement && sectionElement && sectionElement.style.display !== 'none') {
    showTableLoading(tableBodyId, true, colspan);
  }
  
  try {
    const response = await fetch("/api/admin/subscription-types", { // 修正API路径
      headers: { Authorization: `Bearer ${token}` }
    });
    if (handleUnauthorizedAdminResponse(response)) return null;
    const data = await response.json(); // Use await

    // 更新数据访问：API返回的是 data.data.subscription_types
    const typesList = data.data && data.data.subscription_types ? data.data.subscription_types : [];

    if (data.success && typesList) {
      allSubscriptionTypes = typesList; // Populate global variable

      if (tableBodyElement && sectionElement && sectionElement.style.display !== 'none') { // Update table only if visible
        showTableLoading(tableBodyId, false, colspan);
        updateSubscriptionTypesTable(allSubscriptionTypes); // Update the main table
      }
      // 调用新的填充函数，以更新所有相关的下拉框
      populateSubscriptionTypeSelects(allSubscriptionTypes);
    } else {
      allSubscriptionTypes = []; // Clear global on API failure
      if (tableBodyElement && sectionElement && sectionElement.style.display !== 'none') {
        showTableLoading(tableBodyId, false, colspan);
        document.getElementById(tableBodyId).innerHTML =
          `<tr><td colspan="${colspan}" class="text-center">${data.message || '获取订阅类型数据失败'}</td></tr>`;
      }
      // Do not throw an error here to allow openEditSubscriptionModal to show its specific toast
    }
  } catch (error) {
    console.error("获取订阅类型失败 (in loadSubscriptionTypes):", error);
    allSubscriptionTypes = []; // Clear global on error
    if (tableBodyElement && sectionElement && sectionElement.style.display !== 'none') {
      showTableLoading(tableBodyId, false, colspan);
      document.getElementById(tableBodyId).innerHTML =
        `<tr><td colspan="${colspan}" class="text-center text-danger">获取订阅类型失败: ${error.message}</td></tr>`;
    }
    throw error; // Re-throw so that if openEditSubscriptionModal awaits it, it can catch it.
  }
}

function updateSubscriptionTypesTable(types) {
  const tbody = document.getElementById("subscription-types-table-body");
  tbody.innerHTML = "";

   if (!types || types.length === 0) {
    tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无订阅类型数据</td></tr>'; // Colspan updated to 6
    return;
  }
  
  types.forEach(type => {
    const tr = document.createElement("tr");
    tr.innerHTML = `
      <td>${type.name}</td>
      <td>${type.max_devices}</td>
      <td>¥${type.price.toFixed(2)}</td>
      <td>${type.days}</td>
      <td class="text-center">${type.is_public ? '<i class="bi bi-check-circle-fill text-success"></i>' : '<i class="bi bi-x-circle-fill text-danger"></i>'}</td>
      <td>
        <button class="btn btn-sm btn-info view-type-details-btn" data-type-id="${type.id}" title="查看详情">
          <i class="bi bi-eye"></i>
        </button>
        <button class="btn btn-sm btn-primary edit-type-btn" data-type-id="${type.id}" title="编辑">
          <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-sm btn-danger delete-type-btn" data-type-id="${type.id}" data-type-name="${type.name}" title="删除">
          <i class="bi bi-trash"></i>
        </button>
      </td>
    `;
    tbody.appendChild(tr);
    
    // 添加事件监听器
    tr.querySelector(".view-type-details-btn").addEventListener("click", () => viewSubscriptionTypeDetails(type.id)); // New button
    tr.querySelector(".edit-type-btn").addEventListener("click", () => editSubscriptionType(type.id));
    tr.querySelector(".delete-type-btn").addEventListener("click", function() {
      if (confirm(`确定要删除订阅类型 "${type.name}" 吗？`)) {
        deleteSubscriptionType(type.id);
      }
    });
  });
}

async function viewSubscriptionTypeDetails(typeId) {
  const token = localStorage.getItem("token");
  const type = allSubscriptionTypes.find(t => t.id === typeId);

  if (type) {
    document.getElementById("view-type-id").textContent = type.id;
    document.getElementById("view-type-name").textContent = type.name;
    document.getElementById("view-type-max-devices").textContent = type.max_devices;
    document.getElementById("view-type-price").textContent = `¥${type.price.toFixed(2)}`;
    document.getElementById("view-type-days").textContent = type.days;
    document.getElementById("view-type-is-public").innerHTML = type.is_public ? '<i class="bi bi-check-circle-fill text-success"></i> 是' : '<i class="bi bi-x-circle-fill text-danger"></i> 否';
    document.getElementById("view-type-requirements").textContent = type.requirements || '无';
    document.getElementById("view-type-default-instance-capacity").textContent = type.default_subscription_instance_capacity || '未设置';

    const modal = new bootstrap.Modal(document.getElementById('viewSubscriptionTypeModal'));
    modal.show();

    // Load associated instances
    const instancesTableBody = document.getElementById('associated-instances-table-body');
    instancesTableBody.innerHTML = '<tr><td colspan="5" class="text-center"><span class="spinner-border spinner-border-sm"></span> 加载关联实例中...</td></tr>';

    try {
      const response = await fetch(`/api/admin/subscription-types/${typeId}/instances`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (handleUnauthorizedAdminResponse(response)) return;
      const data = await response.json();

      // Updated data access: try data.data.instances then data.instances
      const instancesList = data.data && data.data.instances ? data.data.instances : data.instances;

      if (data.success && instancesList) {
        instancesTableBody.innerHTML = ''; // Clear loading
        document.getElementById("associated-instances-count").textContent = instancesList.length; // Update count here
        if (instancesList.length > 0) {
          instancesList.forEach(instance => {
            const row = instancesTableBody.insertRow();
            row.insertCell().textContent = instance.id;
            row.insertCell().textContent = instance.name;
            row.insertCell().textContent = instance.capacity;
            row.insertCell().textContent = instance.active_users_count !== undefined ? instance.active_users_count : (instance.current_users || 'N/A');
            row.insertCell().innerHTML = instance.is_active ? '<span class="badge bg-success">活跃</span>' : '<span class="badge bg-secondary">禁用</span>';
          });
        } else {
          instancesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">没有关联的订阅实例。</td></tr>';
        }
      } else {
        instancesTableBody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">加载关联实例失败: ${data.message || '未知错误'}</td></tr>`;
        document.getElementById("associated-instances-count").textContent = 0; // Ensure count is 0 on failure
      }
    } catch (error) {
      console.error("Error fetching associated instances:", error);
      instancesTableBody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">加载关联实例时出错: ${error.message}</td></tr>`;
      document.getElementById("associated-instances-count").textContent = 0; // Ensure count is 0 on error
      showToast(`加载关联实例时出错: ${error.message}`, "danger");
    }
  } else {
    showToast('未找到订阅类型信息。', 'warning');
  }
}

function editSubscriptionType(typeId) {
  const token = localStorage.getItem("token");
  
  // 从所有订阅类型列表中获取对应的类型数据
  fetch("/api/subscription-types", {
    headers: { Authorization: `Bearer ${token}` }
  })
    .then(response => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then(data => {
      if (!data) return; // Handled by unauthorized check
      // Ensure typesList is correctly accessed from data.data.types or data.types
      const typesList = data.data && data.data.types ? data.data.types : data.types;
      
      if (data.success && typesList) {
        // 找到对应ID的订阅类型
        const type = typesList.find(t => t.id === typeId);
        
        if (type) {
          // 填充表单
          document.getElementById("edit-type-id").value = type.id;
          document.getElementById("edit-type-name").value = type.name;
          document.getElementById("edit-type-max-devices").value = type.max_devices;
          document.getElementById("edit-type-price").value = type.price;
          document.getElementById("edit-type-days").value = type.days;
          document.getElementById("edit-type-requirements").value = type.requirements || '';
          document.getElementById("edit-type-is-public").checked = type.is_public;
          document.getElementById("edit-type-default-instance-capacity").value = type.default_subscription_instance_capacity || ""; // Added for default capacity
          
          // 显示模态框
          const modal = new bootstrap.Modal(document.getElementById("editSubscriptionTypeModal"));
          modal.show();
        } else {
          // alert(`未找到ID为${typeId}的订阅类型`);
          showToast(`未找到ID为${typeId}的订阅类型`, "warning");
        }
      } else {
        // alert(`获取订阅类型列表失败: ${data.message || '未知错误'}`);
        showToast(`获取订阅类型列表失败: ${data.message || '未知错误'}`, "danger");
      }
    })
    .catch(error => {
      console.error("获取订阅类型列表失败:", error);
      // alert(`获取订阅类型详情时出错: ${error.message}`);
      showToast(`获取订阅类型详情时出错: ${error.message}`, "danger");
    });
}

function deleteSubscriptionType(typeId) {
  const token = localStorage.getItem("token");
  
  fetch(`/api/admin/subscription-types/${typeId}`, {
    method: "DELETE",
    headers: { Authorization: `Bearer ${token}` }
  })
    .then(response => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then(data => {
      if (!data) return; // Handled by unauthorized check
      if (data.success) {
        // alert("删除订阅类型成功");
        showToast("删除订阅类型成功", "success");
        loadSubscriptionTypes(); // 重新加载列表
      } else {
        // alert(`删除订阅类型失败: ${data.message || '未知错误'}`);
        showToast(`删除订阅类型失败: ${data.message || '未知错误'}`, "danger");
      }
    })
    .catch(error => {
      console.error("删除订阅类型失败:", error);
      // alert(`删除订阅类型时出错: ${error.message}`);
      showToast(`删除订阅类型时出错: ${error.message}`, "danger");
    });
}

// 添加新订阅类型
document.getElementById("save-subscription-type-btn").addEventListener("click", function() {
  const token = localStorage.getItem("token");
  const form = document.getElementById("subscription-type-form");

  // 重置验证状态
  form.classList.remove('was-validated');

  // 执行 Bootstrap 验证
  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return; // 阻止提交
  }

  // 表单验证通过，继续
  const formData = {
    // code: document.getElementById("type-code").value, // Removed code
    name: document.getElementById("type-name").value,
    max_devices: parseInt(document.getElementById("type-max-devices").value),
    price: parseFloat(document.getElementById("type-price").value),
    days: parseInt(document.getElementById("type-days").value),
    requirements: document.getElementById("type-requirements").value,
    is_public: document.getElementById("type-is-public").checked,
    default_subscription_instance_capacity: parseInt(document.getElementById("type-default-instance-capacity").value) || null // Added this field
  };
  
  // 移除之前的验证逻辑
  /*
  // 表单验证
  if (!formData.code || !formData.name) {
    // alert("请填写必填字段");
    showToast("请填写类型代码和显示名称", "warning"); // 改用 Toast
    return;
  }
  */
  
  fetch("/api/subscription-types", {
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(formData)
  })
    .then(response => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then(data => {
      if (!data) return; // Handled by unauthorized check
      if (data.success) {
        // alert("添加订阅类型成功");
        showToast("添加订阅类型成功", "success");
        
        // 重置表单
        document.getElementById("subscription-type-form").reset();
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById("addSubscriptionTypeModal"));
        modal.hide();
        
        // 重新加载列表
        loadSubscriptionTypes();
      } else {
        // alert(`添加订阅类型失败: ${data.message || '未知错误'}`);
        showToast(`添加订阅类型失败: ${data.message || '未知错误'}`, "danger");
      }
    })
    .catch(error => {
      console.error("添加订阅类型失败:", error);
      // alert(`添加订阅类型时出错: ${error.message}`);
      showToast(`添加订阅类型时出错: ${error.message}`, "danger");
    });
});

// 保存编辑的订阅类型
document.getElementById("save-edit-subscription-type-btn").addEventListener("click", function() {
  const token = localStorage.getItem("token");
  const typeId = document.getElementById("edit-type-id").value;
  const form = document.getElementById("edit-subscription-type-form");

  // 重置验证状态
  form.classList.remove('was-validated');

  // 执行 Bootstrap 验证
  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return; // 阻止提交
  }

  // 表单验证通过，继续
  const formData = {
    // code: document.getElementById("edit-type-code").value, // Removed code
    name: document.getElementById("edit-type-name").value,
    max_devices: parseInt(document.getElementById("edit-type-max-devices").value),
    price: parseFloat(document.getElementById("edit-type-price").value),
    days: parseInt(document.getElementById("edit-type-days").value),
    requirements: document.getElementById("edit-type-requirements").value,
    is_public: document.getElementById("edit-type-is-public").checked,
    default_subscription_instance_capacity: parseInt(document.getElementById("edit-type-default-instance-capacity").value) || null // Added this field
  };
  
  // 移除之前的验证逻辑
  /*
  // 表单验证
  if (!formData.code || !formData.name) {
    // alert("请填写必填字段");
    showToast("请填写类型代码和显示名称", "warning"); // 改用 Toast
    return;
  }
  */
  
  fetch(`/api/subscription-types/${typeId}`, {
    method: "PUT",
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(formData)
  })
    .then(response => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then(data => {
      if (!data) return; // Handled by unauthorized check
      if (data.success) {
        // alert("更新订阅类型成功");
        showToast("更新订阅类型成功", "success");
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById("editSubscriptionTypeModal"));
        modal.hide();
        
        // 重新加载列表
        loadSubscriptionTypes();
      } else {
        // alert(`更新订阅类型失败: ${data.message || '未知错误'}`);
        showToast(`更新订阅类型失败: ${data.message || '未知错误'}`, "danger");
      }
    })
    .catch(error => {
      console.error("更新订阅类型失败:", error);
      // alert(`更新订阅类型时出错: ${error.message}`);
      showToast(`更新订阅类型时出错: ${error.message}`, "danger");
    });
});

// 编辑账号示例（仅给出一个占位做参考）
async function loadAccountForEdit(accountId) {
  const token = localStorage.getItem("token");
  
  try {
    // 先加载订阅实例列表
    const instancesResponse = await fetch("/api/admin/subscription-instances", {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (handleUnauthorizedAdminResponse(instancesResponse)) return;
    
    const instancesData = await instancesResponse.json();
    
    // 填充订阅实例下拉框
    const instanceSelect = document.getElementById("edit-subscription_instance");
    if (instanceSelect) {
      instanceSelect.innerHTML = '<option value="">-- 不关联到任何实例 --</option>';
      if (instancesData.success && instancesData.data && instancesData.data.subscription_instances) {
        instancesData.data.subscription_instances.forEach(instance => {
          if (instance.is_active) { // 只显示活跃的实例
            const option = document.createElement("option");
            option.value = instance.id;
            option.textContent = `${instance.name} (${instance.subscription_type_name || '未知类型'})`;
            instanceSelect.appendChild(option);
          }
        });
      }
    }
    
    // 获取账号详情
    const response = await fetch(`/api/admin/accounts/adspower/${accountId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (handleUnauthorizedAdminResponse(response)) return;
    
    const data = await response.json();
    
    if (data.success && data.data) {
      const account = data.data;
      
      // 填充表单
      document.getElementById("edit-account-id").value = account.id;
      document.getElementById("edit-username").value = account.username;
      document.getElementById("edit-password").value = ""; // 密码不回显
      document.getElementById("edit-totp_secret").value = account.totp_secret || "";
      document.getElementById("edit-max_devices").value = account.max_devices || 10;
      
      
      // 设置订阅实例
      if (instanceSelect) {
        instanceSelect.value = account.subscription_instance_id || "";
      }
      
      // 获取账号的Cookies
      try {
        const cookiesResponse = await fetch(`/api/admin/accounts/adspower/${accountId}/cookies`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (!handleUnauthorizedAdminResponse(cookiesResponse)) {
          const cookiesData = await cookiesResponse.json();
          if (cookiesData.success && cookiesData.cookies) {
            document.getElementById("edit-cookies").value = 
              JSON.stringify(cookiesData.cookies, null, 2);
          }
        }
      } catch (error) {
        console.error("获取账号Cookies失败:", error);
      }
      
      // 显示编辑模态框
      const modal = new bootstrap.Modal(document.getElementById("editAccountModal"));
      modal.show();
    } else {
      showToast(`获取账号详情失败: ${data.message || '未知错误'}`, "danger");
    }
  } catch (error) {
    console.error("获取账号详情失败:", error);
    showToast(`获取账号详情时出错: ${error.message}`, "danger");
  }
}

// 加载订阅类型选项到下拉菜单
function loadSubscriptionTypeOptions() {
  const token = localStorage.getItem("token");
  
  fetch("/api/subscription-types", {
    headers: { Authorization: `Bearer ${token}` }
  })
    .then(response => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then(data => {
      if (!data) return; // Handled by unauthorized check
      if (data.success && data.types) {
        // 调用新的填充函数，以更新所有相关的下拉框
        populateSubscriptionTypeSelects(data.types);
      }
    })
    .catch(error => {
      console.error("加载订阅类型选项失败:", error);
    });
}

// 新增：用于填充所有订阅类型相关的下拉选择框
function populateSubscriptionTypeSelects(types) {
  const selectsToPopulate = [
    { id: "subscription_type", defaultText: "-- 无 (允许所有用户) --" },
    { id: "edit-user-subscription-plan", defaultText: "-- 无订阅 --" },
    { id: "batch-subscription-type", defaultText: "-- 选择订阅类型 --" },
    { id: "add-instance-subscription-type", defaultText: "-- 请选择订阅类型 --" },
    { id: "edit-instance-subscription-type", defaultText: "-- 请选择订阅类型 --" },
    { id: "edit-sub-plan", defaultText: "-- 选择订阅计划 --" }
  ];

  selectsToPopulate.forEach(selectInfo => {
    const selectElement = document.getElementById(selectInfo.id);
    if (selectElement) {
      // 清空现有选项并添加默认选项
      selectElement.innerHTML = `<option value="">${selectInfo.defaultText}</option>`;
      if (types && types.length > 0) {
        types.forEach(type => {
          const option = document.createElement("option");
          option.value = type.id; // 使用 ID 作为 value
          if (selectInfo.id === "edit-user-subscription-plan") {
            option.textContent = `${type.name} (${type.days}天, ${type.max_devices}设备)`;
          } else {
            option.textContent = type.name; // 显示名称
          }
          // 为添加订阅实例模态框的选项添加默认容量数据属性
          if (selectInfo.id === "add-instance-subscription-type" && type.default_subscription_instance_capacity !== null && type.default_subscription_instance_capacity !== undefined) {
            option.dataset.defaultCapacity = type.default_subscription_instance_capacity;
          }
          selectElement.appendChild(option);
        });
      }
    }
  });
}


// 加载用户信息到编辑模态框
async function loadUserForEdit(user) {
    console.log("Loading user for edit:", user); 
    if (!user || !user.id) { 
        showToast("无法加载用户信息 (无效的用户对象)", "warning");
        return;
    }
    
    const token = localStorage.getItem("token");
    fetch(`/api/admin/users/${user.id}/details`, { 
      method: "GET",
      headers: { Authorization: `Bearer ${token}` },
    })
    .then((response) => {
        if (handleUnauthorizedAdminResponse(response)) return null;
        return response.json();
    })
    .then(async (data) => {
      if (!data) return; 
      if (!data.success || !data.data || !data.data.user) {
        throw new Error(data.message || "获取用户详情失败");
      }
      
      const userDetails = data.data.user;
      
      document.getElementById('edit-user-id').value = userDetails.id;
      document.getElementById('edit-user-email').value = userDetails.email;
      document.getElementById('edit-user-is-admin').checked = userDetails.is_admin;
      document.getElementById('edit-user-is-active').checked = userDetails.is_active;
      
      // 保存当前订阅信息
      let currentSubscriptionTypeId = null;
      let currentSubscriptionInstanceId = null;
      let currentSubscriptionInfo = null;
      if (data.data.subscriptions && data.data.subscriptions.length > 0) {
        console.log('Subscriptions data:', data.data.subscriptions);
        const activeSubscription = data.data.subscriptions.find(sub => sub.status === '活跃');
        if (activeSubscription) {
          console.log('Active subscription:', activeSubscription);
          currentSubscriptionTypeId = activeSubscription.subscription || activeSubscription.subscription_type_id;
          currentSubscriptionInstanceId = activeSubscription.subscription_instance_id;
          // 保存当前订阅的完整信息
          currentSubscriptionInfo = {
            name: activeSubscription.subscription_name,
            instanceName: activeSubscription.instance_name || '',
            endDate: formatDateTime(activeSubscription.end_date).split(' ')[0]
          };
          console.log('Current subscription IDs:', { typeId: currentSubscriptionTypeId, instanceId: currentSubscriptionInstanceId });
        }
      }
      
      // 重置订阅管理
      document.getElementById('edit-user-subscription-instance').innerHTML = '<option value="">请先选择订阅类型...</option>';
      document.getElementById('edit-user-subscription-days').value = 30;
      
      // 加载订阅类型，并传递当前订阅信息
      await loadSubscriptionTypesForEdit(currentSubscriptionTypeId, currentSubscriptionInfo);
      
      // 如果有当前订阅类型，加载对应的实例
      if (currentSubscriptionTypeId) {
        await loadSubscriptionInstancesForType(currentSubscriptionTypeId, document.getElementById('edit-user-subscription-instance'));
        if (currentSubscriptionInstanceId) {
          document.getElementById('edit-user-subscription-instance').value = currentSubscriptionInstanceId;
        }
      }

      // 清空密码字段
      document.getElementById('edit-user-new-password').value = '';
      document.getElementById('edit-user-confirm-password').value = '';
      
      document.getElementById('edit-user-error-alert').style.display = 'none';
      document.getElementById('edit-user-error-alert').textContent = '';
      
      const editUserModal = new bootstrap.Modal(document.getElementById('editUserModal'));
      editUserModal.show();
    })
    .catch((error) => {
      console.error("获取用户详情失败:", error);
      showToast(`获取用户详情失败: ${error.message}`, "danger");
    });
}

// 更新用户信息
function updateUser() {
    const form = document.getElementById('edit-user-form');
    const userId = document.getElementById('edit-user-id').value;
    const email = document.getElementById('edit-user-email').value;
    const isAdmin = document.getElementById('edit-user-is-admin').checked;
    const isActive = document.getElementById('edit-user-is-active').checked;
    // const isEmailVerified = document.getElementById('edit-user-is-email-verified').checked;
    const newPasswordInput = document.getElementById('edit-user-new-password');
    const confirmPasswordInput = document.getElementById('edit-user-confirm-password');
    const subscriptionTypeId = document.getElementById('edit-user-subscription-type').value;
    const subscriptionInstanceId = document.getElementById('edit-user-subscription-instance').value;
    const subscriptionDays = document.getElementById('edit-user-subscription-days').value; 
    
    const token = localStorage.getItem("token");
    const errorAlert = document.getElementById('edit-user-error-alert');

    form.classList.remove('was-validated');
    errorAlert.style.display = 'none'; 
    newPasswordInput.setCustomValidity(""); 
    confirmPasswordInput.setCustomValidity("");
    newPasswordInput.required = false; 
    confirmPasswordInput.required = false;

    // 如果输入了新密码，则需要验证
    if (newPasswordInput.value) {
        newPasswordInput.required = true;
        confirmPasswordInput.required = true;
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (newPassword && confirmPassword && newPassword !== confirmPassword) {
            confirmPasswordInput.setCustomValidity("两次输入的密码不一致。");
            document.getElementById('edit-user-confirm-password-feedback').textContent = "两次输入的密码不一致。";
        } else {
            confirmPasswordInput.setCustomValidity("");
            document.getElementById('edit-user-confirm-password-feedback').textContent = "确认密码不能为空。"; 
        }
    }

    if (!form.checkValidity()) {
      form.classList.add('was-validated');
      return; 
    }

    const payload = {
        email: email,
        is_admin: isAdmin,
        is_active: isActive
    };

    if (newPasswordInput.value) {
        payload.new_password = newPasswordInput.value;
    }

    // 处理订阅管理
    let subscriptionPromise = Promise.resolve();
    
    if (subscriptionTypeId && subscriptionInstanceId) {
        // 先分配订阅
        subscriptionPromise = fetch(`/api/admin/users/${userId}/assign-subscription`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                subscription_type_id: parseInt(subscriptionTypeId),
                subscription_instance_id: parseInt(subscriptionInstanceId),
                days: parseInt(subscriptionDays)
            })
        })
        .then(response => response.json())
        .then(result => {
            if (!result.success) {
                throw new Error(result.message || '分配订阅失败');
            }
            showToast('订阅分配成功', 'success');
        });
    }

    // 先处理订阅（如果需要），然后更新用户信息
    subscriptionPromise
    .then(() => {
        return fetch(`/api/admin/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(payload)
        });
    })
    .then(response => {
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
            return response.text().then(text => {
                console.error("Non-JSON response:", text);
                throw new Error(text || `服务器错误: ${response.status}`);
             });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast("用户信息更新成功！", "success");
            const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            modal.hide();
            loadUsers(); 
        } else {
             errorAlert.textContent = `更新失败: ${data.message || '未知错误'}`;
             errorAlert.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('更新用户时出错:', error);
        errorAlert.textContent = `更新时出错: ${error.message}`;
        errorAlert.style.display = 'block';
    });
}

// 新增：编辑用户模态框关闭时重置
const editUserModalElement = document.getElementById('editUserModal');
if (editUserModalElement) {
    editUserModalElement.addEventListener('hidden.bs.modal', function () {
        const form = document.getElementById("edit-user-form");
        form.classList.remove('was-validated');
        
        // 重置密码部分
        const newPasswordInput = document.getElementById('edit-user-new-password');
        const confirmPasswordInput = document.getElementById('edit-user-confirm-password');
        
        // 清空密码字段
        newPasswordInput.value = '';
        confirmPasswordInput.value = '';
        newPasswordInput.required = false;
        confirmPasswordInput.required = false;
        newPasswordInput.setCustomValidity("");
        confirmPasswordInput.setCustomValidity("");

        // 隐藏 API 错误提示
        const errorAlert = document.getElementById('edit-user-error-alert');
        if(errorAlert) errorAlert.style.display = 'none';

        // 不重置其他表单域内容 (邮箱, is_admin 等)
        // form.reset(); 
    });
}

async function editUser(userId) {
    // Find the user data from the currently loaded list or fetch it again
    // For simplicity, let's assume we fetch details again
    const token = localStorage.getItem("token");
    
    try {
        const response = await fetch(`/api/admin/users/${userId}/details`, { 
            headers: { 'Authorization': `Bearer ${token}` } 
        });
        
        if (handleUnauthorizedAdminResponse(response)) return;
        
        const data = await response.json();
        if (data.success && data.data && data.data.user) {
            // 适配后端接口结构，用户信息在data.data.user中
            await loadUserForEdit(data.data.user); 
        } else {
            showToast(`获取用户信息失败: ${data.message || '未知错误'}`, "danger");
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        showToast(`获取用户信息时出错: ${error.message}`, "danger");
    }
}

// 在文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 页面初始化逻辑
  // ... existing code ...
  
  // 密码字段已默认显示，无需复选框控制
  
  // ... existing code ...
});

// Function to display user details in a modal
function viewUserDetails(userId) {
  const token = localStorage.getItem("token");
  const modalElement = document.getElementById('userDetailsModal');
  const userModal = bootstrap.Modal.getOrCreateInstance(modalElement); // Use getOrCreateInstance

  // Clear previous data & show loading state (Safer clearing)
  const idsToClearText = [
    "user-detail-id", "user-detail-email", "user-detail-last-login", "user-detail-created-at",
    "user-detail-subscription-plan", "user-detail-subscription-instance", "user-detail-subscription-start-date",
    "user-detail-subscription-end-date", "user-detail-subscription-max-devices",
    "user-detail-device-count"
  ];
  idsToClearText.forEach(id => {
    const el = modalElement.querySelector(`#${id}`); // Query within modalElement for safety
    if (el) el.textContent = '';
  });

  const idsToClearHtmlForBadges = [
    "user-detail-is-admin", "user-detail-is-active", // "user-detail-is-email-verified",
    "user-detail-subscription-status"
  ];
  idsToClearHtmlForBadges.forEach(id => {
    const el = modalElement.querySelector(`#${id}`); // Query within modalElement for safety
    if (el) el.innerHTML = '<span class="text-muted small">加载中...</span>'; // Placeholder for badges
  });
  
  document.getElementById('user-detail-devices-table-body').innerHTML = '<tr><td colspan="5" class="text-center"><span class="spinner-border spinner-border-sm"></span> 加载中...</td></tr>';
  document.getElementById('user-detail-no-subscription').style.display = 'none';
  document.getElementById('user-detail-subscription-table').style.display = 'table'; // Show table initially

  userModal.show(); // Show modal early with loading state

  fetch(`/api/admin/users/${userId}/details`, {
    method: "GET",
    headers: { Authorization: `Bearer ${token}` },
  })
  .then((response) => {
    if (handleUnauthorizedAdminResponse(response)) return null;
    if (!response.ok) {
      // Try to parse error json, fallback to status text
       return response.json().catch(() => null).then(errData => {
           throw new Error(errData?.message || `获取用户详情失败 (${response.status})`);
       });
    }
    return response.json();
  })
  .then((response) => {
    if (!response) return; // Handled by unauthorized check
    
    // 检查响应是否成功
    if (!response.success) {
      console.warn("API返回非success状态:", response.message);
      showToast(response.message || "获取用户详情数据格式错误", "warning");
      return;
    }
    
    // 根据后端API的数据结构，正确获取data对象
    const data = response.data;
    if (!data) {
      console.warn("API响应中缺少data字段:", response);
      showToast("响应数据格式错误", "warning");
      return;
    }
    
    // 从data对象中获取user详情
    const userDetails = data.user;

    // Fill basic info
    document.getElementById("user-detail-id").textContent = userDetails.id || "未知";
    document.getElementById("user-detail-email").textContent = userDetails.email || "未知";
    document.getElementById("user-detail-is-admin").innerHTML = userDetails.is_admin ?
      '<span class="badge bg-primary">是</span>' :
      '<span class="badge bg-light text-dark border">否</span>';
    document.getElementById("user-detail-is-active").innerHTML = userDetails.is_active ?
      '<span class="badge bg-success">已激活</span>' :
      '<span class="badge bg-secondary">未激活</span>';
    // document.getElementById("user-detail-is-email-verified").innerHTML = userDetails.is_email_verified ?
    //   '<span class="badge bg-success">已验证</span>' :
    //   '<span class="badge bg-warning">未验证</span>';
    document.getElementById("user-detail-last-login").textContent = userDetails.last_login ?
      formatDateTime(userDetails.last_login) : "从未登录";
    document.getElementById("user-detail-created-at").textContent = userDetails.created_at ?
      formatDateTime(userDetails.created_at) : "未知";

    // Fill subscription info
    const subInfoContainer = document.getElementById('user-detail-subscription-table');
    const noSubInfoContainer = document.getElementById('user-detail-no-subscription');

    // 根据API数据结构，正确获取订阅信息
    if (data.subscriptions && data.subscriptions.length > 0) {
      const currentSubscription = data.subscriptions[0]; // Display the first subscription

      subInfoContainer.style.display = "table";
      noSubInfoContainer.style.display = "none";

      document.getElementById("user-detail-subscription-plan").textContent =
        currentSubscription.subscription_name || "未知";
      
      // 显示订阅实例
      document.getElementById("user-detail-subscription-instance").textContent =
        currentSubscription.instance_name || "未分配";

      const statusElement = document.getElementById("user-detail-subscription-status");
      let statusText = '未知';
      let statusClass = 'secondary';

      if (currentSubscription.status) {
          statusText = currentSubscription.status;
          if (statusText === '活跃') statusClass = 'success';
          else if (statusText === '已过期') statusClass = 'danger';
          else if (statusText === '即将过期') statusClass = 'warning';
          // Add more status mappings if necessary
      } else if (currentSubscription.end_date) { // Fallback if status is not directly provided but end_date is
          const endDate = new Date(currentSubscription.end_date);
          const now = new Date();
          const daysDifference = (endDate - now) / (1000 * 60 * 60 * 24);

          if (now > endDate) {
              statusText = '已过期'; statusClass = 'danger';
          } else if (daysDifference <= 7) { // Example: less than 7 days left
              statusText = '即将过期'; statusClass = 'warning';
          } else {
              statusText = '活跃'; statusClass = 'success';
          }
      }
      statusElement.innerHTML = `<span class="badge bg-${statusClass}">${statusText}</span>`;

      document.getElementById("user-detail-subscription-start-date").textContent =
        currentSubscription.start_date ? formatDateTime(currentSubscription.start_date) : "未知";
      document.getElementById("user-detail-subscription-end-date").textContent =
        currentSubscription.end_date ? formatDateTime(currentSubscription.end_date) : "未知";
      document.getElementById("user-detail-subscription-max-devices").textContent =
        currentSubscription.max_devices || "未限制";
    } else {
      subInfoContainer.style.display = "none";
      noSubInfoContainer.style.display = "block";
    }

    // Fill devices list
    const devicesTableBody = document.getElementById("user-detail-devices-table-body");
    devicesTableBody.innerHTML = ""; // Clear loading state
    document.getElementById("user-detail-device-count").textContent = (data.devices ? data.devices.length : 0);

    if (data.devices && data.devices.length > 0) {
      data.devices.forEach(device => {
        const row = document.createElement("tr");
        row.innerHTML = `
          <td>${device.id}</td>
          <td>${device.name || "未知设备"}</td> 
          <td>${device.type || "未知"}</td>    
          <td>${device.ip_address || "未知"}</td>
          <td>
            <button class="btn btn-sm btn-danger delete-device-btn" data-device-id="${device.id}" data-device-name="${device.name || '未知设备'}" title="删除设备">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        `;
        devicesTableBody.appendChild(row);

        // Bind delete device button click event
        const deleteBtn = row.querySelector(".delete-device-btn");
        if (deleteBtn) {
            deleteBtn.addEventListener("click", (e) => {
                const deviceId = e.currentTarget.getAttribute("data-device-id");
                const deviceName = e.currentTarget.getAttribute("data-device-name");
                if (confirm(`确定要删除设备 "${deviceName}" 吗？`)) {
                    deleteDevice(deviceId, e.currentTarget);
                }
            });
        }
      });
    } else {
      devicesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">该用户没有设备</td></tr>'; // Colspan 5
    }

  })
  .catch((error) => {
    console.error("获取用户详情失败:", error);
    // Display error in the modal body
    document.getElementById('user-detail-devices-table-body').innerHTML = `<tr><td colspan="5" class="text-center text-danger">加载失败: ${error.message}</td></tr>`;
    // 使用Toast而不是alert，避免阻塞用户操作
    showToast(`获取用户详情失败: ${error.message}`, "danger");
  });
}

function toggleUserStatus(userId, enable) { // This seems like an older version, toggleUserActiveStatus is used now
  if (!confirm(`确定要${enable ? "启用" : "禁用"}此用户吗？`)) {
    return;
  }
  
  const token = localStorage.getItem("token");
  fetch(`/api/admin/users/${userId}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ is_active: enable }),
  })
    .then((response) => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (response.success) {
        // alert(`用户已${enable ? "启用" : "禁用"}`);
        showToast(`用户已${enable ? "启用" : "禁用"}`, "success");
        // 更新 allUsers 数组中的状态
        const userIndex = allUsers.findIndex(u => u.id === userId);
        if (userIndex > -1) {
          allUsers[userIndex].is_active = enable;
          updateUsersTable(allUsers); // 重新渲染
      } else {
          loadUsers(); // 找不到就重新加载
        }
        // loadUsers(); // 不再需要重新 fetch
      } else {
        // alert(data.message || `${enable ? "启用" : "禁用"}用户失败`);
        showToast(data.message || `${enable ? "启用" : "禁用"}用户失败`, "danger");
      }
    })
    .catch((error) => {
      console.error("更新用户状态时出错:", error);
      // alert(`${enable ? "启用" : "禁用"}用户失败，请稍后再试`);
      showToast(`${enable ? "启用" : "禁用"}用户失败，请稍后再试`, "danger");
    });
}

// ... 其他代码 ...

// --- 新增：显示/隐藏表格加载状态 ---
function showTableLoading(tableBodyId, isLoading, colspan) {
  const tableBody = document.getElementById(tableBodyId);
  if (!tableBody) return;

  const loadingRowId = `loading-row-${tableBodyId}`;
  const existingLoadingRow = document.getElementById(loadingRowId);

  if (isLoading) {
    if (!existingLoadingRow) {
      tableBody.innerHTML = ''; // 清空现有内容
      const loadingRow = document.createElement('tr');
      loadingRow.id = loadingRowId;
      loadingRow.innerHTML = `
        <td colspan="${colspan}" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 mb-0 text-muted">加载中...</p>
        </td>
      `;
      tableBody.appendChild(loadingRow);
    }
  } else {
    if (existingLoadingRow) {
      existingLoadingRow.remove();
    }
  }
}

/************** 数据加载函数 **************/
// 加载设备列表
// ... existing code ...

// --- 新增：通用表格排序函数 ---
function sortTable(tableId, columnIndex, dataType) {
  console.log(`排序表格: ${tableId}, 列: ${columnIndex}, 类型: ${dataType}`);
  
  const table = document.getElementById(tableId);
  if (!table) {
    console.error(`找不到表格: ${tableId}`);
    return;
  }
  const tbody = table.querySelector("tbody");
  const headers = table.querySelectorAll("thead th[data-sortable]");
  const header = headers[columnIndex];
  
  // 动态创建排序状态如果不存在
  if (!sortState[tableId]) {
    sortState[tableId] = { column: null, direction: 'ascending' };
  }
  const currentTableState = sortState[tableId];

  if (!header) {
    console.error(`找不到表头: 列索引 ${columnIndex}`);
    return;
  }

  const currentDirection = header.dataset.sortDirection;
  let newDirection;

  if (currentDirection === 'ascending') {
    newDirection = 'descending';
  } else {
    newDirection = 'ascending'; // Default to ascending if no direction or descending
  }

  // Update state
  currentTableState.column = columnIndex;
  currentTableState.direction = newDirection;

  // Remove sort direction attributes from all headers in this table
  headers.forEach(th => {
    delete th.dataset.sortDirection;
    th.removeAttribute('aria-sort');
  });

  // Set sort direction attribute on the clicked header
  header.dataset.sortDirection = newDirection;
  header.setAttribute('aria-sort', newDirection); // ARIA sort attribute

  // 通用方法获取数据和更新函数
  const tableDataMap = {
    'devices-table': { data: allDevices, update: updateDevicesTable },
    'users-table': { data: allUsers, update: updateUsersTable },
    'subscription-types-table': { data: allSubscriptionTypes, update: updateSubscriptionTypesTable },
    'subscriptions-table': { data: allSubscriptions, update: updateSubscriptionsTable },
    'accounts-table': { data: allAdspowerAccounts, update: updateAdspowerAccountsTable },
    'subscription-instances-table': { data: allSubscriptionInstances, update: updateSubscriptionInstancesTable }
  };
  
  // 订单表使用分页，不支持客户端排序
  if (tableId === 'orders-table') {
    console.log('订单表使用服务端分页，不支持客户端排序');
    return;
  }
  
  const tableConfig = tableDataMap[tableId];
  if (!tableConfig || !tableConfig.data) {
    console.error("Unknown table ID or no data for sorting:", tableId);
    return;
  }
  
  console.log(`找到表格配置，数据长度: ${tableConfig.data.length}`);
  let dataArray = [...tableConfig.data];
  let updateFunction = tableConfig.update;

  // Sort the data array
  dataArray.sort((a, b) => {
    // Find the correct data key based on columnIndex and header content/attribute
    // This needs a robust way to map columnIndex to data key.
    // Let's assume th has a 'data-key' attribute for simplicity.
    // If not, we need a mapping based on table structure.
    // For now, let's map manually for known tables.
    // 优先使用 data-key 属性
    let dataKey = header.getAttribute('data-key');
    
    if (!dataKey) {
      console.error(`表头缺少 data-key 属性: ${tableId} 列 ${columnIndex}`);
      return 0;
    }
    
    console.log(`使用排序键: ${dataKey}`);

    // Helper to get potentially nested values
    const getValue = (obj, key) => key.split('.').reduce((o, k) => (o || {})[k], obj);

    keyA = getValue(a, dataKey);
    keyB = getValue(b, dataKey);
    
    // 调试：打印第一条数据的值
    if (dataArray.indexOf(a) === 0) {
      console.log(`第一条数据的 ${dataKey} 值:`, keyA);
      console.log('完整数据对象:', a);
    }


    // Handle null/undefined/empty values - place them at the end when ascending
    const valAExists = keyA !== null && keyA !== undefined && keyA !== '';
    const valBExists = keyB !== null && keyB !== undefined && keyB !== '';

    if (!valAExists && !valBExists) return 0;
    if (!valAExists) return newDirection === 'ascending' ? 1 : -1;
    if (!valBExists) return newDirection === 'ascending' ? -1 : 1;


    // Comparison logic based on dataType
    let comparison = 0;
    switch (dataType) {
      case 'number':
        comparison = parseFloat(keyA) - parseFloat(keyB);
        break;
      case 'date':
         // Ensure dates are valid before comparing
         const dateA = new Date(keyA);
         const dateB = new Date(keyB);
         if (!isNaN(dateA) && !isNaN(dateB)) {
             comparison = dateA - dateB;
         } else if (!isNaN(dateA)) {
             comparison = -1; // Valid date comes first
         } else if (!isNaN(dateB)) {
             comparison = 1;
         } else {
             comparison = 0; // Both invalid
         }
         break;
      case 'boolean': // Added boolean type for status etc.
         comparison = (keyA === keyB)? 0 : keyA? -1 : 1; // True comes before false
         break;
      case 'string':
      default:
        comparison = String(keyA).localeCompare(String(keyB), 'zh-CN', { sensitivity: 'base' }); // Case-insensitive-ish string comparison
        break;
    }

    return newDirection === 'ascending' ? comparison : -comparison;
  });

  // Update the table body with sorted data
  console.log(`排序完成，调用更新函数，数据长度: ${dataArray.length}`);
  updateFunction(dataArray);

  // Announce the sort action
   const columnName = header.textContent.trim();
   announce(`表格已按 ${columnName} ${newDirection === 'ascending' ? '升序' : '降序'} 排序`);
}

// --- 新增：为可排序表头添加事件监听器 ---
function addSortEventListeners() {
  const tables = ['devices-table', 'users-table', 'subscription-types-table', 'subscriptions-table', 'accounts-table', 'subscription-instances-table', 'orders-table']; // 包括订阅实例表和订单表
  tables.forEach(tableId => {
    const table = document.getElementById(tableId);
    if (table) {
      const thead = table.querySelector('thead');
      if (thead) {
        thead.addEventListener('click', (event) => {
          const header = event.target.closest('th[data-sortable]');
          if (header) {
            const columnIndex = Array.from(header.parentNode.children).indexOf(header);
            const dataType = header.dataset.sortType || 'string'; // Default to string
            sortTable(tableId, columnIndex, dataType);
          }
        });
      }
      // 验证表头具有必需的属性
      validateTableHeaders(tableId);
    }
  });
}

// --- 新增：辅助函数，验证表头具有必需的 data-key 属性 ---
function validateTableHeaders(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const headers = table.querySelectorAll('thead th[data-sortable]');
    let missingKeys = [];
    
    headers.forEach((th, index) => {
        if (!th.dataset.key) {
            missingKeys.push(`列 ${index + 1}: ${th.textContent.trim()}`);
        }
    });
    
    if (missingKeys.length > 0) {
        console.warn(`表格 ${tableId} 缺少 data-key 属性的列:`, missingKeys);
    }
}

/************** 数据加载函数 **************/
// 加载设备列表
// ... existing code ...
function loadSubscriptions() {
  const token = localStorage.getItem("token");
  const tableBodyId = "subscriptions-table-body";
  const colspan = 8; // 订阅表预计8列: 用户, 计划, 实例, 状态, 开始, 结束, 设备数, 操作

  showTableLoading(tableBodyId, true, colspan);

  fetch("/api/admin/subscriptions", { headers: { Authorization: `Bearer ${token}` } })
    .then(response => {
      if (handleUnauthorizedAdminResponse(response)) return null;
      if (!response.ok) {
        return response.json().then(err => { throw new Error(err.message || `HTTP error ${response.status}`) });
      }
      return response.json();
    })
    .then(data => {
      if (!data) return; // Handled by unauthorized check
      // 适配新的响应结构 data.data.subscriptions
      if (data.success && data.data && data.data.subscriptions) {
        allSubscriptions = data.data.subscriptions; // 从 data.data.subscriptions 获取列表
        showTableLoading(tableBodyId, false, colspan);
        updateSubscriptionsTable(allSubscriptions);
        announce(`${allSubscriptions.length}个订阅已加载`);
      } else {
        allSubscriptions = [];
        showTableLoading(tableBodyId, false, colspan);
        document.getElementById(tableBodyId).innerHTML =
          `<tr><td colspan="${colspan}" class="text-center">${data.message || '没有订阅数据'}</td></tr>`;
        announce(data.message || "没有订阅数据");
      }
    })
    .catch(error => {
      console.error("Error fetching subscriptions:", error);
      showTableLoading(tableBodyId, false, colspan);
      document.getElementById(tableBodyId).innerHTML = `<tr><td colspan="${colspan}" class="text-center text-danger">加载订阅失败: ${error.message}</td></tr>`;
      showToast(`加载订阅失败: ${error.message}`, "danger");
      announce("加载订阅失败");
    });
}

function updateSubscriptionsTable(subscriptions) {
    const tableBody = document.getElementById('subscriptions-table-body');
    if (!tableBody) return;
    tableBody.innerHTML = ''; // 清空现有行

  if (!subscriptions || subscriptions.length === 0) {
        const row = tableBody.insertRow();
        const cell = row.insertCell();
        cell.colSpan = 8; // 8列：去掉价格列
        cell.textContent = '没有找到订阅信息。';
        cell.className = 'text-center';
    return;
  }

  subscriptions.forEach(sub => {
        const row = tableBody.insertRow();
        row.insertCell().textContent = sub.user_email || 'N/A';
        row.insertCell().textContent = sub.subscription_name || 'N/A';
        
        // 添加订阅实例列
        const instanceCell = row.insertCell();
        if (sub.subscription_instance_name) {
            instanceCell.textContent = sub.subscription_instance_name;
        } else {
            instanceCell.innerHTML = '<span class="text-muted">未分配</span>';
        }
        
        const statusCell = row.insertCell();
        const statusBadge = document.createElement('span');
        statusBadge.className = `badge ${getSubscriptionBadgeClass(sub.status)}`;
        statusBadge.textContent = sub.status_display || sub.status;
        statusCell.appendChild(statusBadge);

        row.insertCell().textContent = sub.start_date ? formatDateTime(sub.start_date) : 'N/A';
        row.insertCell().textContent = sub.end_date ? formatDateTime(sub.end_date) : 'N/A';

        // Max Devices column
        row.insertCell().textContent = (sub.max_devices !== null && sub.max_devices !== undefined) ? sub.max_devices : 'N/A';

        const actionsCell = row.insertCell();
        actionsCell.className = 'text-nowrap'; // Ensure buttons don't wrap unnecessarily
        actionsCell.innerHTML = `
            <button class="btn btn-sm btn-outline-info me-1" onclick="viewSubscriptionDetail(${sub.id})" title="查看详情">
                <i class="bi bi-eye"></i> 查看
            </button>
            <button class="btn btn-sm btn-outline-primary me-1" onclick="openEditSubscriptionModal(${sub.id})" title="编辑订阅">
                <i class="bi bi-pencil-square"></i> 编辑
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="handleDeleteSubscription(${sub.id}, '${sub.user_email || '该用户'}')" title="删除订阅">
                <i class="bi bi-trash"></i> 删除
            </button>
        `;
    });
    announce(`订阅列表已更新，共 ${subscriptions.length} 条记录。`);
}

async function openEditSubscriptionModal(subscriptionId) {
    const modalElement = document.getElementById('editSubscriptionModal');
    if (!modalElement) {
        console.error('Modal element #editSubscriptionModal not found in DOM');
        showToast('UI错误：找不到编辑模态框元素', 'danger');
        return;
    }
    const modal = new bootstrap.Modal(modalElement);
    
    const form = document.getElementById('edit-subscription-form');
    if (!form) {
        console.error('Form element #edit-subscription-form not found in DOM');
        showToast('UI错误：找不到编辑表单元素', 'danger');
        return;
    }
    form.classList.remove('was-validated');
    form.reset(); // 重置表单

    const safeSetValue = (elementId, value) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = value;
        } else {
            console.warn(`Element with ID "${elementId}" not found when trying to set value: ${value}`);
        }
    };

    const token = localStorage.getItem('token'); // 获取认证Token
    if (!token) {
        showToast('用户未登录或会话已过期，请重新登录。', 'danger');
        return;
    }

    try {
        // Ensure subscription types are loaded and populate the specific select for this modal
        const planSelect = document.getElementById('edit-sub-plan');
        const instanceSelect = document.getElementById('edit-sub-instance');
        
        if (planSelect) {
            planSelect.innerHTML = '<option value="">-- 选择订阅计划 --</option>'; 
            if (allSubscriptionTypes && allSubscriptionTypes.length > 0) {
                 allSubscriptionTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id; 
                    option.textContent = type.name; 
                    planSelect.appendChild(option);
                });
            } else {
                 // If allSubscriptionTypes is not populated, load them.
                 // loadSubscriptionTypes itself calls populateSubscriptionTypeSelects which populates all such dropdowns.
                 await loadSubscriptionTypes(); 
                 // After loading, allSubscriptionTypes should be populated, so re-populate this specific select if it wasn't by the generic call
                 if (allSubscriptionTypes && allSubscriptionTypes.length > 0 && planSelect.options.length <=1) { // check if it was not populated
                    allSubscriptionTypes.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.id;
                        option.textContent = type.name;
                        planSelect.appendChild(option);
                    });
                 }
            }
            
            // 添加订阅计划变更事件监听器
            planSelect.addEventListener('change', async function() {
                const selectedTypeId = this.value;
                instanceSelect.innerHTML = '<option value="">-- 加载中... --</option>';
                
                if (!selectedTypeId) {
                    instanceSelect.innerHTML = '<option value="">-- 请先选择订阅计划 --</option>';
                    return;
                }
                
                try {
                    const response = await fetch(`/api/admin/subscription-types/${selectedTypeId}/instances`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        instanceSelect.innerHTML = '<option value="">-- 选择订阅实例 --</option>';
                        
                        if (result.data && result.data.instances) {
                            result.data.instances.forEach(instance => {
                                const option = document.createElement('option');
                                option.value = instance.id;
                                option.textContent = `${instance.name} (容量: ${instance.current_users}/${instance.capacity})`;
                                instanceSelect.appendChild(option);
                            });
                        }
                    } else {
                        instanceSelect.innerHTML = '<option value="">-- 加载失败 --</option>';
                    }
                } catch (error) {
                    console.error('Error loading instances:', error);
                    instanceSelect.innerHTML = '<option value="">-- 加载失败 --</option>';
                }
            });
        } else {
            console.error('Select element #edit-sub-plan not found in DOM for openEditSubscriptionModal');
        }

        const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        if (handleUnauthorizedAdminResponse(response)) return;

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ message: response.statusText }));
            showToast(`获取订阅详情失败: ${errorData.message || response.statusText}`, 'danger');
            return;
        }
        
        const result = await response.json();
        if (result.success && result.data) {
            const sub = result.data;
            
            safeSetValue('edit-subscription-id', sub.id);
            safeSetValue('edit-sub-user-email', sub.user_email || 'N/A');
            
            if (typeof toLocalISOString === 'function') {
                if (sub.start_date) safeSetValue('edit-sub-start-date', toLocalISOString(sub.start_date));
                if (sub.end_date) safeSetValue('edit-sub-end-date', toLocalISOString(sub.end_date));
            }
            
            if (sub.max_devices !== null) safeSetValue('edit-sub-max-devices', sub.max_devices);
  
            // Set the selected plan using subscription_type_id from the backend
            if (planSelect && sub.subscription_type_id) {
                planSelect.value = sub.subscription_type_id;
                
                // 触发change事件以加载订阅实例
                await planSelect.dispatchEvent(new Event('change'));
                
                // 设置当前订阅实例
                setTimeout(() => {
                    if (instanceSelect && sub.subscription_instance_id) {
                        instanceSelect.value = sub.subscription_instance_id;
                    }
                }, 500);
            } else if (planSelect) {
                planSelect.value = ""; // Default to no selection if no ID
            }

            modal.show();
      } else {
            showToast(`获取订阅详情失败: ${result.message || '未知错误'}`, 'danger');
      }
    } catch (error) {
        showToast(`打开编辑模态框时发生错误: ${error.message}`, 'danger');
        console.error('Error opening edit subscription modal:', error);
    }
}

async function handleUpdateSubscription() {
    const form = document.getElementById('edit-subscription-form');
    if (!form) {
        showToast('表单元素不存在', 'danger');
        return;
    }

  if (!form.checkValidity()) {
    form.classList.add('was-validated');
        showToast('请检查表单中的错误。', 'warning');
    return;
  }

    const subscriptionIdElement = document.getElementById('edit-subscription-id');
    if (!subscriptionIdElement) {
        showToast('找不到订阅ID字段', 'danger');
        return;
    }
    const subscriptionId = subscriptionIdElement.value;
    
    const planElement = document.getElementById('edit-sub-plan');
    const instanceElement = document.getElementById('edit-sub-instance');
    const startDateElement = document.getElementById('edit-sub-start-date');
    const endDateElement = document.getElementById('edit-sub-end-date');
    const maxDevicesElement = document.getElementById('edit-sub-max-devices');
    
    if (!planElement || !instanceElement || !startDateElement || !endDateElement) {
        showToast('缺少必要的表单字段', 'danger');
        return;
    }
    
    // 'planElement.value' now correctly holds the subscription_type_id
          const subscriptionTypeId = planElement.value;
          const subscriptionInstanceId = instanceElement.value;
    const startDate = startDateElement.value;
    const endDate = endDateElement.value;
    const maxDevices = maxDevicesElement ? maxDevicesElement.value : '';

    if (!subscriptionTypeId || !startDate || !endDate) {
        showToast('请填写所有必填字段', 'warning');
        return;
    }

    if (new Date(startDate) >= new Date(endDate)) {
        showToast('结束日期必须晚于开始日期。', 'danger');
        if (endDateElement) endDateElement.classList.add('is-invalid');
        return;
    }
    if (endDateElement) endDateElement.classList.remove('is-invalid');

    // 确保toUTCISOString函数存在
    const dateToUTC = (dateStr) => {
        if (typeof toUTCISOString === 'function') {
            return toUTCISOString(dateStr);
        }
        // 简单的ISO日期转换（没有时区处理）
        return new Date(dateStr).toISOString();
  };
  
      const payload = {
        subscription_type_id: subscriptionTypeId,
        subscription_instance_id: subscriptionInstanceId ? parseInt(subscriptionInstanceId, 10) : null,
        start_date: startDate ? dateToUTC(startDate) : null,
        end_date: endDate ? dateToUTC(endDate) : null,
        max_devices: maxDevices === '' ? null : parseInt(maxDevices, 10)
  };

    const saveButton = document.getElementById('save-subscription-changes-btn');
    if (saveButton) setButtonLoading(saveButton, true);
    
    const token = localStorage.getItem('token');
    if (!token) {
        showToast('用户未登录或会话已过期', 'danger');
        if (saveButton) setButtonLoading(saveButton, false);
        return;
    }

    try {
        const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`, {
            method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(payload)
        });

        if (handleUnauthorizedAdminResponse(response)) return;

        const result = await response.json();
        if (result.success) {
            showToast('订阅信息更新成功！', 'success');
      const modal = bootstrap.Modal.getInstance(document.getElementById('editSubscriptionModal'));
            if (modal) {
      modal.hide();
            }
            loadSubscriptions(); // 刷新订阅列表
    } else {
            showToast(`更新失败: ${result.message || '未知错误'}`, 'danger');
        }
    } catch (error) {
        showToast(`更新订阅时发生错误: ${error.message}`, 'danger');
        console.error('Error updating subscription:', error);
    } finally {
        if (saveButton) setButtonLoading(saveButton, false, '保存更改');
    }
}

// 查看订阅实例详情
function viewSubscriptionInstance(instanceId) {
    // 切换到订阅实例管理页面
    switchSection('subscription-instances');
    
    // 滚动到对应的实例行并高亮显示
    setTimeout(() => {
        const instanceRows = document.querySelectorAll('#subscription-instances-table-body tr');
        instanceRows.forEach(row => {
            // 查找包含该实例ID的行
            const idCell = row.cells[0];
            if (idCell && idCell.textContent.trim() === instanceId.toString()) {
                row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                row.classList.add('table-primary');
                setTimeout(() => {
                    row.classList.remove('table-primary');
                }, 3000);
            }
        });
    }, 500);
}

async function viewSubscriptionDetail(subscriptionId) {
    const token = localStorage.getItem("token");
    const modal = new bootstrap.Modal(document.getElementById('subscriptionDetailModal'));
    
    // 显示加载状态
    document.getElementById('subscription-detail-loading').style.display = 'block';
    document.getElementById('subscription-detail-content').style.display = 'none';
    document.getElementById('subscription-detail-error').style.display = 'none';
    
    modal.show();
    
    try {
        const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`, {
            headers: { 
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
            const data = result.data;
            
            // 填充基本信息
            safeSetTextContent('detail-user-email', data.user_email || 'N/A');
            
            // 设置状态徽章
            const statusElement = document.getElementById('detail-status');
            if (statusElement) {
                statusElement.innerHTML = `<span class="badge ${getSubscriptionBadgeClass(data.status)}">${data.status_display || data.status || 'N/A'}</span>`;
            }
            
            safeSetTextContent('detail-start-date', data.start_date ? formatDateTime(data.start_date) : 'N/A');
            safeSetTextContent('detail-end-date', data.end_date ? formatDateTime(data.end_date) : 'N/A');
            
            // 填充套餐信息
            if (data.plan_details) {
                safeSetTextContent('detail-plan-name', data.plan_details.name || 'N/A');
                safeSetTextContent('detail-plan-price', data.plan_details.price ? `¥${parseFloat(data.plan_details.price).toFixed(2)}` : 'N/A');
                safeSetTextContent('detail-plan-days', data.plan_details.days ? `${data.plan_details.days} 天` : 'N/A');
                safeSetTextContent('detail-max-devices', data.plan_details.max_devices || 'N/A');
            } else {
                safeSetTextContent('detail-plan-name', 'N/A');
                safeSetTextContent('detail-plan-price', 'N/A');
                safeSetTextContent('detail-plan-days', 'N/A');
                safeSetTextContent('detail-max-devices', 'N/A');
            }
            
            // 填充支付信息
            const paymentContent = document.getElementById('payment-details-content');
            if (paymentContent) {
                if (data.payment_details) {
                    const payment = data.payment_details;
                    let paymentHtml = `
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">订单号</label>
                                <div class="fw-semibold">${payment.order_id || 'N/A'}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">支付状态</label>
                                <div class="fw-semibold">
                                    <span class="badge ${getPaymentBadgeClass(payment.status)}">${getPaymentStatusDisplay(payment.status)}</span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">支付金额</label>
                                <div class="fw-semibold">${payment.amount ? `¥${parseFloat(payment.amount).toFixed(2)}` : 'N/A'}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">支付方式</label>
                                <div class="fw-semibold">${payment.payment_method || 'N/A'}</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">支付平台ID</label>
                                <div class="fw-semibold text-break">${payment.payment_id || 'N/A'}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">交易号</label>
                                <div class="fw-semibold text-break">${payment.transaction_id || 'N/A'}</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">创建时间</label>
                                <div class="fw-semibold">${payment.created_at ? formatDateTime(payment.created_at) : 'N/A'}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="text-muted small">支付时间</label>
                                <div class="fw-semibold">${payment.paid_at ? formatDateTime(payment.paid_at) : 'N/A'}</div>
                            </div>
                        </div>
                    `;
                    
                    if (payment.remarks) {
                        paymentHtml += `
                            <div class="row">
                                <div class="col-12">
                                    <label class="text-muted small">备注</label>
                                    <div class="fw-semibold">${payment.remarks}</div>
                                </div>
                            </div>
                        `;
                    }
                    
                    paymentContent.innerHTML = paymentHtml;
                } else {
                    paymentContent.innerHTML = `
                        <div class="text-center text-muted py-3">
                            <i class="bi bi-inbox fs-3"></i>
                            <p class="mt-2">暂无支付信息</p>
                        </div>
                    `;
                }
            }
            
            // 隐藏加载状态，显示内容
            document.getElementById('subscription-detail-loading').style.display = 'none';
            document.getElementById('subscription-detail-content').style.display = 'block';
            
        } else {
            throw new Error(result.message || '获取订阅详情失败');
        }
        
    } catch (error) {
        console.error('Error fetching subscription detail:', error);
        document.getElementById('subscription-detail-loading').style.display = 'none';
        document.getElementById('subscription-detail-error').style.display = 'block';
        document.getElementById('subscription-detail-error-message').textContent = error.message || '加载订阅详情时出错';
    }
}

// 获取支付状态徽章样式
function getPaymentBadgeClass(status) {
    switch (status) {
        case 'paid':
            return 'bg-success';
        case 'pending':
            return 'bg-warning';
        case 'cancelled':
        case 'refunded':
            return 'bg-secondary';
        case 'error':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// 获取支付状态显示文本
function getPaymentStatusDisplay(status) {
    const statusMap = {
        'pending': '待支付',
        'paid': '已支付',
        'cancelled': '已取消',
        'refunded': '已退款',
        'error': '支付失败'
    };
    return statusMap[status] || status;
}

async function handleDeleteSubscription(subscriptionId, userEmail) {
    if (!confirm(`确定要删除用户 "${userEmail}" 的这个订阅吗？此操作不可恢复。`)) {
    return;
  }
  const token = localStorage.getItem("token");
  if (!token) {
    showToast('用户未登录或会话已过期', 'danger');
    return;
  }

    // 通常，删除按钮本身可以用来显示加载状态，但这里没有直接传递按钮元素
    // 可以考虑在调用处传递按钮，或者在表格行上显示一个覆盖的加载指示

    try {
        const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`, {
            method: 'DELETE',
    headers: {
      // 'X-CSRFToken': csrfToken // 确保csrfToken已定义并获取 -> Removed CSRF Token
      'Authorization': `Bearer ${token}` // Use Bearer token for auth
    }
        });
        if (handleUnauthorizedAdminResponse(response)) return;
        const result = await response.json();
        if (result.success) {
            showToast('订阅已成功删除。', 'success');
      loadSubscriptions(); // 刷新列表
    } else {
            showToast(`删除失败: ${result.message || '未知错误'}`, 'danger');
    }
    } catch (error) {
        showToast(`删除订阅时发生错误: ${error.message}`, 'danger');
        console.error('Error deleting subscription:', error);
    }
}
// ... existing code ...

// toLocalISOString 和 toUTCISOString 已在 utils.js 中定义为全局函数

// 新增：调用删除用户API的函数
async function callDeleteUserApi(userId, buttonElement) { // Changed to async
  const token = localStorage.getItem("token");
  setButtonLoading(buttonElement, true, '<span class="spinner-border spinner-border-sm"></span>');

  try {
    const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
    });
    
    if (handleUnauthorizedAdminResponse(response)) return; // Check for 401 after fetch

    // Try to parse JSON for all responses to get potential error messages
    const data = await response.json().catch(() => ({ message: `Server error: ${response.statusText} (No JSON body)` }));

    setButtonLoading(buttonElement, false); 

    if (response.ok && data.success) { 
      showToast(data.message || "用户已成功删除", "success");
      loadUsers(); 
    } else {
      showToast(data.message || `删除用户失败 (状态码: ${response.status})`, "danger");
    }
  } catch (error) {
    setButtonLoading(buttonElement, false); 
    console.error("删除用户时出错:", error);
    showToast(`删除用户时发生网络或解析错误: ${error.message}`, "danger");
  }
}

// --- 新增：订阅实例 (车次管理) 相关函数 ---

// 加载订阅实例列表
async function loadSubscriptionInstances() {
  const token = localStorage.getItem("token");
  const tableBodyId = "subscription-instances-table-body";
  const colspan = 9; 

  showTableLoading(tableBodyId, true, colspan);

  try {
    const response = await fetch("/api/admin/subscription-instances", {
      headers: { Authorization: `Bearer ${token}` },
    });

    if (handleUnauthorizedAdminResponse(response)) return; 

    const data = await response.json();

    if (response.ok && data.success && data.data && data.data.subscription_instances) {
      const rawInstances = data.data.subscription_instances;
      allSubscriptionInstances = rawInstances.map(instance => {
        let augmentedInstance = { ...instance }; // Create a copy
        if (augmentedInstance.subscription_type_id) {
          if (allSubscriptionTypes && allSubscriptionTypes.length > 0) {
            const subType = allSubscriptionTypes.find(st => st.id === augmentedInstance.subscription_type_id);
            if (subType) {
              augmentedInstance.subscription_type_name = subType.name;
            } else {
              // If backend sent a name, it might already be here. Otherwise, it's an unknown ID.
              augmentedInstance.subscription_type_name = augmentedInstance.subscription_type_name || `未知类型 (ID: ${augmentedInstance.subscription_type_id})`;
            }
          } else {
            // allSubscriptionTypes not loaded yet or empty. Preserve backend name or use placeholder.
            augmentedInstance.subscription_type_name = augmentedInstance.subscription_type_name || `(类型加载中 ID: ${augmentedInstance.subscription_type_id})`;
          }
        } else if (!augmentedInstance.subscription_type_name) {
          // No ID and no name from backend.
          augmentedInstance.subscription_type_name = '未关联类型';
        }
        // If backend provided a subscription_type_name and there's no ID, it's preserved.
        // If there was an ID, it would have been overwritten by lookup if successful.
        return augmentedInstance;
      });
      updateSubscriptionInstancesTable(allSubscriptionInstances);
      announce(`${allSubscriptionInstances.length}个订阅实例已加载`);
    } else {
      allSubscriptionInstances = [];
      updateSubscriptionInstancesTable([]); 
      showToast(data.message || "获取订阅实例数据失败", "warning");
      announce(data.message || "没有订阅实例数据");
    }
  } catch (error) {
    console.error("Error loading subscription instances:", error);
    allSubscriptionInstances = [];
    updateSubscriptionInstancesTable([]); 
    showToast(`加载订阅实例失败: ${error.message}`, "danger");
    announce("加载订阅实例失败");
  } finally {
    showTableLoading(tableBodyId, false, colspan);
  }
}

// 加载订阅实例到选择框
function loadSubscriptionInstancesForSelect(selectId) {
  const token = localStorage.getItem("token");
  const selectElement = document.getElementById(selectId);
  
  if (!selectElement) return;
  
  // 显示加载状态
  selectElement.innerHTML = '<option value="">加载中...</option>';
  selectElement.disabled = true;
  
  fetch("/api/admin/subscription-instances", {
    headers: { Authorization: `Bearer ${token}` }
  })
    .then(response => {
      if (!response.ok) throw new Error("获取订阅实例失败");
      return response.json();
    })
    .then(data => {
      selectElement.disabled = false;
      selectElement.innerHTML = '<option value="">-- 请选择订阅实例 --</option>';
      
      if (data.success && data.data && data.data.subscription_instances) {
        data.data.subscription_instances.forEach(instance => {
          if (instance.is_active) { // 只显示活跃的实例
            const option = document.createElement("option");
            option.value = instance.id;
            option.textContent = `${instance.name} (${instance.subscription_type_name || '未知类型'}, 容量: ${instance.active_users_count || 0}/${instance.capacity})`;
            selectElement.appendChild(option);
          }
        });
      }
    })
    .catch(error => {
      console.error("加载订阅实例失败:", error);
      selectElement.disabled = false;
      selectElement.innerHTML = '<option value="">加载失败，请刷新重试</option>';
    });
}

// 更新订阅实例表格
function updateSubscriptionInstancesTable(instances) {
  const tableBody = document.getElementById("subscription-instances-table-body");
  if (!tableBody) return;
  tableBody.innerHTML = ""; 

  if (!instances || instances.length === 0) {
    const row = tableBody.insertRow();
    const cell = row.insertCell();
    cell.colSpan = 10; // Updated colspan to include AdsPower accounts column
    cell.textContent = "没有找到订阅实例信息。";
    cell.className = "text-center";
    return;
  }

  instances.forEach((instance) => {
    const row = tableBody.insertRow();
    row.insertCell().textContent = instance.id;
    row.insertCell().textContent = instance.name || "N/A";
    
    // subscription_type_name is now pre-populated by loadSubscriptionInstances
    row.insertCell().textContent = instance.subscription_type_name || 'N/A'; 
    
    row.insertCell().textContent = instance.capacity;
    
    // 显示用户数量信息（活跃/过期）
    const usersCell = row.insertCell();
    if (instance.active_users_count !== undefined && instance.expired_users_count !== undefined) {
      // 如果有过期用户，显示更详细的信息
      if (instance.expired_users_count > 0) {
        usersCell.innerHTML = `
          <span class="text-success">${instance.active_users_count}</span>
          <span class="text-muted">/</span>
          <span class="text-danger" title="过期用户">${instance.expired_users_count}</span>
        `;
      } else {
        usersCell.textContent = instance.active_users_count;
      }
    } else {
      // 兼容旧版本
      usersCell.textContent = instance.active_users_count !== undefined ? instance.active_users_count : "N/A";
    } 
    
    // 添加AdsPower账号数量列
    const adspowerCount = instance.adspower_accounts_count !== undefined ? instance.adspower_accounts_count : 0;
    row.insertCell().textContent = adspowerCount;
    
    const statusCell = row.insertCell();
    const statusBadge = document.createElement('span');
    // Assuming backend provides is_active for instances similar to model
    statusBadge.className = `badge ${instance.is_active ? 'bg-success' : 'bg-secondary'}`; 
    statusBadge.textContent = instance.is_active ? '活跃' : '禁用'; 
    statusCell.appendChild(statusBadge);

    row.insertCell().textContent = instance.description || ""; 
    row.insertCell().textContent = instance.created_at ? formatDateTime(instance.created_at) : "N/A";

    const actionsCell = row.insertCell();
    actionsCell.className = "text-nowrap";
    actionsCell.innerHTML = `
      <button class="btn btn-sm btn-outline-info me-1 view-instance-btn" data-instance-id="${instance.id}" title="查看详情">
        <i class="bi bi-eye"></i> 详情
      </button>
      <button class="btn btn-sm btn-outline-primary me-1 edit-instance-btn" data-instance-id="${instance.id}" title="编辑实例">
        <i class="bi bi-pencil-square"></i> 编辑
      </button>
      <button class="btn btn-sm btn-outline-danger delete-instance-btn" data-instance-id="${instance.id}" data-instance-name="${instance.name || '该实例'}" title="删除实例">
        <i class="bi bi-trash"></i> 删除
      </button>
    `;
    actionsCell.querySelector(".view-instance-btn").addEventListener("click", (e) => {
      viewInstanceDetails(e.currentTarget.dataset.instanceId);
    });
    actionsCell.querySelector(".edit-instance-btn").addEventListener("click", (e) => {
      openEditInstanceModal(e.currentTarget.dataset.instanceId);
    });
    actionsCell.querySelector(".delete-instance-btn").addEventListener("click", (e) => {
      handleDeleteSubscriptionInstance(e.currentTarget.dataset.instanceId, e.currentTarget.dataset.instanceName, e.currentTarget);
    });
  });
  if (instances.length > 0) {
    announce(`订阅实例列表已更新，共 ${instances.length} 条记录。`);
  }
}

// 处理添加订阅实例
async function handleAddSubscriptionInstance() {
  const form = document.getElementById("add-instance-form");
  if (!form) {
    showToast("表单元素不存在", "danger");
    return;
  }

  if (!form.checkValidity()) {
    form.classList.add("was-validated");
    showToast("请检查表单中的错误。", "warning");
    return;
  }
  form.classList.remove("was-validated");

  const name = document.getElementById("add-instance-name").value;
  // Value from select is already subscription_type_id
  const subscriptionTypeId = document.getElementById("add-instance-subscription-type").value;
  const capacity = parseInt(document.getElementById("add-instance-capacity").value, 10);
  // Assuming the status select for add instance also correctly reflects is_active
  const isActive = document.getElementById("add-instance-status").value === 'active'; 
  const description = document.getElementById("add-instance-description").value;

  if (!subscriptionTypeId) {
      showToast("请选择一个订阅类型。", "warning");
      document.getElementById("add-instance-subscription-type").classList.add("is-invalid");
      return;
  }
  document.getElementById("add-instance-subscription-type").classList.remove("is-invalid");

  const payload = {
    name: name || null, 
    subscription_type_id: parseInt(subscriptionTypeId), // Ensure it's an int
    capacity: capacity,
    is_active: isActive, 
    description: description,
  };

  const saveButton = document.getElementById("save-instance-btn");
  setButtonLoading(saveButton, true);

  const token = localStorage.getItem("token");
  if (!token) {
    showToast("用户未登录或会话已过期", "danger");
    setButtonLoading(saveButton, false);
    return;
  }

  try {
    const response = await fetch("/api/admin/subscription-instances", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (handleUnauthorizedAdminResponse(response)) { 
        setButtonLoading(saveButton, false);
        return;
    }

    const result = await response.json();
    if (response.ok && result.success) {
      showToast("订阅实例添加成功！", "success");
      const modal = bootstrap.Modal.getInstance(document.getElementById("addInstanceModal"));
      if (modal) modal.hide();
      form.reset(); 
      loadSubscriptionInstances(); 
    } else {
      showToast(`添加失败: ${result.message || "未知错误"}`, "danger");
    }
  } catch (error) {
    showToast(`添加订阅实例时发生错误: ${error.message}`, "danger");
    console.error("Error adding subscription instance:", error);
  } finally {
    setButtonLoading(saveButton, false);
  }
}

// 打开编辑订阅实例模态框
async function openEditInstanceModal(instanceId) {
  const modalElement = document.getElementById("editInstanceModal");
  if (!modalElement) {
    showToast("UI错误：找不到编辑实例模态框元素", "danger");
    return;
  }
  const modal = new bootstrap.Modal(modalElement);
  const form = document.getElementById("edit-instance-form");
  if (form) {
      form.classList.remove("was-validated");
      form.reset();
  }

  const token = localStorage.getItem("token");
  if (!token) {
    showToast("用户未登录或会话已过期", "danger");
    return;
  }

  // Ensure subscription types are loaded and populate the specific select for this modal
  const typeSelect = document.getElementById('edit-instance-subscription-type');
  if (typeSelect) {
      typeSelect.innerHTML = '<option value="">-- 请选择订阅类型 --</option>'; 
      if (allSubscriptionTypes && allSubscriptionTypes.length > 0) {
          allSubscriptionTypes.forEach(type => {
              const option = document.createElement('option');
              option.value = type.id;
              option.textContent = type.name;
              typeSelect.appendChild(option);
          });
      } else {
         await loadSubscriptionTypes(); // This will populate allSubscriptionTypes and call populateSubscriptionTypeSelects
         // Re-populate after load if allSubscriptionTypes is now available and select wasn't populated by broad call
         if (allSubscriptionTypes && allSubscriptionTypes.length > 0 && typeSelect.options.length <= 1) {
            allSubscriptionTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = type.name;
                typeSelect.appendChild(option);
            });
         }
      }
  } else {
      console.error("Select element #edit-instance-subscription-type not found for openEditInstanceModal");
  }

  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}`, {
      method: "GET",
      headers: { Authorization: `Bearer ${token}` },
    });

    if (handleUnauthorizedAdminResponse(response)) return;

    const result = await response.json();

    if (response.ok && result.success && result.data) {
      const instance = result.data.instance || result.data; // 处理嵌套的instance数据结构
            
      if (instance && typeof instance.id !== 'undefined') { 
        document.getElementById("edit-instance-id").value = instance.id;
        document.getElementById("edit-instance-name").value = instance.name || '';
        if(typeSelect) typeSelect.value = instance.subscription_type_id || ''; 
        document.getElementById("edit-instance-capacity").value = instance.capacity !== undefined ? instance.capacity : '';
        document.getElementById("edit-instance-is-active").checked = instance.is_active === true;
        document.getElementById("edit-instance-description").value = instance.description || "";
        modal.show();
      } else {
        showToast(`获取实例详情失败: 数据结构不正确或未找到实例数据。`, "danger");
      }
    } else {
      showToast(`获取实例详情失败: ${result.message || "响应格式不正确或请求未成功"}`, "danger");
    }
  } catch (error) {
    showToast(`打开编辑实例模态框时发生错误: ${error.message}`, "danger");
    console.error("Error opening edit instance modal:", error);
  }
}

// 处理更新订阅实例
async function handleUpdateSubscriptionInstance() {
  const form = document.getElementById("edit-instance-form");
   if (!form) {
    showToast("表单元素不存在", "danger");
    return;
  }

  if (!form.checkValidity()) {
    form.classList.add("was-validated");
    showToast("请检查表单中的错误。", "warning");
    return;
  }
  form.classList.remove("was-validated");

  const instanceId = document.getElementById("edit-instance-id").value;
  const name = document.getElementById("edit-instance-name").value;
  // Value from select is already subscription_type_id
  const subscriptionTypeId = document.getElementById("edit-instance-subscription-type").value;
  const capacity = parseInt(document.getElementById("edit-instance-capacity").value, 10);
  const isActive = document.getElementById("edit-instance-is-active").checked;
  const description = document.getElementById("edit-instance-description").value;

  if (!subscriptionTypeId) {
      showToast("请为实例选择一个订阅类型。", "warning");
      document.getElementById("edit-instance-subscription-type").classList.add("is-invalid");
      return;
  }
  document.getElementById("edit-instance-subscription-type").classList.remove("is-invalid");


  const payload = {
    name: name,
    subscription_type_id: parseInt(subscriptionTypeId), // Ensure it's an int 
    capacity: capacity,
    is_active: isActive,
    description: description,
  };

  const updateButton = document.getElementById("update-instance-btn");
  setButtonLoading(updateButton, true);

  const token = localStorage.getItem("token");
  if (!token) {
    showToast("用户未登录或会话已过期", "danger");
    setButtonLoading(updateButton, false);
    return;
  }

  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (handleUnauthorizedAdminResponse(response)) { 
        setButtonLoading(updateButton, false);
        return;
    }

    const result = await response.json();
    if (response.ok && result.success) {
      showToast("订阅实例更新成功！", "success");
      const modal = bootstrap.Modal.getInstance(document.getElementById("editInstanceModal"));
      if (modal) modal.hide();
      loadSubscriptionInstances(); 
    } else {
      showToast(`更新失败: ${result.message || "未知错误"}`, "danger");
    }
  } catch (error) {
    showToast(`更新订阅实例时发生错误: ${error.message}`, "danger");
    console.error("Error updating subscription instance:", error);
  } finally {
    setButtonLoading(updateButton, false);
  }
}

// 处理删除订阅实例
async function handleDeleteSubscriptionInstance(instanceId, instanceName, buttonElement) {
  if (!confirm(`确定要删除订阅实例 "${instanceName}" (ID: ${instanceId}) 吗？此操作不可恢复。`)) {
    return;
  }

  const token = localStorage.getItem("token");
  if (!token) {
    showToast("用户未登录或会话已过期", "danger");
    return;
  }

  if (buttonElement) setButtonLoading(buttonElement, true);

  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}`, {
      method: "DELETE",
      headers: { Authorization: `Bearer ${token}` },
    });

    if (handleUnauthorizedAdminResponse(response)) { // 401处理
        if (buttonElement) setButtonLoading(buttonElement, false);
        return;
    }
    
    const result = await response.json().catch(() => ({ message: `请求失败，状态码: ${response.status}` }));

    if (response.ok && result.success) {
      showToast("订阅实例已成功删除。", "success");
      loadSubscriptionInstances(); 
    } else {
      showToast(`删除失败: ${result.message || "未知错误"}`, "danger");
    }
  } catch (error) {
    showToast(`删除订阅实例时发生错误: ${error.message}`, "danger");
    console.error("Error deleting subscription instance:", error);
  } finally {
    if (buttonElement) setButtonLoading(buttonElement, false);
  }
}
// 查看订阅实例详情
async function viewInstanceDetails(instanceId) {
  const token = localStorage.getItem("token");
  if (!token) {
    showToast("用户未登录或会话已过期", "danger");
    return;
  }

  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}`, {
      method: "GET",
      headers: { Authorization: `Bearer ${token}` },
    });

    if (handleUnauthorizedAdminResponse(response)) return;

    const result = await response.json();

    if (response.ok && result.success && result.data && result.data.instance) {
      const instance = result.data.instance;
      
      // 填充模态框数据
      document.getElementById('view-instance-id').textContent = instance.id;
      document.getElementById('view-instance-name').textContent = instance.name || 'N/A';
      document.getElementById('view-instance-type').textContent = instance.subscription_type_name || 'N/A';
      document.getElementById('view-instance-capacity').textContent = instance.capacity;
      // 显示用户数量（活跃/过期）
      const currentUsersElement = document.getElementById('view-instance-current-users');
      if (instance.active_users_count !== undefined && instance.expired_users_count !== undefined) {
        if (instance.expired_users_count > 0) {
          currentUsersElement.innerHTML = `
            <span class="text-success">${instance.active_users_count} 活跃</span>
            <span class="text-muted"> / </span>
            <span class="text-danger">${instance.expired_users_count} 过期</span>
            <br>
            <small class="text-muted">可用槽位: ${instance.available_slots || 0}</small>
          `;
        } else {
          currentUsersElement.innerHTML = `${instance.active_users_count} <small class="text-muted">(可用: ${instance.available_slots || 0})</small>`;
        }
      } else {
        currentUsersElement.textContent = instance.active_users_count || 0;
      }
      
      // 状态
      const statusElement = document.getElementById('view-instance-status');
      statusElement.innerHTML = `<span class="badge ${instance.is_active ? 'bg-success' : 'bg-secondary'}">${instance.is_active ? '活跃' : '禁用'}</span>`;
      
      document.getElementById('view-instance-description').textContent = instance.description || 'N/A';
      document.getElementById('view-instance-created-at').textContent = instance.created_at ? formatDateTime(instance.created_at) : 'N/A';
      
      // AdsPower账号列表
      const adspowerCount = instance.adspower_accounts ? instance.adspower_accounts.length : 0;
      document.getElementById('instance-adspower-count').textContent = adspowerCount;
      
      const adspowerTableBody = document.getElementById('instance-adspower-table-body');
      adspowerTableBody.innerHTML = '';
      
      if (instance.adspower_accounts && instance.adspower_accounts.length > 0) {
        instance.adspower_accounts.forEach(account => {
          const row = adspowerTableBody.insertRow();
          row.insertCell().textContent = account.id;
          row.insertCell().textContent = account.username;
          // 显示设备数（活跃/过期）
          const devicesCell = row.insertCell();
          if (account.active_devices !== undefined && account.expired_devices !== undefined) {
            if (account.expired_devices > 0) {
              devicesCell.innerHTML = `
                <span class="text-success">${account.active_devices}</span>/<span class="text-danger">${account.expired_devices}</span>
                <span class="text-muted">/${account.max_devices || 0}</span>
              `;
            } else {
              devicesCell.textContent = `${account.active_devices || 0}/${account.max_devices || 0}`;
            }
          } else {
            devicesCell.textContent = `${account.current_devices || 0}/${account.max_devices || 0}`;
          }
          
          const statusCell = row.insertCell();
          statusCell.innerHTML = `<span class="badge ${account.is_active ? 'bg-success' : 'bg-secondary'}">${account.is_active ? '正常' : '禁用'}</span>`;
          
          const actionsCell = row.insertCell();
          actionsCell.innerHTML = `
            <button class="btn btn-sm btn-outline-danger" onclick="removeAdspowerFromInstance(${instanceId}, ${account.id}, '${account.username}')">
              <i class="bi bi-x-circle"></i> 移除
            </button>
          `;
        });
      } else {
        const row = adspowerTableBody.insertRow();
        const cell = row.insertCell();
        cell.colSpan = 5;
        cell.textContent = '暂无关联的AdsPower账号';
        cell.className = 'text-center';
      }
      
      // 用户列表
      const usersCount = instance.users ? instance.users.length : 0;
      document.getElementById('instance-users-count').textContent = usersCount;
      
      const usersTableBody = document.getElementById('instance-users-table-body');
      usersTableBody.innerHTML = '';
      
      if (instance.users && instance.users.length > 0) {
        instance.users.forEach(user => {
          const row = usersTableBody.insertRow();
          row.insertCell().textContent = user.user_id;
          row.insertCell().textContent = user.user_email || 'N/A';
          row.insertCell().textContent = user.start_date ? formatDateTime(user.start_date) : 'N/A';
          row.insertCell().textContent = user.end_date ? formatDateTime(user.end_date) : 'N/A';
          
          const statusCell = row.insertCell();
          const isActive = new Date(user.end_date) > new Date();
          statusCell.innerHTML = `<span class="badge ${isActive ? 'bg-success' : 'bg-secondary'}">${isActive ? '活跃' : '已过期'}</span>`;
        });
      } else {
        const row = usersTableBody.insertRow();
        const cell = row.insertCell();
        cell.colSpan = 5;
        cell.textContent = '暂无用户';
        cell.className = 'text-center';
      }
      
      // 设置管理按钮的事件
      const manageBtn = document.getElementById('manage-instance-adspower-btn');
      manageBtn.onclick = () => openManageInstanceAdspowerModal(instanceId, instance.name);
      
      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('viewInstanceDetailsModal'));
      modal.show();
      
    } else {
      showToast(`获取实例详情失败: ${result.message || "响应格式不正确"}`, "danger");
    }
  } catch (error) {
    showToast(`查看订阅实例详情时发生错误: ${error.message}`, "danger");
    console.error("Error viewing instance details:", error);
  }
}

// 打开管理实例AdsPower账号模态框
async function openManageInstanceAdspowerModal(instanceId, instanceName) {
  const token = localStorage.getItem("token");
  
  // 设置实例信息
  document.getElementById('manage-instance-name').textContent = instanceName || 'N/A';
  
  // 存储当前实例ID，供后续操作使用
  window.currentManageInstanceId = instanceId;
  
  try {
    // 获取实例详情以获取当前关联的账号
    const instanceResponse = await fetch(`/api/admin/subscription-instances/${instanceId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (!instanceResponse.ok) throw new Error("获取实例详情失败");
    
    const instanceData = await instanceResponse.json();
    const currentAccounts = instanceData.data.instance.adspower_accounts || [];
    const currentAccountIds = currentAccounts.map(acc => acc.id);
    
    document.getElementById('manage-instance-account-count').textContent = currentAccounts.length;
    
    // 填充当前关联的账号列表
    const currentAccountsTableBody = document.getElementById('manage-instance-adspower-table-body');
    currentAccountsTableBody.innerHTML = '';
    
    if (currentAccounts.length > 0) {
      currentAccounts.forEach(account => {
        const row = currentAccountsTableBody.insertRow();
        row.insertCell().textContent = account.id;
        row.insertCell().textContent = account.username;
        
        const statusCell = row.insertCell();
        statusCell.innerHTML = `<span class="badge ${account.is_active ? 'bg-success' : 'bg-secondary'}">${account.is_active ? '正常' : '禁用'}</span>`;
        
        const actionsCell = row.insertCell();
        actionsCell.innerHTML = `
          <button class="btn btn-sm btn-outline-danger" onclick="removeAdspowerFromInstanceInModal(${instanceId}, ${account.id}, '${account.username}')">
            <i class="bi bi-x-circle"></i> 移除
          </button>
        `;
      });
    } else {
      const row = currentAccountsTableBody.insertRow();
      const cell = row.insertCell();
      cell.colSpan = 4;
      cell.textContent = '暂无关联的账号';
      cell.className = 'text-center';
    }
    
    // 获取所有AdsPower账号
    const allAccountsResponse = await fetch("/api/admin/accounts/adspower", {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (!allAccountsResponse.ok) throw new Error("获取AdsPower账号列表失败");
    
    const allAccountsData = await allAccountsResponse.json();
    const allAccounts = allAccountsData.data.accounts || [];
    
    // 填充可选账号列表（排除已关联的）
    const selectElement = document.getElementById('add-adspower-to-instance-select');
    selectElement.innerHTML = '';
    
    const availableAccounts = allAccounts.filter(acc => !currentAccountIds.includes(acc.id) && acc.is_active);
    
    if (availableAccounts.length > 0) {
      availableAccounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.username} (ID: ${account.id}, 设备: ${account.current_devices || 0}/${account.max_devices})`;
        selectElement.appendChild(option);
      });
    } else {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = '没有可用的账号';
      option.disabled = true;
      selectElement.appendChild(option);
    }
    
    // 在显示新模态框之前，先检查是否有现存的模态框实例
    const modalElement = document.getElementById('manageInstanceAdspowerModal');
    let modal = bootstrap.Modal.getInstance(modalElement);
    
    if (modal) {
      // 如果已经有实例，先销毁它
      modal.dispose();
    }
    
    // 创建新的模态框实例并显示
    modal = new bootstrap.Modal(modalElement);
    
    // 添加隐藏事件监听器，确保遮罩被正确清理
    modalElement.addEventListener('hidden.bs.modal', function() {
      // 延迟清理，确保动画完成
      setTimeout(() => {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
          if (backdrop && !document.querySelector('.modal.show')) {
            backdrop.remove();
          }
        });
        // 确保body的modal-open类被正确处理
        if (!document.querySelector('.modal.show')) {
          document.body.classList.remove('modal-open');
          document.body.style.removeProperty('overflow');
          document.body.style.removeProperty('padding-right');
        }
      }, 150);
    }, { once: true }); // 使用once选项，确保事件只触发一次
    
    modal.show();
    
  } catch (error) {
    showToast(`加载管理界面失败: ${error.message}`, 'danger');
    console.error('Error opening manage modal:', error);
  }
}

// 添加AdsPower账号到实例
async function addAdspowerToInstance() {
  const token = localStorage.getItem("token");
  const instanceId = window.currentManageInstanceId;
  const selectElement = document.getElementById('add-adspower-to-instance-select');
  const selectedAccountIds = Array.from(selectElement.selectedOptions).map(opt => parseInt(opt.value));
  
  if (selectedAccountIds.length === 0) {
    showToast('请选择至少一个账号', 'warning');
    return;
  }
  
  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}/adspower-accounts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ account_ids: selectedAccountIds })
    });
    
    const result = await response.json();
    
    if (response.ok) {
      // 处理响应结果
      if (result.data && result.data.success_count > 0) {
        // 有账号成功添加
        let message = `成功添加 ${result.data.success_count} 个账号`;
        
        // 如果有失败的账号，添加失败信息
        if (result.data.failed_accounts && result.data.failed_accounts.length > 0) {
          message += `\n失败 ${result.data.failed_accounts.length} 个`;
          
          const failedDetails = result.data.failed_accounts.map(account => {
            return `${account.username || `ID: ${account.id}`}: ${account.reason}`;
          }).join('\n');
          
          message += '\n\n失败详情:\n' + failedDetails;
          showToast(message, 'warning', 8000); // 部分成功用warning
        } else {
          showToast(message, 'success');
        }
        
        // 刷新管理模态框
        openManageInstanceAdspowerModal(instanceId, document.getElementById('manage-instance-name').textContent);
        // 刷新实例列表
        loadSubscriptionInstances();
      } else {
        // 全部失败
        let errorMessage = result.message || '添加失败';
        
        // 如果有失败的账号详情，显示详细信息
        if (result.data && result.data.failed_accounts && result.data.failed_accounts.length > 0) {
          const failedDetails = result.data.failed_accounts.map(account => {
            return `${account.username || `ID: ${account.id}`}: ${account.reason}`;
          }).join('\n');
          
          errorMessage += '\n\n失败详情:\n' + failedDetails;
        }
        
        showToast(errorMessage, 'danger', 8000); // 显示8秒，让用户有时间阅读
      }
    } else {
      // HTTP错误
      showToast(result.message || '请求失败', 'danger');
    }
  } catch (error) {
    showToast(`添加账号时发生错误: ${error.message}`, 'danger');
    console.error('Error adding accounts:', error);
  }
}

// 从实例中移除AdsPower账号（在详情模态框中）
async function removeAdspowerFromInstance(instanceId, accountId, accountUsername) {
  if (!confirm(`确定要从此实例中移除账号 "${accountUsername}" 吗？`)) {
    return;
  }
  
  const token = localStorage.getItem("token");
  
  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}/adspower-accounts/${accountId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      showToast('账号已移除', 'success');
      // 刷新详情模态框
      viewInstanceDetails(instanceId);
      // 刷新实例列表
      loadSubscriptionInstances();
    } else {
      showToast(result.message || '移除失败', 'danger');
    }
  } catch (error) {
    showToast(`移除账号时发生错误: ${error.message}`, 'danger');
    console.error('Error removing account:', error);
  }
}

// 从实例中移除AdsPower账号（在管理模态框中）
async function removeAdspowerFromInstanceInModal(instanceId, accountId, accountUsername) {
  if (!confirm(`确定要从此实例中移除账号 "${accountUsername}" 吗？`)) {
    return;
  }
  
  const token = localStorage.getItem("token");
  
  try {
    const response = await fetch(`/api/admin/subscription-instances/${instanceId}/adspower-accounts/${accountId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      showToast('账号已移除', 'success');
      // 刷新管理模态框
      openManageInstanceAdspowerModal(instanceId, document.getElementById('manage-instance-name').textContent);
      // 刷新实例列表
      loadSubscriptionInstances();
    } else {
      showToast(result.message || '移除失败', 'danger');
    }
  } catch (error) {
    showToast(`移除账号时发生错误: ${error.message}`, 'danger');
    console.error('Error removing account:', error);
  }
}

// --- 订阅实例 (车次管理) 相关函数结束 ---

// --- 分配订阅相关函数开始 ---

// 打开分配订阅模态框
function openAssignSubscriptionModal(userId, userEmail) {
  // 设置用户信息
  document.getElementById('assign-subscription-user-id').textContent = userId;
  document.getElementById('assign-subscription-user-email').textContent = userEmail;
  
  // 重置表单
  document.getElementById('assignSubscriptionForm').reset();
  document.getElementById('assign-subscription-instance').innerHTML = '<option value="">请先选择订阅类型...</option>';
  
  // 加载订阅类型
  loadSubscriptionTypesForAssign();
  
  // 显示模态框
  const modal = new bootstrap.Modal(document.getElementById('assignSubscriptionModal'));
  modal.show();
}

// 加载订阅类型供分配使用
async function loadSubscriptionTypesForAssign() {
  const token = localStorage.getItem("token");
  const selectElement = document.getElementById('assign-subscription-type');
  
  try {
    const response = await fetch('/api/admin/subscription-types', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (!response.ok) throw new Error('获取订阅类型失败');
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.subscription_types) {
      selectElement.innerHTML = '<option value="">请选择订阅类型...</option>';
      
      result.data.subscription_types.forEach(type => {
        const option = document.createElement('option');
        option.value = type.id;
        option.textContent = `${type.name} (${type.days}天, ¥${type.price})`;
        option.dataset.days = type.days;
        option.dataset.price = type.price;
        selectElement.appendChild(option);
      });
    }
  } catch (error) {
    showToast(`加载订阅类型失败: ${error.message}`, 'danger');
    console.error('Error loading subscription types:', error);
  }
}

// --- 分配订阅相关函数结束 ---

// --- 编辑用户中的订阅管理函数开始 ---

// 加载订阅类型供编辑用户使用
async function loadSubscriptionTypesForEdit(currentTypeId = null, currentSubscriptionInfo = null) {
  const token = localStorage.getItem("token");
  const selectElement = document.getElementById('edit-user-subscription-type');
  
  try {
    const response = await fetch('/api/admin/subscription-types', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (!response.ok) throw new Error('获取订阅类型失败');
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.subscription_types) {
      selectElement.innerHTML = '<option value="">请选择订阅类型...</option>';
      
      result.data.subscription_types.forEach(type => {
        const option = document.createElement('option');
        option.value = type.id;
        option.textContent = `${type.name} (${type.days}天, ¥${type.price})`;
        option.dataset.days = type.days;
        option.dataset.price = type.price;
        // 如果是当前订阅类型，设为选中
        if (currentTypeId && type.id === currentTypeId) {
          option.selected = true;
        }
        selectElement.appendChild(option);
      });
    }
  } catch (error) {
    showToast(`加载订阅类型失败: ${error.message}`, 'danger');
    console.error('Error loading subscription types:', error);
  }
}

// 加载订阅实例
async function loadSubscriptionInstancesForType(typeId, selectElement) {
  const token = localStorage.getItem("token");
  
  try {
    const response = await fetch(`/api/admin/subscription-types/${typeId}/instances`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (!response.ok) throw new Error('获取订阅实例失败');
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.instances) {
      selectElement.innerHTML = '<option value="">请选择订阅实例...</option>';
      
      result.data.instances.forEach(instance => {
        if (instance.is_active) {
          const option = document.createElement('option');
          option.value = instance.id;
          // 使用后端计算的可用槽位，如果没有则手动计算
          const availableSlots = instance.available_slots !== undefined ? 
            instance.available_slots : 
            (instance.capacity - (instance.current_users || 0));
          
          // 如果有过期用户，显示更详细的信息
          let optionText = `${instance.name} (剩余容量: ${availableSlots}/${instance.capacity})`;
          if (instance.expired_users_count && instance.expired_users_count > 0) {
            optionText += ` [过期: ${instance.expired_users_count}]`;
          }
          option.textContent = optionText;
          option.disabled = availableSlots <= 0;
          selectElement.appendChild(option);
        }
      });
      
      if (selectElement.options.length === 1) {
        selectElement.innerHTML = '<option value="">该类型暂无可用实例</option>';
      }
    }
  } catch (error) {
    showToast(`加载订阅实例失败: ${error.message}`, 'danger');
    console.error('Error loading subscription instances:', error);
  }
}

// --- 编辑用户中的订阅管理函数结束 ---
// 时间戳: 1748160290

// --- 设备审计功能开始 ---

// 全局变量
let currentAuditsPage = 1;
const auditsPerPage = 50;
let auditFilters = {};

// 加载设备审计日志
async function loadDeviceAudits(page = 1) {
  const token = localStorage.getItem("token");
  
  try {
    // 构建查询参数
    const params = new URLSearchParams({
      page: page,
      per_page: auditsPerPage,
      ...auditFilters
    });
    
    const response = await fetch(`/api/admin/device-audits?${params}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (handleUnauthorizedAdminResponse(response)) return;
    if (!response.ok) throw new Error('获取审计日志失败');
    
    const result = await response.json();
    
    if (result.success) {
      displayAudits(result.data.audits);
      setupAuditsPagination(result.data.page, result.data.total_pages);
      currentAuditsPage = page;
    } else {
      showToast(result.message || '获取审计日志失败', 'danger');
    }
  } catch (error) {
    showToast(`加载审计日志失败: ${error.message}`, 'danger');
    console.error('Error loading audits:', error);
  }
}

// 显示审计日志
function displayAudits(audits) {
  const tbody = document.getElementById('audits-tbody');
  if (!tbody) return;
  
  // 保存当前审计数据
  currentAuditsData = audits;
  
  tbody.innerHTML = '';
  
  if (audits.length === 0) {
    tbody.innerHTML = '<tr><td colspan="8" class="text-center">暂无审计记录</td></tr>';
    return;
  }
  
  audits.forEach(audit => {
    const row = document.createElement('tr');
    
    
    // 时间
    const timeCell = document.createElement('td');
    timeCell.textContent = formatDateTime(audit.created_at);
    row.appendChild(timeCell);
    
    // 用户
    const userCell = document.createElement('td');
    userCell.innerHTML = `<small>${audit.user_email}<br>ID: ${audit.user_id}</small>`;
    row.appendChild(userCell);
    
    // 操作类型
    const actionCell = document.createElement('td');
    actionCell.innerHTML = `<span class="badge ${getActionBadgeClass(audit.action)}">${getActionLabel(audit.action)}</span>`;
    row.appendChild(actionCell);
    
    // 操作来源
    const sourceCell = document.createElement('td');
    sourceCell.innerHTML = `<span class="badge ${getSourceBadgeClass(audit.action_source)}">${getSourceLabel(audit.action_source)}</span>`;
    row.appendChild(sourceCell);
    
    // 描述
    const descCell = document.createElement('td');
    descCell.textContent = audit.description || '-';
    row.appendChild(descCell);
    
    // IP地址
    const ipCell = document.createElement('td');
    ipCell.textContent = audit.ip_address || '-';
    row.appendChild(ipCell);
    
    // 浏览器信息
    const userAgentCell = document.createElement('td');
    // 截取显示部分浏览器信息，完整信息在详情中查看
    const userAgentText = audit.user_agent || '-';
    if (userAgentText !== '-' && userAgentText.length > 50) {
      userAgentCell.innerHTML = `<small class="text-muted" title="${userAgentText}">${userAgentText.substring(0, 50)}...</small>`;
    } else {
      userAgentCell.innerHTML = `<small class="text-muted">${userAgentText}</small>`;
    }
    row.appendChild(userAgentCell);
    
    // 操作按钮
    const actionCell2 = document.createElement('td');
    actionCell2.innerHTML = `
      <button class="btn btn-sm btn-info" onclick="viewAuditDetails(${audit.id})">
        <i class="bi bi-eye"></i> 详情
      </button>
    `;
    row.appendChild(actionCell2);
    
    tbody.appendChild(row);
  });
}

// 获取操作类型标签
function getActionLabel(action) {
  const labels = {
    'register': '添加设备',
    'delete': '删除设备',
    'logout': '设备登出',
    'verify_failed': '验证失败',
    'login': '设备登录',
    'verify_success': '验证成功',
    'update': '更新设备',
    'suspicious': '可疑操作'
  };
  return labels[action] || action;
}

// 获取操作类型徽章样式
function getActionBadgeClass(action) {
  const classes = {
    'register': 'bg-success',
    'delete': 'bg-danger',
    'login': 'bg-info',
    'logout': 'bg-secondary',
    'verify_failed': 'bg-warning',
    'suspicious': 'bg-danger'
  };
  return classes[action] || 'bg-primary';
}


// 获取操作来源标签
function getSourceLabel(source) {
  const labels = {
    'user': '用户',
    'admin': '管理员',
    'system': '系统'
  };
  return labels[source] || source || '未知';
}

// 获取操作来源徽章样式
function getSourceBadgeClass(source) {
  const classes = {
    'user': 'bg-primary',
    'admin': 'bg-warning',
    'system': 'bg-secondary'
  };
  return classes[source] || 'bg-light';
}

// 检测频繁操作并发出预警
function checkFrequentOperations(audits) {
  // 统计每个用户的操作
  const userOperations = {};
  const now = new Date();
  const oneHourAgo = new Date(now - 60 * 60 * 1000); // 1小时前
  const oneDayAgo = new Date(now - 24 * 60 * 60 * 1000); // 1天前
  
  audits.forEach(audit => {
    const auditTime = new Date(audit.created_at);
    if (!userOperations[audit.user_id]) {
      userOperations[audit.user_id] = {
        email: audit.user_email,
        hourlyOps: 0,
        dailyOps: 0,
        deviceOps: 0,
        actions: []
      };
    }
    
    // 统计1小时内的操作
    if (auditTime >= oneHourAgo) {
      userOperations[audit.user_id].hourlyOps++;
    }
    
    // 统计1天内的操作
    if (auditTime >= oneDayAgo) {
      userOperations[audit.user_id].dailyOps++;
      // 特别统计设备增删操作
      if (audit.action === 'register' || audit.action === 'delete') {
        userOperations[audit.user_id].deviceOps++;
      }
      userOperations[audit.user_id].actions.push({
        action: audit.action,
        time: auditTime
      });
    }
  });
  
  // 检查是否有需要预警的用户
  const warnings = [];
  for (const userId in userOperations) {
    const user = userOperations[userId];
    
    // 1小时内操作超过10次
    if (user.hourlyOps > 10) {
      warnings.push({
        level: 'high',
        message: `用户 ${user.email} 在1小时内操作了 ${user.hourlyOps} 次，请注意！`
      });
    }
    
    // 1天内设备增删操作超过5次
    if (user.deviceOps > 5) {
      warnings.push({
        level: 'high',
        message: `用户 ${user.email} 在1天内进行了 ${user.deviceOps} 次设备增删操作，可能存在滥用！`
      });
    }
    
    // 1天内总操作超过50次
    if (user.dailyOps > 50) {
      warnings.push({
        level: 'medium',
        message: `用户 ${user.email} 在1天内总计操作了 ${user.dailyOps} 次，活动异常频繁！`
      });
    }
  }
  
  // 显示预警
  if (warnings.length > 0) {
    displayWarnings(warnings);
  }
}

// 显示预警信息
function displayWarnings(warnings) {
  // 检查是否已有预警容器
  let warningContainer = document.getElementById('audit-warnings');
  if (!warningContainer) {
    // 创建预警容器
    const auditSection = document.getElementById('section-device-audits');
    const firstCard = auditSection.querySelector('.card');
    
    warningContainer = document.createElement('div');
    warningContainer.id = 'audit-warnings';
    warningContainer.className = 'mb-3';
    auditSection.insertBefore(warningContainer, firstCard);
  }
  
  // 清空现有预警
  warningContainer.innerHTML = '';
  
  // 添加预警
  warnings.forEach(warning => {
    const alertClass = warning.level === 'high' ? 'alert-danger' : 'alert-warning';
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
      <i class="bi bi-exclamation-triangle-fill me-2"></i>
      <strong>预警：</strong>${warning.message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    warningContainer.appendChild(alertDiv);
  });
}

// 设置审计日志分页
function setupAuditsPagination(currentPage, totalPages) {
  const pagination = document.getElementById('audits-pagination');
  if (!pagination) return;
  
  pagination.innerHTML = '';
  
  if (totalPages <= 1) return;
  
  // 上一页
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `<a class="page-link" href="#">上一页</a>`;
  if (currentPage > 1) {
    prevLi.onclick = () => loadDeviceAudits(currentPage - 1);
  }
  pagination.appendChild(prevLi);
  
  // 页码
  for (let i = 1; i <= totalPages; i++) {
    if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
      const li = document.createElement('li');
      li.className = `page-item ${i === currentPage ? 'active' : ''}`;
      li.innerHTML = `<a class="page-link" href="#">${i}</a>`;
      li.onclick = () => loadDeviceAudits(i);
      pagination.appendChild(li);
    } else if (i === currentPage - 3 || i === currentPage + 3) {
      const li = document.createElement('li');
      li.className = 'page-item disabled';
      li.innerHTML = `<a class="page-link" href="#">...</a>`;
      pagination.appendChild(li);
    }
  }
  
  // 下一页
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `<a class="page-link" href="#">下一页</a>`;
  if (currentPage < totalPages) {
    nextLi.onclick = () => loadDeviceAudits(currentPage + 1);
  }
  pagination.appendChild(nextLi);
}

// 查看审计详情
// 存储当前加载的审计数据
let currentAuditsData = [];

async function viewAuditDetails(auditId) {
  // 从当前数据中查找审计记录
  const audit = currentAuditsData.find(a => a.id === auditId);
  if (!audit) {
    showToast('找不到审计记录', 'danger');
    return;
  }
  
  // 创建模态框（如果不存在）
  let modal = document.getElementById('auditDetailsModal');
  if (!modal) {
    modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'auditDetailsModal';
    modal.innerHTML = `
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">审计详情</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" id="auditDetailsBody">
            <div class="text-center">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
    document.body.appendChild(modal);
  }
  
  // 显示模态框
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();
  
  // 显示审计详情
  const detailsBody = document.getElementById('auditDetailsBody');
  
  // 解析浏览器信息
  let browserInfo = audit.user_agent || '未记录';
  if (browserInfo !== '未记录' && browserInfo.length > 100) {
    // 如果浏览器信息太长，进行换行显示
    browserInfo = `<div style="word-break: break-all;">${browserInfo}</div>`;
  }
  
  detailsBody.innerHTML = `
    <dl class="row">
      <dt class="col-sm-3">时间</dt>
      <dd class="col-sm-9">${formatDateTime(audit.created_at)}</dd>
      <dt class="col-sm-3">用户</dt>
      <dd class="col-sm-9"><small>${audit.user_email}<br>ID: ${audit.user_id}</small></dd>
      <dt class="col-sm-3">操作类型</dt>
      <dd class="col-sm-9"><span class="badge ${getActionBadgeClass(audit.action)}">${getActionLabel(audit.action)}</span></dd>
      <dt class="col-sm-3">操作来源</dt>
      <dd class="col-sm-9"><span class="badge ${getSourceBadgeClass(audit.action_source)}">${getSourceLabel(audit.action_source)}</span></dd>
      <dt class="col-sm-3">描述</dt>
      <dd class="col-sm-9">${audit.description || '-'}</dd>
      <dt class="col-sm-3">IP地址</dt>
      <dd class="col-sm-9">${audit.ip_address || '-'}</dd>
      <dt class="col-sm-3">浏览器信息</dt>
      <dd class="col-sm-9"><small class="text-muted">${browserInfo}</small></dd>
    </dl>
    ${audit.device_snapshot ? `
    <hr>
    <h6>设备快照</h6>
    <dl class="row">
      <dt class="col-sm-3">设备ID</dt>
      <dd class="col-sm-9">${audit.device_snapshot.device_id || '-'}</dd>
      <dt class="col-sm-3">设备名称</dt>
      <dd class="col-sm-9">${audit.device_snapshot.device_name || '-'}</dd>
      <dt class="col-sm-3">设备类型</dt>
      <dd class="col-sm-9">${audit.device_snapshot.device_type || '-'}</dd>
      <dt class="col-sm-3">设备IP</dt>
      <dd class="col-sm-9">${audit.device_snapshot.device_ip || '-'}</dd>
    </dl>
    ` : ''}
  `;
}

// 显示设备更换统计
async function showDeviceChangeStatistics() {
  const token = localStorage.getItem("token");
  
  try {
    const response = await fetch('/api/admin/device-audits/device-changes?days=30&limit=20', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (handleUnauthorizedAdminResponse(response)) return;
    if (!response.ok) throw new Error('获取设备更换统计失败');
    
    const result = await response.json();
    
    if (result.success) {
      // 创建模态框显示统计
      let modal = document.getElementById('deviceChangesModal');
      if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'deviceChangesModal';
        modal.innerHTML = `
          <div class="modal-dialog modal-xl">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">设备更换统计（最近30天）</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <div class="alert alert-info mb-3">
                  <i class="bi bi-info-circle"></i> 显示最近30天内设备更换次数最多的前20个用户
                </div>
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>排名</th>
                        <th>用户</th>
                        <th>总更换次数</th>
                        <th>注册设备</th>
                        <th>删除设备</th>
                        <th>日均更换</th>
                        <th>不同IP数</th>
                        <th>最后操作</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody id="deviceChangesBody"></tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        `;
        document.body.appendChild(modal);
      }
      
      // 填充数据
      const tbody = document.getElementById('deviceChangesBody');
      tbody.innerHTML = '';
      
      if (result.data.statistics && result.data.statistics.length > 0) {
        result.data.statistics.forEach((stat, index) => {
          const row = document.createElement('tr');
          
          // 根据更换次数设置行样式
          if (stat.total_changes > 20) {
            row.classList.add('table-danger');
          } else if (stat.total_changes > 10) {
            row.classList.add('table-warning');
          }
          
          row.innerHTML = `
            <td>${index + 1}</td>
            <td>
              <strong>${stat.email}</strong><br>
              <small class="text-muted">ID: ${stat.user_id}</small>
            </td>
            <td><span class="badge bg-primary fs-6">${stat.total_changes}</span></td>
            <td><span class="badge bg-success">${stat.register_count}</span></td>
            <td><span class="badge bg-danger">${stat.delete_count}</span></td>
            <td><span class="badge bg-info">${stat.change_frequency}/天</span></td>
            <td>${stat.unique_ips}</td>
            <td><small>${formatDateTime(stat.last_action_time)}</small></td>
            <td>
              <button class="btn btn-sm btn-primary" onclick="viewUserAudits(${stat.user_id})">
                查看详情
              </button>
            </td>
          `;
          tbody.appendChild(row);
        });
      } else {
        // 没有数据时显示友好提示
        tbody.innerHTML = `
          <tr>
            <td colspan="9" class="text-center py-5">
              <div class="text-muted">
                <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                <h5>暂无设备更换记录</h5>
                <p>最近30天内没有用户进行设备更换操作</p>
              </div>
            </td>
          </tr>
        `;
      }
      
      // 显示模态框
      const bsModal = new bootstrap.Modal(modal);
      bsModal.show();
    }
  } catch (error) {
    showToast(`获取设备更换统计失败: ${error.message}`, 'danger');
    console.error('Error loading device change statistics:', error);
  }
}

// 获取风险等级标签
function getRiskLevelLabel(level) {
  const labels = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险',
    'normal': '正常'
  };
  return labels[level] || level;
}

// 获取风险等级徽章样式
function getRiskLevelBadgeClass(level) {
  const classes = {
    'high': 'bg-danger',
    'medium': 'bg-warning',
    'low': 'bg-info',
    'normal': 'bg-success'
  };
  return classes[level] || 'bg-secondary';
}

// 查看用户审计记录
function viewUserAudits(userId) {
  // 关闭设备更换统计模态框
  const deviceChangesModal = bootstrap.Modal.getInstance(document.getElementById('deviceChangesModal'));
  if (deviceChangesModal) deviceChangesModal.hide();
  
  // 跳转到审计页面
  showSection('device-audits');
  
  // 设置筛选器并加载
  setTimeout(() => {
    document.getElementById('audit-user-filter').value = userId;
    applyAuditFilter();
  }, 300);
}

// 应用审计筛选
function applyAuditFilter() {
  // 获取筛选值
  auditFilters = {};
  
  const userFilter = document.getElementById('audit-user-filter').value;
  if (userFilter) auditFilters.user_id = userFilter;
  
  const actionFilter = document.getElementById('audit-action-filter').value;
  if (actionFilter) auditFilters.action = actionFilter;
  
  const startDate = document.getElementById('audit-start-date').value;
  if (startDate) auditFilters.start_date = startDate;
  
  const endDate = document.getElementById('audit-end-date').value;
  if (endDate) auditFilters.end_date = endDate;
  
  
  // 重新加载数据
  loadDeviceAudits(1);
}

// 重置审计筛选
function resetAuditFilter() {
  document.getElementById('audit-user-filter').value = '';
  document.getElementById('audit-action-filter').value = '';
  document.getElementById('audit-start-date').value = '';
  document.getElementById('audit-end-date').value = '';
  
  auditFilters = {};
  loadDeviceAudits(1);
}

// 初始化审计事件监听器
document.addEventListener('DOMContentLoaded', function() {
  // 刷新按钮
  const refreshBtn = document.getElementById('refresh-audits-btn');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', () => loadDeviceAudits(currentAuditsPage));
  }
  
  // 设备更换统计按钮
  const deviceChangesBtn = document.getElementById('device-changes-btn');
  if (deviceChangesBtn) {
    deviceChangesBtn.addEventListener('click', showDeviceChangeStatistics);
  }
  
  // 应用筛选按钮
  const applyFilterBtn = document.getElementById('apply-audit-filter');
  if (applyFilterBtn) {
    applyFilterBtn.addEventListener('click', applyAuditFilter);
  }
  
  // 重置筛选按钮
  const resetFilterBtn = document.getElementById('reset-audit-filter');
  if (resetFilterBtn) {
    resetFilterBtn.addEventListener('click', resetAuditFilter);
  }
});

// 加载设备审计统计数据
async function loadDeviceAuditStats() {
  const token = localStorage.getItem("token");
  
  try {
    // 获取最近7天的审计数据来计算统计
    const params = new URLSearchParams({
      page: 1,
      per_page: 1000, // 获取足够多的数据进行统计
      start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString() // 7天前
    });
    
    const response = await fetch(`/api/admin/device-audits?${params}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (handleUnauthorizedAdminResponse(response)) return;
    if (!response.ok) throw new Error('获取审计统计失败');
    
    const result = await response.json();
    
    if (result.success && result.data.audits) {
      const audits = result.data.audits;
      
      // 计算统计数据
      let totalChanges = 0;
      let totalAdds = 0;
      let totalRemoves = 0;
      const userChanges = {};
      
      audits.forEach(audit => {
        if (audit.action === 'register' || audit.action === 'delete') {
          totalChanges++;
          
          if (audit.action === 'register') {
            totalAdds++;
          } else if (audit.action === 'delete') {
            totalRemoves++;
          }
          
          // 统计每个用户的操作次数
          if (!userChanges[audit.user_id]) {
            userChanges[audit.user_id] = 0;
          }
          userChanges[audit.user_id]++;
        }
      });
      
      // 计算高频用户数（超过5次操作的用户）
      const highFrequencyUsers = Object.values(userChanges).filter(count => count > 5).length;
      
      // 更新界面
      document.getElementById('total-device-changes').textContent = totalChanges;
      document.getElementById('total-device-adds').textContent = totalAdds;
      document.getElementById('total-device-removes').textContent = totalRemoves;
      document.getElementById('high-frequency-users').textContent = highFrequencyUsers;
    }
  } catch (error) {
    console.error('加载设备审计统计失败:', error);
    // 显示错误状态
    document.getElementById('total-device-changes').textContent = '-';
    document.getElementById('total-device-adds').textContent = '-';
    document.getElementById('total-device-removes').textContent = '-';
    document.getElementById('high-frequency-users').textContent = '-';
  }
}

// --- 设备审计功能结束 ---

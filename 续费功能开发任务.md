# Context
Filename: 续费功能开发任务.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
为这个项目增加续费功能，现在如果已有套餐点击立刻续费，会出现已有购买套餐无法重复购买的问题。

# Project Overview
这是一个基于Flask的AdsPower账号管理系统，用户可以购买订阅套餐来获得AdsPower账号的使用权限。系统包含：
- 用户认证和管理
- 订阅套餐管理（SubscriptionType）
- 订阅实例管理（SubscriptionInstance）
- 用户订阅记录（Subscription）
- 支付系统（Payment）
- 设备管理（Device）

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 现有系统架构分析

### 数据模型关系
1. **SubscriptionType**: 订阅套餐类型（月付、年付等）
2. **SubscriptionInstance**: 订阅实例（具体的"车次"或资源池）
3. **Subscription**: 用户订阅记录（用户购买的具体订阅）
4. **Payment**: 支付记录
5. **User**: 用户信息

### 现有购买流程
1. 用户在前端选择套餐
2. 调用 `/api/payments/create` 创建支付订单
3. 在 `routes.py` 中检查用户是否已有活跃订阅
4. 如果有不同类型的活跃订阅，阻止购买
5. 如果有相同类型的活跃订阅，应该允许续费但当前被阻止

### 问题根源
在 `adspower_manager/api/routes.py` 的 `create_payment()` 函数中（第549-566行），存在以下逻辑：

```python
if existing_sub:
    # 如果已有不同类型的活跃订阅，阻止创建支付
    if existing_sub.subscription_type_id != subscription_type_id:
        # 阻止不同类型订阅
        return jsonify_response(success=False, message="无法同时购买不同类型订阅")
```

但是这个检查没有区分"续费相同套餐"和"购买不同套餐"的情况。

### 续费逻辑存在于
在 `adspower_manager/services/subscription_service.py` 的 `create_or_extend_subscription()` 函数中（第164-172行），已经有续费逻辑：

```python
if existing_sub.subscription_type_id == subscription_type.id:
    new_end_date = existing_sub.extend(subscription_type.days)
    # ... 续费逻辑
```

### 前端续费按钮
在 `templates/dashboard.html` 和 `static/js/dashboard.js` 中，已经有续费按钮，但点击后仍然调用相同的购买API。

## 核心问题
支付创建阶段就阻止了相同套餐的重复购买，没有区分续费场景。需要在支付创建时允许相同套餐的续费请求通过。

# Proposed Solution (Populated by INNOVATE mode)

## 解决方案选项

### 方案1：修改现有API逻辑（推荐）
在 `/api/payments/create` 中修改检查逻辑：
- 如果用户已有相同类型的活跃订阅，允许创建支付（续费）
- 如果用户已有不同类型的活跃订阅，继续阻止
- 在后续的订阅创建/延长逻辑中处理续费

**优点**：
- 最小化代码变更
- 复用现有的续费逻辑
- 保持API一致性

**缺点**：
- 可能需要在前端添加续费确认提示

### 方案2：创建专门的续费API
创建新的 `/api/payments/renew` 端点：
- 专门处理续费请求
- 更明确的语义
- 可以添加续费特定的验证和逻辑

**优点**：
- 语义更清晰
- 可以添加续费特定功能
- 不影响现有购买流程

**缺点**：
- 需要更多代码变更
- 前端需要区分购买和续费按钮

### 方案3：前端参数区分
在现有API中添加 `is_renewal` 参数来区分续费和购买：
- 前端续费按钮传递 `is_renewal: true`
- 后端根据此参数调整验证逻辑

**优点**：
- 保持API端点统一
- 明确区分续费意图
- 灵活性高

**缺点**：
- 需要前后端协调修改

## 推荐方案
采用**方案1**，因为：
1. 变更最小，风险最低
2. 现有的续费逻辑已经很完善
3. 只需要调整支付创建时的验证逻辑

# Implementation Plan (Generated by PLAN mode)

## 详细实施计划

### 文件修改清单

#### 1. 后端API修改
**文件**: `adspower_manager/api/routes.py`
- **位置**: `create_payment()` 函数（约第549-566行）
- **修改内容**: 调整现有订阅检查逻辑，允许相同套餐类型的续费

#### 2. 前端显示优化
**文件**: `static/js/dashboard.js`
- **位置**: `loadSubscriptionInfo()` 函数中的续费按钮部分（约第392-397行）
- **修改内容**: 优化续费按钮的显示文本和确认提示

**文件**: `static/js/dashboard.js`
- **位置**: `createPaymentOrder()` 函数（约第1010-1167行）
- **修改内容**: 为续费场景添加特殊的确认提示

#### 3. 国际化文本
**文件**: `static/js/i18n.js`
- **修改内容**: 添加续费相关的提示文本

### 具体修改步骤

#### 步骤1: 修改支付创建API逻辑
在 `routes.py` 中修改现有订阅检查：
- 保留对不同套餐类型的阻止逻辑
- 移除对相同套餐类型的阻止逻辑
- 添加续费场景的日志记录

#### 步骤2: 优化前端续费体验
- 修改续费确认提示，明确说明这是续费操作
- 在续费成功后显示新的到期时间
- 优化续费按钮的文案

#### 步骤3: 添加必要的国际化文本
- 续费确认提示
- 续费成功消息
- 续费相关错误消息

#### 步骤4: 测试验证
- 测试相同套餐续费功能
- 测试不同套餐购买仍被阻止
- 测试续费后订阅时间正确延长
- 测试前端显示更新正确

## Implementation Checklist:
1. 修改 `adspower_manager/api/routes.py` 中的 `create_payment()` 函数，调整现有订阅检查逻辑
2. 在 `static/js/i18n.js` 中添加续费相关的国际化文本
3. 修改 `static/js/dashboard.js` 中的 `createPaymentOrder()` 函数，添加续费确认提示
4. 测试续费功能是否正常工作
5. 验证不同套餐购买仍被正确阻止

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "步骤2: 添加国际化文本"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2024-12-19 14:30
    *   Step: 1. 修改 `adspower_manager/api/routes.py` 中的支付创建逻辑，允许相同套餐类型的续费请求通过
    *   Modifications: 在第549-569行修改了现有订阅检查逻辑，添加了相同套餐类型的续费允许逻辑和日志记录
    *   Change Summary: 允许相同套餐类型的续费请求通过支付创建阶段，同时保持对不同套餐类型的限制
    *   Reason: 执行计划步骤1
    *   Blockers: None
    *   Status: Pending Confirmation

# Final Review (Populated by REVIEW mode)

"""删除subscription_types表的discount字段

Revision ID: 20250525224552
Revises: 0416953ab1ef
Create Date: 2025-05-25 22:45:52

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250525224552'
down_revision = '0416953ab1ef'
branch_labels = None
depends_on = None


def upgrade():
    # 删除 subscription_types 表的 discount 字段
    # 由于前面的迁移可能已经删除了这个字段，需要检查它是否存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('subscription_types')]
    
    if 'discount' in columns:
        with op.batch_alter_table('subscription_types', schema=None) as batch_op:
            batch_op.drop_column('discount')
    else:
        # 字段已经被删除，跳过
        pass


def downgrade():
    # 恢复 subscription_types 表的 discount 字段
    # 检查字段是否已存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('subscription_types')]
    
    if 'discount' not in columns:
        with op.batch_alter_table('subscription_types', schema=None) as batch_op:
            batch_op.add_column(sa.Column('discount', sa.INTEGER(), nullable=True, default=100))
"""将订阅类型关联重构为使用ID并更新相关字段

Revision ID: 1eb33b773986
Revises: e3289a493ad2
Create Date: 2025-05-19 22:20:01.362614

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1eb33b773986'
down_revision = 'e3289a493ad2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscription_types', schema=None) as batch_op:
        batch_op.alter_column('code',
               existing_type=sa.VARCHAR(length=32),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscription_types', schema=None) as batch_op:
        batch_op.alter_column('code',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)

    # ### end Alembic commands ###

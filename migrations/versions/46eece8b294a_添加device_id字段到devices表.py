"""添加device_id字段到devices表

Revision ID: 46eece8b294a
Revises: 20250526000127
Create Date: 2025-05-27 23:09:55.656386

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '46eece8b294a'
down_revision = '20250526000127'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.add_column(sa.Column('device_id', sa.String(length=100), nullable=True))
        batch_op.create_index(batch_op.f('ix_devices_device_id'), ['device_id'], unique=True)

    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('max_devices',
               existing_type=sa.INTEGER(),
               nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('max_devices',
               existing_type=sa.INTEGER(),
               nullable=True)

    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_devices_device_id'))
        batch_op.drop_column('device_id')

    # ### end Alembic commands ###

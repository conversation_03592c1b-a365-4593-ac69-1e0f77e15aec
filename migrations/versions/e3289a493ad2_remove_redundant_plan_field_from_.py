"""Remove redundant plan field from Subscription model,Remove_plan_id_from_Payment_model

Revision ID: e3289a493ad2
Revises: 44be192e02e2
Create Date: 2025-05-19 21:57:38.947610

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e3289a493ad2'
down_revision = '44be192e02e2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.drop_column('plan')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('plan', sa.VARCHAR(length=50), nullable=False))

    # ### end Alembic commands ###

"""remove code filed and migrate data to id-based foreign keys

Revision ID: 44be192e02e2
Revises: 61a3082e600f
Create Date: 2025-05-19 18:53:20.303184

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '44be192e02e2'
down_revision = '61a3082e600f'
branch_labels = None
depends_on = None


def upgrade():
    # ### Step 1: Add new columns as nullable first ###
    with op.batch_alter_table('payments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_id', sa.Integer(), nullable=True))

    with op.batch_alter_table('subscription_instances', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_id', sa.Integer(), nullable=True)) # Initially nullable

    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_id', sa.Integer(), nullable=True)) # Initially nullable

    # ### Step 2: Data Migration - Populate new *_id columns ###
    conn = op.get_bind()

    # Populate payments.subscription_type_id
    # Assumes payments.plan_id stores the old 'code' from subscription_types
    # Using a direct update with a subquery. Ensure data integrity (codes in plan_id exist in subscription_types).
    # For large tables, consider batching or a more robust UPDATE FROM JOIN if your DB supports it well.
    conn.execute(
        sa.text(
            """
            UPDATE payments
            SET subscription_type_id = (
                SELECT st.id
                FROM subscription_types st
                WHERE st.code = payments.plan_id
            )
            WHERE payments.plan_id IS NOT NULL;
            """
        )
    )
    
    # Populate subscription_instances.subscription_type_id
    conn.execute(
        sa.text(
            """
            UPDATE subscription_instances
            SET subscription_type_id = (
                SELECT st.id
                FROM subscription_types st
                WHERE st.code = subscription_instances.subscription_type_code
            )
            WHERE subscription_instances.subscription_type_code IS NOT NULL;
            """
        )
    )

    # Populate subscriptions.subscription_type_id
    conn.execute(
        sa.text(
            """
            UPDATE subscriptions
            SET subscription_type_id = (
                SELECT st.id
                FROM subscription_types st
                WHERE st.code = subscriptions.subscription_type_code
            )
            WHERE subscriptions.subscription_type_code IS NOT NULL;
            """
        )
    )

    # ### Step 3: Alter columns to non-nullable, create FKs, and drop old columns/constraints ###

    with op.batch_alter_table('payments', schema=None) as batch_op:
        # Foreign key for payments. The column remains nullable as per model.
        batch_op.create_foreign_key('fk_payments_subscription_type_id_subscription_types', 'subscription_types', ['subscription_type_id'], ['id'])
        # The 'plan_id' column (storing old code) is kept as per model changes for now.
        batch_op.drop_column('plan_id')

    with op.batch_alter_table('subscription_instances', schema=None) as batch_op:
        # Now that data is populated, make subscription_type_id non-nullable
        # This assumes all rows were successfully populated. If any subscription_type_code was invalid or NULL,
        # this step might fail if subscription_type_id remains NULL.
        # Consider adding a check or data cleanup step before this if necessary.
        batch_op.alter_column('subscription_type_id', nullable=False)
        
        # The old foreign key on 'subscription_type_code' will be effectively removed
        # when the 'subscription_type_code' column is dropped in batch mode (table recreation for SQLite).
        # No explicit drop_constraint for the old FK is needed here for SQLite in batch mode if the column is dropped.

        batch_op.create_foreign_key('fk_subscription_instances_subscription_type_id_subscription_types', 'subscription_types', ['subscription_type_id'], ['id'])
        batch_op.drop_column('subscription_type_code')

    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('subscription_type_id', nullable=False)
        
        # This constraint name was explicitly in the original script, so it's safer.
        batch_op.drop_constraint('fk_subscriptions_subscription_type_code_subscription_types', type_='foreignkey')
        batch_op.create_foreign_key('fk_subscriptions_subscription_type_id_subscription_types', 'subscription_types', ['subscription_type_id'], ['id'])
        
        # Confirm if 'status' column drop is intended.
        # Assuming it is, as per your original script and model changes.
        batch_op.drop_column('status') 
        
        batch_op.drop_column('subscription_type_code')

    # ### end Alembic commands ###


def downgrade():
    # ### Step 1: Add back old columns as nullable, drop new FKs and columns ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_code', sa.VARCHAR(length=32), nullable=True))
        # Restore 'status' column if it was dropped. Adjust type/nullable as per original schema.
        batch_op.add_column(sa.Column('status', sa.VARCHAR(length=32), nullable=True, server_default='active')) # Assuming 'active' was a common default

        batch_op.drop_constraint('fk_subscriptions_subscription_type_id_subscription_types', type_='foreignkey')
        batch_op.drop_column('subscription_type_id')

    with op.batch_alter_table('subscription_instances', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_code', sa.VARCHAR(length=32), nullable=True))
        batch_op.drop_constraint('fk_subscription_instances_subscription_type_id_subscription_types', type_='foreignkey')
        batch_op.drop_column('subscription_type_id')

    with op.batch_alter_table('payments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('plan_id', sa.VARCHAR(length=32), nullable=True)) # Add back plan_id
        # Data migration for plan_id will be handled by conn.execute before dropping subscription_type_id
        batch_op.drop_constraint('fk_payments_subscription_type_id_subscription_types', type_='foreignkey')
        # We will drop subscription_type_id after populating plan_id via conn.execute
        # batch_op.drop_column('subscription_type_id') # This will be done after data migration

    # ### Step 2: Data Migration - Populate old *_code columns from *_id ###
    conn = op.get_bind()

    # Populate subscriptions.subscription_type_code from subscriptions.subscription_type_id (which was just dropped but data was in memory before column drop)
    # This is tricky as the column is dropped. This implies we need to populate *before* dropping.
    # The downgrade logic needs careful ordering. Let's re-evaluate.

    # Corrected Downgrade Order:
    # 1. Add back _code columns (nullable=True)
    # 2. Add back status column to subscriptions (nullable=True)
    # 3. Populate _code columns using the _id columns (BEFORE _id columns are dropped)
    # 4. Make _code columns non-nullable (and status if it was)
    # 5. Drop new FKs (on _id columns)
    # 6. Drop _id columns
    # 7. Recreate old FKs (on _code columns)

    # --- This is a rewrite of the downgrade for better data handling ---
    op.execute("DELETE FROM alembic_version WHERE version_num = '44be192e02e2';") # Manual pre-delete if re-running

    # Add back columns first
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_code', sa.VARCHAR(length=32), nullable=True))
        batch_op.add_column(sa.Column('status', sa.VARCHAR(length=32), nullable=True, server_default='active')) # Assuming previous default

    with op.batch_alter_table('subscription_instances', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_code', sa.VARCHAR(length=32), nullable=True))
    
    # Populate old _code columns
    conn.execute(
        sa.text(
            """
            UPDATE subscriptions
            SET subscription_type_code = (
                SELECT st.code
                FROM subscription_types st
                WHERE st.id = subscriptions.subscription_type_id -- This relies on subscription_type_id still existing
            )
            WHERE subscriptions.subscription_type_id IS NOT NULL; 
            """
            # This implies subscription_type_id must exist during this update.
            # The previous auto-generated downgrade dropped it too early.
        )
    )
    conn.execute(
        sa.text(
            """
            UPDATE subscription_instances
            SET subscription_type_code = (
                SELECT st.code
                FROM subscription_types st
                WHERE st.id = subscription_instances.subscription_type_id -- Relies on subscription_type_id
            )
            WHERE subscription_instances.subscription_type_id IS NOT NULL;
            """
        )
    )
    # For payments, plan_id was assumed to hold the code and was not dropped.
    # If subscription_type_id was used to populate plan_id in some other logic, that needs to be reversed here if plan_id was also changed.
    # But since plan_id was kept, and subscription_type_id (new) is being dropped, no specific data migration for payments.plan_id here.

    # Populate payments.plan_id from payments.subscription_type_id
    conn.execute(
        sa.text(
            """
            UPDATE payments
            SET plan_id = (
                SELECT st.code
                FROM subscription_types st
                WHERE st.id = payments.subscription_type_id
            )
            WHERE payments.subscription_type_id IS NOT NULL;
            """
        )
    )

    # Now make columns non-nullable and manage constraints
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('subscription_type_code', nullable=False)
        batch_op.alter_column('status', nullable=False) # Assuming it was non-nullable
        
        batch_op.drop_constraint('fk_subscriptions_subscription_type_id_subscription_types', type_='foreignkey')
        batch_op.create_foreign_key('fk_subscriptions_subscription_type_code_subscription_types', 'subscription_types', ['subscription_type_code'], ['code'])
        batch_op.drop_column('subscription_type_id')

    with op.batch_alter_table('subscription_instances', schema=None) as batch_op:
        batch_op.alter_column('subscription_type_code', nullable=False)
        
        batch_op.drop_constraint('fk_subscription_instances_subscription_type_id_subscription_types', type_='foreignkey')
        # Recreate old FK on subscription_type_code. Name it if known.
        # Example: batch_op.create_foreign_key('fk_subscription_instances_old_code_fk', 'subscription_types', ['subscription_type_code'], ['code'])
        # The original script used `None` for create_foreign_key, let's be more specific if possible
        # or use a conventional name
        batch_op.create_foreign_key('fk_subscription_instances_subscription_type_code_st_code', 'subscription_types', ['subscription_type_code'], ['code'])
        batch_op.drop_column('subscription_type_id')

    with op.batch_alter_table('payments', schema=None) as batch_op:
        # No subscription_type_code to make non-nullable as plan_id was kept.
        # batch_op.drop_constraint('fk_payments_subscription_type_id_subscription_types', type_='foreignkey') # Already dropped above
        batch_op.drop_column('subscription_type_id') # Now drop it after data migration
    # ### end Alembic commands ###

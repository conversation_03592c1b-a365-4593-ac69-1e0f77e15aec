"""移除Device表的last_login和last_active字段

Revision ID: 20250526000127
Revises: 20250525224552
Create Date: 2025-05-26 00:01:27

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250526000127'
down_revision = '20250525224552'
branch_labels = None
depends_on = None


def upgrade():
    # 删除 devices 表的 last_login 和 last_active 字段
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.drop_column('last_login')
        batch_op.drop_column('last_active')


def downgrade():
    # 恢复 devices 表的 last_login 和 last_active 字段
    with op.batch_alter_table('devices', schema=None) as batch_op:
        batch_op.add_column(sa.Column('last_login', sa.DATETIME(), nullable=True))
        batch_op.add_column(sa.Column('last_active', sa.DATETIME(), nullable=True))
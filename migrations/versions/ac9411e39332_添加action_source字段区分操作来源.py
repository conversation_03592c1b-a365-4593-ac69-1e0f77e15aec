"""添加action_source字段区分操作来源

Revision ID: ac9411e39332
Revises: 268ea8c70f7d
Create Date: 2025-06-05 14:59:17.796854

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ac9411e39332'
down_revision = '268ea8c70f7d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('device_audits', schema=None) as batch_op:
        batch_op.add_column(sa.Column('action_source', sa.String(length=50), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('device_audits', schema=None) as batch_op:
        batch_op.drop_column('action_source')

    # ### end Alembic commands ###

"""Implement SubscriptionInstance and update models

Revision ID: 61a3082e600f
Revises: 419ebf0455c8
Create Date: 2025-05-18 18:02:34.441698

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '61a3082e600f'
down_revision = '419ebf0455c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 1. 先创建 payment_records 表
    op.create_table('payment_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('transaction_id', sa.String(length=128), nullable=True),
    sa.Column('payment_status', sa.String(length=20), nullable=True),
    sa.Column('payment_time', sa.DateTime(), nullable=True),
    sa.Column('subscription_days', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    # 2. 创建 subscription_instances 表
    op.create_table('subscription_instances',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('subscription_type_code', sa.String(length=32), nullable=False),
    sa.Column('capacity', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['subscription_type_code'], ['subscription_types.code'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    # 3. 为每个 subscription_type 创建默认的 subscription_instance
    # 使用兼容旧版本 SQLite 的方式
    conn = op.get_bind()
    result = conn.execute(sa.text("SELECT id, code, name FROM subscription_types ORDER BY id"))
    rows = result.fetchall()
    for idx, (type_id, code, name) in enumerate(rows, 1):
        # 转义单引号以防止 SQL 注入
        safe_name = name.replace("'", "''")
        safe_code = code.replace("'", "''")
        conn.execute(sa.text(f"""
            INSERT INTO subscription_instances (id, name, subscription_type_code, capacity, is_active, created_at)
            VALUES ({idx}, 'Default - {safe_name}', '{safe_code}', 100, 1, datetime('now'))
        """))

    # 4. 添加新列到 subscriptions 表（先设为可空）
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type_code', sa.String(length=32), nullable=True))
        batch_op.add_column(sa.Column('subscription_instance_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('status', sa.String(length=32), nullable=True))
        batch_op.alter_column('payment_id',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.Integer(),
               existing_nullable=True)
    
    # 5. 为现有数据设置 subscription_type_code（基于 plan 字段）
    # 首先尝试直接匹配
    op.execute("""
        UPDATE subscriptions 
        SET subscription_type_code = (
            SELECT code FROM subscription_types 
            WHERE LOWER(subscription_types.code) = LOWER(subscriptions.plan)
            LIMIT 1
        )
        WHERE subscription_type_code IS NULL
    """)
    
    # 如果还有未匹配的，使用第一个可用的 subscription_type
    op.execute("""
        UPDATE subscriptions 
        SET subscription_type_code = (
            SELECT code FROM subscription_types 
            ORDER BY id 
            LIMIT 1
        )
        WHERE subscription_type_code IS NULL
    """)
    
    # 6. 设置 subscription_instance_id
    op.execute("""
        UPDATE subscriptions 
        SET subscription_instance_id = (
            SELECT id FROM subscription_instances 
            WHERE subscription_instances.subscription_type_code = subscriptions.subscription_type_code
            LIMIT 1
        )
        WHERE subscription_instance_id IS NULL
    """)
    
    # 7. 设置状态
    op.execute("""
        UPDATE subscriptions 
        SET status = CASE 
            WHEN end_date > datetime('now') THEN 'active'
            ELSE 'expired'
        END
        WHERE status IS NULL
    """)
    
    # 8. 现在可以安全地将列设为非空并添加外键
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('subscription_type_code', nullable=False)
        batch_op.alter_column('subscription_instance_id', nullable=False)
        batch_op.alter_column('status', nullable=False)
        batch_op.create_foreign_key(batch_op.f('fk_subscriptions_subscription_type_code_subscription_types'), 'subscription_types', ['subscription_type_code'], ['code'])
        batch_op.create_foreign_key(batch_op.f('fk_subscriptions_payment_id_payments'), 'payments', ['payment_id'], ['id'])
        batch_op.create_foreign_key(batch_op.f('fk_subscriptions_subscription_instance_id_subscription_instances'), 'subscription_instances', ['subscription_instance_id'], ['id'])
        batch_op.drop_column('chatgpt_account_id')

    # 9. 删除 chatgpt_accounts 表
    op.drop_table('chatgpt_accounts')

    # 10. 创建 login_sessions 表（替代 login_session）
    op.create_table('login_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('adspower_account_id', sa.Integer(), nullable=True),
    sa.Column('login_token', sa.String(length=128), nullable=False),
    sa.Column('login_time', sa.DateTime(), nullable=True),
    sa.Column('logout_time', sa.DateTime(), nullable=True),
    sa.Column('completed_time', sa.DateTime(), nullable=True),
    sa.Column('ip_address', sa.String(length=50), nullable=True),
    sa.Column('user_agent', sa.String(length=255), nullable=True),
    sa.Column('login_status', sa.String(length=20), nullable=True),
    sa.Column('device_info', sa.Text(), nullable=True),
    sa.Column('session_duration_seconds', sa.Integer(), nullable=True),
    sa.Column('expiration_timestamp', sa.DateTime(), nullable=False),
    sa.Column('known_devices_snapshot', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['adspower_account_id'], ['adspower_accounts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('login_sessions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_login_sessions_login_token'), ['login_token'], unique=True)

    # 11. 删除旧的 login_session 表
    with op.batch_alter_table('login_session', schema=None) as batch_op:
        batch_op.drop_index('ix_login_session_login_token')
    op.drop_table('login_session')
    
    # 12. 删除其他旧表
    op.drop_table('payment_record')
    op.drop_table('email_verifications')
    op.drop_table('user_adspower_account')
    
    # 13. 更新 adspower_accounts 表
    # 先更新 NULL 的 max_devices 为默认值
    op.execute("UPDATE adspower_accounts SET max_devices = 10 WHERE max_devices IS NULL")
    
    with op.batch_alter_table('adspower_accounts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_instance_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('description', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True))
        batch_op.alter_column('max_devices',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.create_unique_constraint('uq_adspower_accounts_username', ['username'])
        batch_op.create_foreign_key('fk_adspower_accounts_subscription_instance_id', 'subscription_instances', ['subscription_instance_id'], ['id'])
        batch_op.drop_column('api_key')
        batch_op.drop_column('remarks')
        batch_op.drop_column('subscription_type')

    # 14. 更新 payments 表
    with op.batch_alter_table('payments', schema=None) as batch_op:
        batch_op.alter_column('payment_id',
               existing_type=sa.VARCHAR(length=64),
               nullable=True)
        batch_op.alter_column('payment_method',
               existing_type=sa.VARCHAR(length=32),
               nullable=True)
        batch_op.drop_column('subscription_id')

    # 15. 更新 subscription_types 表
    # 先更新可能的 NULL 值
    op.execute("UPDATE subscription_types SET price = 0 WHERE price IS NULL")
    op.execute("UPDATE subscription_types SET days = 30 WHERE days IS NULL")
    op.execute("UPDATE subscription_types SET max_devices = 1 WHERE max_devices IS NULL")
    
    with op.batch_alter_table('subscription_types', schema=None) as batch_op:
        batch_op.add_column(sa.Column('default_subscription_instance_capacity', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('description', sa.Text(), nullable=True))
        batch_op.alter_column('price',
               existing_type=sa.FLOAT(),
               nullable=False)
        batch_op.alter_column('days',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('max_devices',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.drop_column('discount')

    # 16. 更新 users 表
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('email_verified_at')
        batch_op.drop_column('is_email_verified')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_email_verified', sa.BOOLEAN(), nullable=True))
        batch_op.add_column(sa.Column('email_verified_at', sa.DATETIME(), nullable=True))

    with op.batch_alter_table('subscription_types', schema=None) as batch_op:
        batch_op.add_column(sa.Column('discount', sa.INTEGER(), nullable=True))
        batch_op.alter_column('max_devices',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('days',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('price',
               existing_type=sa.FLOAT(),
               nullable=True)
        batch_op.drop_column('description')
        batch_op.drop_column('default_subscription_instance_capacity')

    with op.batch_alter_table('payments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_id', sa.INTEGER(), nullable=True))
        batch_op.create_foreign_key(None, 'subscriptions', ['subscription_id'], ['id'])
        batch_op.alter_column('payment_method',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
        batch_op.alter_column('payment_id',
               existing_type=sa.VARCHAR(length=64),
               nullable=False)

    with op.batch_alter_table('adspower_accounts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('subscription_type', sa.VARCHAR(length=32), nullable=True))
        batch_op.add_column(sa.Column('remarks', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('api_key', sa.VARCHAR(length=128), nullable=True))
        batch_op.drop_constraint('fk_adspower_accounts_subscription_instance_id', type_='foreignkey')
        batch_op.drop_constraint('uq_adspower_accounts_username', type_='unique')
        batch_op.alter_column('max_devices',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.drop_column('updated_at')
        batch_op.drop_column('description')
        batch_op.drop_column('subscription_instance_id')

    op.create_table('user_adspower_account',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('adspower_account_id', sa.INTEGER(), nullable=False),
    sa.Column('assigned_at', sa.DATETIME(), nullable=True),
    sa.Column('last_login', sa.DATETIME(), nullable=True),
    sa.Column('login_count', sa.INTEGER(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.ForeignKeyConstraint(['adspower_account_id'], ['adspower_accounts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('email_verifications',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('email', sa.VARCHAR(length=120), nullable=False),
    sa.Column('code', sa.VARCHAR(length=10), nullable=False),
    sa.Column('code_type', sa.VARCHAR(length=20), nullable=False),
    sa.Column('is_used', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('expires_at', sa.DATETIME(), nullable=False),
    sa.Column('used_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('payment_record',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('amount', sa.FLOAT(), nullable=False),
    sa.Column('payment_method', sa.VARCHAR(length=50), nullable=True),
    sa.Column('transaction_id', sa.VARCHAR(length=128), nullable=True),
    sa.Column('payment_status', sa.VARCHAR(length=20), nullable=True),
    sa.Column('payment_time', sa.DATETIME(), nullable=True),
    sa.Column('subscription_days', sa.INTEGER(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('login_session',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('adspower_account_id', sa.INTEGER(), nullable=True),
    sa.Column('login_token', sa.VARCHAR(length=128), nullable=False),
    sa.Column('login_time', sa.DATETIME(), nullable=True),
    sa.Column('logout_time', sa.DATETIME(), nullable=True),
    sa.Column('completed_time', sa.DATETIME(), nullable=True),
    sa.Column('ip_address', sa.VARCHAR(length=50), nullable=True),
    sa.Column('user_agent', sa.VARCHAR(length=255), nullable=True),
    sa.Column('login_status', sa.VARCHAR(length=20), nullable=True),
    sa.Column('device_info', sa.TEXT(), nullable=True),
    sa.Column('session_duration_seconds', sa.INTEGER(), nullable=True),
    sa.Column('initial_devices_count', sa.INTEGER(), nullable=True),
    sa.Column('initial_devices_info', sa.TEXT(), nullable=True),
    sa.Column('known_devices', sa.TEXT(), nullable=True),
    sa.Column('expiration_timestamp', sa.DATETIME(), nullable=False),
    sa.Column('known_devices_snapshot', sa.TEXT(), nullable=True),
    sa.ForeignKeyConstraint(['adspower_account_id'], ['adspower_accounts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('login_session', schema=None) as batch_op:
        batch_op.create_index('ix_login_session_login_token', ['login_token'], unique=1)

    op.create_table('chatgpt_accounts',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('email', sa.VARCHAR(length=64), nullable=False),
    sa.Column('username', sa.VARCHAR(length=64), nullable=True),
    sa.Column('password', sa.VARCHAR(length=128), nullable=False),
    sa.Column('totp_secret', sa.VARCHAR(length=128), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('user_id', sa.INTEGER(), nullable=True),
    sa.Column('assigned_at', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('max_users', sa.INTEGER(), nullable=True),
    sa.Column('current_users', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email')
    )
    
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('chatgpt_account_id', sa.INTEGER(), nullable=True))
        batch_op.drop_constraint(batch_op.f('fk_subscriptions_subscription_type_code_subscription_types'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_subscriptions_payment_id_payments'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('fk_subscriptions_subscription_instance_id_subscription_instances'), type_='foreignkey')
        batch_op.create_foreign_key(None, 'chatgpt_accounts', ['chatgpt_account_id'], ['id'])
        batch_op.alter_column('payment_id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
        batch_op.drop_column('status')
        batch_op.drop_column('subscription_instance_id')
        batch_op.drop_column('subscription_type_code')

    with op.batch_alter_table('login_sessions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_login_sessions_login_token'))

    op.drop_table('login_sessions')
    op.drop_table('subscription_instances')
    op.drop_table('payment_records')
    # ### end Alembic commands ###
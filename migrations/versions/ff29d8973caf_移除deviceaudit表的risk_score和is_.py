"""移除DeviceAudit表的risk_score和is_suspicious字段

Revision ID: ff29d8973caf
Revises: ac9411e39332
Create Date: 2025-06-05 16:27:53.335025

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ff29d8973caf'
down_revision = 'ac9411e39332'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('device_audits', schema=None) as batch_op:
        batch_op.drop_column('is_suspicious')
        batch_op.drop_column('risk_score')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('device_audits', schema=None) as batch_op:
        batch_op.add_column(sa.Column('risk_score', sa.INTEGER(), nullable=True))
        batch_op.add_column(sa.Column('is_suspicious', sa.BOOLEAN(), nullable=True))

    # ### end Alembic commands ###

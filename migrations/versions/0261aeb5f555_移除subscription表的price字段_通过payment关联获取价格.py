"""移除Subscription表的price字段_通过payment关联获取价格

Revision ID: 0261aeb5f555
Revises: 1eb33b773986
Create Date: 2025-05-24 18:07:19.457318

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0261aeb5f555'
down_revision = '1eb33b773986'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.drop_column('price')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('price', sa.FLOAT(), nullable=True))
    # ### end Alembic commands ###

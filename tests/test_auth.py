"""
Authentication API Tests
"""
import pytest


def test_login_success(client):
    """Test successful login"""
    # 首先创建用户
    from adspower_manager.models import User
    from extensions import db
    
    with client.application.app_context():
        user = User(email='<EMAIL>', is_active=True)
        user.set_password('password123')
        db.session.add(user)
        db.session.commit()
    
    # 测试登录
    response = client.post('/api/auth/login', json={
        'email': '<EMAIL>',
        'password': 'password123'
    })
    
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] is True
    assert 'access_token' in data['data']


def test_login_invalid_credentials(client):
    """Test login with invalid credentials"""
    response = client.post('/api/auth/login', json={
        'email': '<EMAIL>',
        'password': 'wrongpass'
    })
    
    assert response.status_code == 401
    data = response.get_json()
    assert data['success'] is False


def test_protected_route_without_token(client):
    """Test accessing protected route without token"""
    response = client.get('/api/user/profile')
    
    assert response.status_code == 401
    data = response.get_json()
    assert data['success'] is False


def test_protected_route_with_token(client, auth_headers):
    """Test accessing protected route with valid token"""
    response = client.get('/api/user/profile', headers=auth_headers)
    
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] is True
import os
import logging
from datetime import timedelta

# 获取 config.py 文件所在的目录的绝对路径
basedir = os.path.abspath(os.path.dirname(__file__))

class Config(object):
    # --- 核心 Flask 应用配置 ---
    # 重要提示：在生产环境中务必通过环境变量或实例配置文件 (e.g., instance/application.cfg) 设置 SECRET_KEY！
    # 例如: SECRET_KEY = "a_very_strong_and_random_secret_key"
    # JWT_SECRET_KEY 通常也依赖于此。
    SECRET_KEY = os.environ.get('SECRET_KEY')
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 实例文件夹路径，确保 app 创建时使用 instance_relative_config=True
    INSTANCE_FOLDER_PATH = os.path.join(basedir, 'instance')
    DB_NAME = 'app.db' # 数据库文件名，用于拼接SQLALCHEMY_DATABASE_URI

    # --- JWT 配置 --- 
    # JWT 密钥，优先使用环境变量 JWT_SECRET_KEY，否则回退到 Flask 的 SECRET_KEY
    # 如果 SECRET_KEY 为 None, JWT_SECRET_KEY 也将为 None, create_app 中应有检查机制
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or (SECRET_KEY if SECRET_KEY else None)
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=int(os.environ.get('JWT_ACCESS_TOKEN_EXPIRES_HOURS', 24)))
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=int(os.environ.get('JWT_REFRESH_TOKEN_EXPIRES_DAYS', 30)))

    # --- 应用特定配置 ---
    PORT = int(os.environ.get('PORT', 5000)) # Flask 开发服务器运行的端口

    # --- 邮件配置 ---
    MAIL_SERVER = os.environ.get('MAIL_SERVER') # e.g., 'smtp.163.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 465)) # (587 for TLS, 465 for SSL)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'False').lower() == 'true'
    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'True').lower() == 'true' # 163 邮箱推荐 SSL
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') # 你的邮箱账号
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') # 你的邮箱授权码
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '"AI服务拼车共享平台" <<EMAIL>>')
    EMAIL_CODE_EXPIRY_MINUTES = int(os.environ.get('EMAIL_CODE_EXPIRY_MINUTES', 10))
    # MAIL_DEBUG 在 ProductionConfig/TestingConfig 中设置

    # --- 密码策略 ---
    PASSWORD_MIN_LENGTH = int(os.environ.get('PASSWORD_MIN_LENGTH', 8))
    PASSWORD_REQUIRE_NUMBER = os.environ.get('PASSWORD_REQUIRE_NUMBER', 'True').lower() == 'true'
    PASSWORD_REQUIRE_SPECIAL_CHAR = os.environ.get('PASSWORD_REQUIRE_SPECIAL_CHAR', 'False').lower() == 'true'
    FAILED_LOGIN_MAX_ATTEMPTS = int(os.environ.get('FAILED_LOGIN_MAX_ATTEMPTS', 5))
    FAILED_LOGIN_LOCKOUT_TIME = int(os.environ.get('FAILED_LOGIN_LOCKOUT_TIME', 300))
    
    # --- 支付配置 ---
    MOCK_PAYMENT_ENABLED = os.environ.get('MOCK_PAYMENT_ENABLED', 'False').lower() == 'true'


    # --- 易支付 (Epay) 配置 ---
    EPAY_PID = os.environ.get('EPAY_PID')
    EPAY_KEY = os.environ.get('EPAY_KEY')
    EPAY_SUBMIT_URL = os.environ.get('EPAY_SUBMIT_URL', 'https://pay.netzz.net/submit.php') # Submit接口地址
    EPAY_API_URL = os.environ.get('EPAY_API_URL', 'https://pay.netzz.net/mapi.php') # MAPI接口地址 (备用)
    
    # EPAY_NOTIFY_URL 和 EPAY_RETURN_URL 在 Production/Testing Config 中定义

    # --- OIDC (Keycloak) 配置 ---
    OIDC_ISSUER_URL = os.environ.get('OIDC_ISSUER_URL', 'https://auth.chatgptpro.club/realms/chatgptpro.club') # 不设置默认值，未配置时回退到本地登录
    OIDC_CLIENT_ID = os.environ.get('OIDC_CLIENT_ID', 'chatgpt-pro-client')
    OIDC_CLIENT_SECRET = os.environ.get('OIDC_CLIENT_SECRET', 'TUdtLIKi3FOlXNwkGmdmr43EjPxJJJ5m')
    # OIDC_REDIRECT_URI 在 Production/Testing Config 中定义
    OIDC_SCOPES = os.environ.get('OIDC_SCOPES', 'openid email profile').split(' ')
    OIDC_JIT_USER_PROVISIONING = os.environ.get('OIDC_JIT_USER_PROVISIONING', 'True').lower() == 'true'

    # --- 认证模式配置 ---
    # USE_SSO: 控制是否使用单点登录（SSO）
    # True: 使用OIDC/Keycloak单点登录
    # False: 使用本地登录（邮箱+密码）
    USE_SSO = os.environ.get('USE_SSO', 'True').lower() == 'true'

    # WebDriver 配置
    ENABLE_WEBDRIVER = os.environ.get('ENABLE_WEBDRIVER', 'False').lower() == 'true'  # 默认禁用
    
    # AdsPower API 模式配置
    # True: 使用协议模式（HTTP API）（默认）
    # False: 使用Selenium模式
    USE_PROTOCOL_MODE = os.environ.get('USE_PROTOCOL_MODE', 'True').lower() == 'true'
    
    LOG_LEVEL = logging.INFO # 默认日志级别

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False
    LOG_LEVEL = logging.INFO # 生产环境日志级别
    
    # 生产环境默认使用SSO，可通过环境变量覆盖
    # USE_SSO 已在基类Config中从环境变量读取，默认为True
    
    # 生产环境密钥配置 - 优先使用环境变量，否则使用默认值
    SECRET_KEY = os.environ.get('SECRET_KEY', 'prod-secret-key-2024-gptshare-do-not-expose-this')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'prod-jwt-key-2024-gptshare-keep-secure')

    # 生产数据库路径，优先环境变量 DATABASE_URL，否则使用 instance 文件夹中的 SQLite
    DB_PATH = os.path.join(Config.INSTANCE_FOLDER_PATH, Config.DB_NAME)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'sqlite:///{DB_PATH}'
    
    # 生产环境邮件配置
    MAIL_DEBUG = False

    # 生产环境回调/通知 URL
    OIDC_REDIRECT_URI = os.environ.get('OIDC_REDIRECT_URI', 'https://dashboard.chatgptpro.club/api/oidc/callback')
    
    # --- 易支付 (Epay / 彩虹易支付) 配置 ---
    EPAY_PID = os.environ.get('EPAY_PID', '1683') # 你的商户ID
    EPAY_KEY = os.environ.get('EPAY_KEY', 'ygn4JZ0ICFEXZtyFiupDCVnlTSqBFuZ0') # 你的商户密钥
    EPAY_NOTIFY_URL = os.environ.get('EPAY_NOTIFY_URL', 'https://dashboard.chatgptpro.club/api/payments/epay/notify') # 异步通知URL
    EPAY_RETURN_URL = os.environ.get('EPAY_RETURN_URL', 'https://dashboard.chatgptpro.club/payments/epay/result') # 同步跳转URL

class TestingConfig(Config):
    TESTING = True
    DEBUG = True # 测试环境开启DEBUG
    LOG_LEVEL = logging.INFO # 测试环境日志级别
    
    # 测试环境默认使用单点登录，可通过环境变量覆盖
    USE_SSO = os.environ.get('USE_SSO', 'True').lower() == 'true'

    # 测试时使用固定的 SECRET_KEY 和 JWT_SECRET_KEY
    SECRET_KEY = 'dummy_test_secret_key_please_change_for_real_tests_if_needed'
    JWT_SECRET_KEY = SECRET_KEY

    # 测试数据库使用实际文件而非内存
    DB_PATH = os.path.join(Config.INSTANCE_FOLDER_PATH, 'test_' + Config.DB_NAME)
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DB_PATH}'

    # --- 易支付 (Epay / 彩虹易支付) 配置 ---
    EPAY_PID = os.environ.get('EPAY_PID', '1683') # 你的商户ID
    EPAY_KEY = os.environ.get('EPAY_KEY', 'ygn4JZ0ICFEXZtyFiupDCVnlTSqBFuZ0') # 你的商户密钥
    EPAY_NOTIFY_URL = os.environ.get('EPAY_NOTIFY_URL', 'https://dashboard-test.chatgptpro.club/api/payments/epay/notify') # 异步通知URL
    EPAY_RETURN_URL = os.environ.get('EPAY_RETURN_URL', 'https://dashboard-test.chatgptpro.club/payments/epay/result') # 同步跳转URL

    # 测试邮件配置
    MAIL_SUPPRESS_SEND = True # 使用 Flask-Mail 的此配置禁止实际发送邮件 (需要Flask-Mail支持)
                               # 或者在测试中 mock email sending
    MAIL_DEBUG = True # 可以开启邮件调试信息
    MAIL_SERVER = 'localhost'
    MAIL_PORT = 1025 # MailHog/MailCatcher 等本地SMTP服务器常用端口
    MAIL_USE_TLS = False
    MAIL_USE_SSL = False
    MAIL_USERNAME = '<EMAIL>'
    MAIL_PASSWORD = 'testpassword'
    MAIL_DEFAULT_SENDER = '"Test Sender" <<EMAIL>>'

    OIDC_REDIRECT_URI = os.environ.get('OIDC_REDIRECT_URI', 'https://dashboard-test.chatgptpro.club/api/oidc/callback')

    # 测试环境模拟支付配置 - 优先使用环境变量，默认启用
    MOCK_PAYMENT_ENABLED = os.environ.get('MOCK_PAYMENT_ENABLED', 'False').lower() == 'true'
 
    WTF_CSRF_ENABLED = False # 在测试表单时，禁用 CSRF 保护通常更方便

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ChatGPTPro俱乐部 - 系统维护中">
    <title>ChatGPTPro俱乐部 - 系统维护中</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50; /* 深宝蓝色，体现专业高级感 */
            --hover-color: #1c2e40;
            --accent-color: #3498db; /* 鲜亮蓝，体现科技感 */
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --bg-color: #f8f9fa;
            --secondary-bg: #ecf0f1;
            --card-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            --input-border: #dfe6e9;
            --text-color: #2c3e50;
            --text-muted: #7f8c8d;
            --highlight-color: #f39c12; /* 暖金色，增加高级质感 */
            --gold-color: #f39c12; /* 金色，传达尊贵感 */
            --premium-gradient: linear-gradient(135deg, #f39c12 0%, #fdcb6e 50%, #f39c12 100%); /* 尊贵渐变 */
        }
        
        html, body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            color: var(--text-color);
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }

        .maintenance-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .maintenance-card {
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 550px;
            width: 90%;
            text-align: center;
            padding: 40px;
            position: relative;
            overflow: hidden;
            border-left: 4px solid var(--accent-color);
        }

        .maintenance-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(52, 152, 219, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            z-index: 0;
        }

        .maintenance-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .maintenance-subtitle {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--accent-color);
        }

        .maintenance-description {
            margin-bottom: 30px;
            color: var(--text-color);
            line-height: 1.6;
        }

        .maintenance-icon {
            font-size: 70px;
            color: var(--accent-color);
            margin-bottom: 20px;
            animation: pulse 2s infinite ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }


        .maintenance-time {
            font-size: 14px;
            color: var(--text-muted);
            margin-bottom: 30px;
        }


        .feature-list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            background-color: rgba(52, 152, 219, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            color: var(--primary-color);
        }

        .feature-item i {
            margin-right: 8px;
            color: var(--accent-color);
        }

        .social-links {
            margin-top: 20px;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            background-color: white;
            color: var(--accent-color);
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin: 0 5px;
        }

        .social-link:hover {
            background-color: var(--accent-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .social-link i {
            margin-right: 6px;
        }

        /* 隐藏的点击计数器 */
        .click-count {
            display: none;
        }
        
        /* 齿轮图标作为隐藏入口 */
        .maintenance-icon {
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .maintenance-icon:hover {
            transform: scale(1.2) rotate(180deg);
        }

        @media (max-width: 576px) {
            .maintenance-card {
                padding: 30px 20px;
            }
            
            .maintenance-title {
                font-size: 24px;
            }
            
            .maintenance-subtitle {
                font-size: 16px;
            }
            
            .feature-list {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-card">
            <i class="bi bi-gear-fill maintenance-icon" id="secretGear"></i>
            <h1 class="maintenance-title">系统维护中</h1>
            <h2 class="maintenance-subtitle">我们正在进行系统升级</h2>
            
            <p class="maintenance-description">
                尊敬的用户，我们正在对ChatGPTPro俱乐部进行系统升级，以提供更好的用户体验。
                维护期间您将无法访问网站，我们将尽快恢复服务。感谢您的理解与支持！
            </p>
            
            
            <div class="feature-list">
                <div class="feature-item">
                    <i class="bi bi-speedometer2"></i>
                    <span>性能优化</span>
                </div>
                <div class="feature-item">
                    <i class="bi bi-shield-check"></i>
                    <span>安全加固</span>
                </div>
                <div class="feature-item">
                    <i class="bi bi-lightning-charge"></i>
                    <span>新功能上线</span>
                </div>
            </div>
            
            <p class="maintenance-time">
                预计完成时间: <strong>2025年5月29日 18:00</strong>
            </p>
            
            <span class="click-count" id="clickCount">0</span>
            
            <div class="social-links">
                <a href="https://t.me/gptproclub" target="_blank" class="social-link">
                    <i class="bi bi-telegram"></i>
                    <span>官方频道</span>
                </a>
                <a href="https://t.me/chatgptpro_notification" target="_blank" class="social-link">
                    <i class="bi bi-bell-fill"></i>
                    <span>通知频道</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const secretGear = document.getElementById('secretGear');
            const clickCount = document.getElementById('clickCount');
            let count = 0;
            
            secretGear.addEventListener('click', function() {
                count++;
                clickCount.textContent = count;
                
                // 检查是否点击了三次
                if (count === 3) {
                    // 齿轮旋转动画
                    secretGear.style.animation = 'pulse 0.5s infinite ease-in-out';
                    
                    // 设置cookie，允许返回原网站，有效期1小时
                    document.cookie = "bypass_maintenance=true; path=/; max-age=3600;";
                    
                    // 延迟后重定向到主页
                    setTimeout(function() {
                        window.location.href = '/';
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ChatGPTPro俱乐部 - 尊贵AI拼车体验，多级拼车服务，专业技术支持，为您节省90%使用成本">
    <title>ChatGPTPro俱乐部 - 尊享AI拼车服务</title>
    <link rel="icon" href="static/images/logo.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .pricing-card {
            transition: transform 0.3s;
            height: 100%;
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,.1);
        }
        
        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        .testimonial-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid rgba(52, 152, 219, 0.1);
            transition: all 0.3s ease;
        }
        
        .testimonial-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,.1);
        }
        
        .community-btn {
            background-color: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            color: var(--accent-color);
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.375rem 0.75rem;
        }
        
        .community-btn:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .community-btn i {
            font-size: 1.2rem;
        }
        
        .community-btn.telegram:hover {
            background-color: #0088cc;
            border-color: #0088cc;
        }
        
        .community-btn.discord:hover {
            background-color: #5865F2;
            border-color: #5865F2;
        }
    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="static/images/logo.png" alt="Logo" height="30" class="d-inline-block align-text-top">
                ChatGPTPro俱乐部
            </a>
            <div class="navbar-nav ms-auto d-flex align-items-center">
                <!-- Community Links -->
                <div class="d-none d-md-flex me-3 gap-2">
                    <a href="https://t.me/gptproclub" target="_blank" rel="noopener noreferrer" class="btn community-btn telegram d-flex align-items-center">
                        <i class="bi bi-telegram me-1"></i> TG群
                    </a>
                    <a href="https://t.me/chatgptpro_notification" target="_blank" rel="noopener noreferrer" class="btn community-btn telegram d-flex align-items-center">
                        <i class="bi bi-broadcast me-1"></i> TG频道
                    </a>
                    <a href="https://discord.gg/d6FnJKrekQ" target="_blank" rel="noopener noreferrer" class="btn community-btn discord d-flex align-items-center">
                        <i class="bi bi-discord me-1"></i> Discord
                    </a>
                </div>
                <a class="btn btn-primary" href="/login" id="login-btn">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">欢迎来到 ChatGPTPro俱乐部</h1>
            <p class="lead mb-5">尊贵AI助手拼车体验，为您节省90%使用成本</p>
            <a href="#pricing" class="btn btn-light btn-lg me-3">
                <i class="bi bi-cart3"></i> 查看套餐
            </a>
            <a href="/login" class="btn btn-outline-light btn-lg">
                <i class="bi bi-box-arrow-in-right"></i> 立即登录
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">为什么选择我们</h2>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-primary">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h4>安全可靠</h4>
                    <p class="text-muted">基于指纹浏览器技术，环境安全隔离，不降智不封号，原生官网账号</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-success">
                        <i class="bi bi-people"></i>
                    </div>
                    <h4>灵活选择</h4>
                    <p class="text-muted">1人独享车、5人车、10人车等多种套餐，满足不同需求</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-info">
                        <i class="bi bi-headset"></i>
                    </div>
                    <h4>专业服务</h4>
                    <p class="text-muted">多位成员提供支持，快速响应，专业技术团队保障服务质量</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-5 bg-light" id="pricing">
        <div class="container">
            <h2 class="text-center mb-5">选择您的套餐</h2>
            <div class="row" id="pricing-cards">
                <!-- Pricing cards will be loaded here -->
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载套餐信息...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card testimonial-card shadow-sm">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-quote text-primary" style="font-size: 2rem; line-height: 1;"></i>
                                <div class="ms-3">
                                    <p class="mb-3" style="font-style: italic;">
                                        加入5人共享后，GPT-4o生成的参考图帮我突破了设计瓶颈，素材准备时间从2小时缩短到20分钟。图像质量好到让客户惊讶，整个设计流程更加流畅高效。
                                    </p>
                                    <p class="mb-0 text-end">
                                        <strong>— 陈总</strong>
                                        <span class="text-muted">，创意公司合作伙伴</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 text-center">
        <div class="container">
            <h3 class="mb-4">准备好开始了吗？</h3>
            <p class="lead mb-4">加入200+尊贵会员，享受高品质AI服务</p>
            <a href="/login" class="btn btn-primary btn-lg">
                <i class="bi bi-box-arrow-in-right"></i> 立即加入
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4 bg-dark text-white text-center">
        <div class="container">
            <p class="mb-0">&copy; 2025 ChatGPTPro俱乐部. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check if user is logged in and redirect accordingly
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            
            if (token && user.id) {
                // User is logged in, redirect to appropriate page
                document.querySelector('.loading-spinner').style.display = 'block';
                if (user.is_admin) {
                    window.location.href = '/admin';
                } else {
                    window.location.href = '/dashboard';
                }
                return;
            }
            
            // Load pricing plans
            loadPricingPlans();
        });
        
        function loadPricingPlans() {
            fetch('/api/public/subscription-types')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('pricing-cards');
                    
                    if (data.success && data.data && data.data.types && data.data.types.length > 0) {
                        container.innerHTML = '';
                        
                        data.data.types.forEach(type => {
                            const col = document.createElement('div');
                            col.className = 'col-lg-4 col-md-6 mb-4';
                            
                            col.innerHTML = `
                                <div class="card pricing-card">
                                    <div class="card-header text-center">
                                        <h4>${type.name}</h4>
                                    </div>
                                    <div class="card-body">
                                        <h2 class="text-center mb-4">¥${type.price.toFixed(2)}<small class="text-muted">/${type.days}天</small></h2>
                                        <ul class="list-unstyled">
                                            <li class="mb-2"><i class="bi bi-check-circle text-success"></i> 最多${type.max_devices}台设备</li>
                                            ${type.requirements ? type.requirements.split('\n').filter(line => line.trim()).map(line => 
                                                `<li class="mb-2"><i class="bi bi-check-circle text-success"></i> ${line.trim()}</li>`
                                            ).join('') : ''}
                                        </ul>
                                        <div class="text-center mt-4">
                                            <a href="/login" class="btn btn-outline-primary w-100">
                                                <i class="bi bi-box-arrow-in-right"></i> 登录后订阅
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            container.appendChild(col);
                        });
                    } else {
                        container.innerHTML = '<div class="col-12 text-center"><p>暂无可用套餐</p></div>';
                    }
                })
                .catch(error => {
                    console.error('Failed to load pricing:', error);
                    document.getElementById('pricing-cards').innerHTML = 
                        '<div class="col-12 text-center"><p class="text-danger">加载套餐信息失败，请稍后重试</p></div>';
                });
        }
    </script>
</body>
</html>
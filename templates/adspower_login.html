<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdsPower登录信息</title>
    <link href="https://gcore.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://gcore.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa; /* Slightly lighter background */
            display: flex;
            align-items: center;
            padding-top: 20px; /* Reduce top padding */
            padding-bottom: 20px;
            min-height: 100vh;
        }
        .login-container {
            width: 100%;
            max-width: 900px; /* Wider container for horizontal layout */
            padding: 15px;
            margin: auto;
        }
        .card {
            border-radius: 15px; /* Slightly more rounded corners */
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
            overflow: hidden; /* Ensure child elements adhere to rounded corners */
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            padding: 1rem 1.5rem; /* More padding in header */
        }
        .card-body {
            padding: 2rem; /* More padding in body */
        }
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .totp-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .copy-btn {
            cursor: pointer;
        }
        /* Removed step styling as instructions are simpler now */
        .login-timer {
            margin-top: 1rem;
            margin-bottom: 1.5rem;
        }
        .progress {
            height: 10px; /* Slimmer progress bar */
            margin-top: 5px;
            border-radius: 5px;
        }
        .progress-bar {
             border-radius: 5px;
        }
        .timer-text {
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        #login-success-alert {
            display: none;
        }
        .instructions-card {
             background-color: #e9ecef; /* Light background for instructions */
             border: none;
             height: 100%; /* Make instruction card fill column height */
             display: flex;
             flex-direction: column;
        }
        /* 警告横幅样式 */
        .warning-banner {
            background-color: #ffc107;
            color: #000;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: bold;
            font-size: 1.1rem;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .warning-banner i {
            font-size: 1.5rem;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        /* 确认按钮高亮样式 */
        .confirm-btn-highlight {
            animation: glow 2s ease-in-out infinite alternate;
            position: relative;
        }
        @keyframes glow {
            from { box-shadow: 0 0 10px #28a745, 0 0 20px #28a745; }
            to { box-shadow: 0 0 20px #28a745, 0 0 30px #28a745; }
        }
        /* 浏览器标题闪烁 */
        .floating-reminder {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #dc3545;
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            display: none;
            align-items: center;
            gap: 10px;
            z-index: 1050;
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        /* 关键步骤强调 */
        .critical-step {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding-left: 12px !important;
            font-weight: bold;
        }
        .instructions-card .card-body {
            flex-grow: 1; /* Allow body to grow */
        }
        .action-buttons {
            margin-top: auto; /* Push buttons to the bottom of the instruction card */
            padding-top: 1rem;
        }
        .info-column {
             border-right: 1px solid #dee2e6; /* Add a separator line */
        }

        @media (max-width: 767.98px) {
            .info-column {
                border-right: none; /* Remove separator on smaller screens */
                border-bottom: 1px solid #dee2e6; /* Add separator below */
                padding-bottom: 1.5rem;
                margin-bottom: 1.5rem;
            }
             .login-container {
                max-width: 500px; /* Revert to narrower container */
             }
             .card-body {
                 padding: 1.5rem;
             }
             .instructions-card {
                 height: auto;
             }
             .action-buttons {
                 margin-top: 1rem;
             }
        }
        #totp-section { /* Style for the TOTP section container */
            /* display: none; Initially hidden, JS will show it if needed */
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="bi bi-shield-lock"></i> AdsPower登录信息</h4>
            </div>
            <div class="card-body">
                <!-- 警告横幅 -->
                <div class="warning-banner">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                    <span>重要提醒：登录AdsPower后必须返回此页面确认设备，否则登录会被系统自动撤销！</span>
                </div>
                 <div class="row">
                    <!-- Left Column: Login Info & Timer -->
                    <div class="col-md-6 info-column">
                        <div class="alert alert-info small mb-3">
                            <i class="bi bi-info-circle"></i> 会话将在 <strong id="session-valid-until">--:--</strong> 过期，请尽快登录。
                        </div>

                        <div class="login-timer">
                            <div class="timer-text">会话剩余时间: <span id="countdown-timer">--:--</span></div>
                            <div class="progress">
                                <div id="login-progress" class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>

                        <div id="login-success-alert" class="alert alert-success">
                            <i class="bi bi-check-circle"></i> <strong>登录成功!</strong> 您已成功登录AdsPower账号。
                        </div>

                        <form>
                            <div class="mb-3">
                                <label for="adspower-username" class="form-label">用户名</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="adspower-username" value="{{ username }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" onclick="copyToClipboard('adspower-username', event)">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="adspower-password" class="form-label">密码</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="adspower-password" value="{{ password }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" onclick="copyToClipboard('adspower-password', event)">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- TOTP Section - add id, initially hide -->
                            <div class="mb-3" id="totp-section" style="display: none;">
                                <label for="adspower-totp" class="form-label">验证器验证码</label>
                                <div class="totp-container">
                                    <input type="text" class="form-control" id="adspower-totp" value="" readonly>
                                    <span class="badge bg-secondary" id="totp-timer">--</span>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" onclick="copyToClipboard('adspower-totp', event)">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- End TOTP Section -->
                        </form>
                    </div>

                    <!-- Right Column: Instructions & Actions -->
                    <div class="col-md-6">
                         <div class="card instructions-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3"><i class="bi bi-info-circle-fill text-info"></i> 登录步骤与设备确认</h5>
                                <ol class="list-group list-group-numbered list-group-flush">
                                    <li class="list-group-item bg-transparent border-0 px-0">在AdsPower客户端使用左侧提供的账号密码登录。</li>
                                    <li class="list-group-item bg-transparent border-0 px-0 critical-step"><i class="bi bi-arrow-return-left"></i> <strong style="color: #dc3545;">登录成功后立即返回此页面！</strong></li>
                                    <li class="list-group-item bg-transparent border-0 px-0 critical-step"><i class="bi bi-hand-index-thumb"></i> 点击<strong>"我已在客户端登录，开始验证"</strong>按钮完成设备确认。</li>
                                    <li class="list-group-item bg-transparent border-0 px-0">系统将显示设备信息供您确认。</li>
                                    <li class="list-group-item bg-transparent border-0 px-0" style="color: #dc3545;"><i class="bi bi-x-circle"></i> <strong>警告：</strong>未确认的设备将在3分钟内被强制下线。</li>
                                </ol>
                            </div>
                            <div class="card-footer bg-transparent border-0 action-buttons">
                                <div class="d-grid gap-2">
                                    <button id="confirm-login-btn" type="button" class="btn btn-success btn-lg confirm-btn-highlight">
                                        <i class="bi bi-check2-circle"></i> 我已在客户端登录，开始验证
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary mt-2" onclick="cancelAndReturn()">
                                        <i class="bi bi-arrow-left"></i> 返回控制面板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 浮动提醒 -->
    <div class="floating-reminder" id="floating-reminder">
        <i class="bi bi-bell-fill"></i>
        <span>请返回页面确认设备！</span>
    </div>

    <!-- New Device Confirmation Modal -->
    <div class="modal fade" id="confirmDeviceModal" tabindex="-1" aria-labelledby="confirmDeviceModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="confirmDeviceModalLabel"><i class="bi bi-exclamation-triangle-fill"></i> 检测到新设备登录</h5>
                </div>
                <div class="modal-body">
                    <p>系统检测到一个新的设备尝试使用此账号登录。请确认这是否是您当前的操作：</p>
                    <ul class="list-group mb-3">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            设备名称:
                            <strong id="modal-device-name">加载中...</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            IP 地址:
                            <strong id="modal-device-ip">加载中...</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            设备类型:
                            <strong id="modal-device-type">加载中...</strong>
                        </li>
                    </ul>
                    <p class="text-muted small">如果您不确认此设备，请点击"否"。为保障安全，此登录会话将被终止。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" id="modal-deny-btn">否，这不是我</button>
                    <button type="button" class="btn btn-success" id="modal-confirm-btn">确认是我的设备</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Modal -->

    <script src="https://gcore.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 账号ID - 用于检查登录状态
        const accountId = "{{ account_id }}";
        // 登录令牌 - 用于检查登录状态
        const loginToken = "{{ login_token }}";
        // 会话过期时间戳 (ISO格式 UTC)
        const expirationTimestampISO = "{{ expiration_timestamp_iso }}";
        const expirationTime = new Date(expirationTimestampISO);
        const totalDurationSeconds = 180; // 假设总时长还是180秒，用于进度条计算

        // 倒计时相关变量
        let sessionCountdownInterval;
        let loginCheckInterval; // This variable is kept but not used for interval setup anymore
        let isConfirmingDevice = false; // 添加标志位防止重复确认
        let timer; // TOTP timer variable
        let timerInterval; // TOTP interval ID
        let loginCompleted = false; // 标记登录是否已完成
        let sessionExpired = false; // 标记会话是否已过期

        // 标题闪烁提醒
        let titleInterval;
        let originalTitle = document.title;
        
        function startTitleBlink() {
            let isOriginal = true;
            titleInterval = setInterval(() => {
                document.title = isOriginal ? '⚠️ 请确认设备 - AdsPower' : originalTitle;
                isOriginal = !isOriginal;
            }, 1000);
        }
        
        function stopTitleBlink() {
            if (titleInterval) {
                clearInterval(titleInterval);
                document.title = originalTitle;
            }
        }

        // 启动会话倒计时
        function startSessionCountdown() {
            const progressBar = document.getElementById('login-progress');
            const countdownElement = document.getElementById('countdown-timer');
            const validUntilElement = document.getElementById('session-valid-until');

            // 设置初始的过期时间显示
            if (validUntilElement) {
                validUntilElement.textContent = expirationTime.toLocaleTimeString();
            }

            sessionCountdownInterval = setInterval(function() {
                const now = new Date();
                const remainingMilliseconds = expirationTime - now;
                const remainingSeconds = Math.max(0, Math.floor(remainingMilliseconds / 1000));

                // 更新进度条 (基于假设的总时长)
                const progressPercent = (remainingSeconds / totalDurationSeconds) * 100;
                updateProgressBar(progressPercent);

                // 更新倒计时文本
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                
                // 当剩余时间少于20秒时，禁用验证按钮
                if (remainingSeconds < 20 && remainingSeconds > 0) {
                    const confirmButton = document.getElementById('confirm-login-btn');
                    if (confirmButton && !confirmButton.disabled && !isConfirmingDevice) {
                        confirmButton.disabled = true;
                        confirmButton.innerHTML = '<i class="bi bi-clock-history"></i> 时间不足';
                        confirmButton.classList.remove('btn-success');
                        confirmButton.classList.add('btn-secondary');
                    }
                }

                // 检查是否倒计时结束
                if (remainingSeconds <= 0) {
                    clearInterval(sessionCountdownInterval);
                    // clearInterval(loginCheckInterval); // No interval to clear here
                    handleSessionExpired();
                }
            }, 1000);
        }

        // 检查是否有足够时间完成验证
        function hasEnoughTimeForVerification() {
            const now = new Date();
            const remainingMilliseconds = expirationTime - now;
            const remainingSeconds = Math.floor(remainingMilliseconds / 1000);
            return remainingSeconds >= 20; // 至少需要20秒
        }

        // 检查登录状态
        function checkLoginStatus() {
            // 如果正在确认设备，则跳过检查
            if (isConfirmingDevice) {
                console.log('正在确认设备，跳过本次状态检查');
                // Re-enable button if check was skipped during confirmation process?
                const confirmButton = document.getElementById('confirm-login-btn');
                 if (confirmButton) {
                     confirmButton.disabled = false;
                     confirmButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                 }
                return;
            }

            // 检查登录令牌是否存在
            if (!loginToken || loginToken.trim() === '') {
                console.error('登录令牌不存在，无法检查登录状态');
                alert('登录会话无效，请返回控制面板重新开始登录流程。');
                 // Re-enable button if token is missing
                 const confirmButton = document.getElementById('confirm-login-btn');
                 if (confirmButton) {
                     confirmButton.disabled = false;
                     confirmButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                 }
                // 3秒后跳转回控制面板
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 3000);
                return;
            }
            
            // 方案一：预防性处理 - 检查剩余时间
            if (!hasEnoughTimeForVerification()) {
                console.warn('会话剩余时间不足，无法完成验证');
                const confirmButton = document.getElementById('confirm-login-btn');
                if (confirmButton) {
                    confirmButton.disabled = false;
                    confirmButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                }
                
                // 显示时间不足警告
                showTimeoutWarning();
                return;
            }

            // 方案二：异步超时处理 - 创建超时监控
            let requestCompleted = false;
            const timeoutCheckInterval = setInterval(() => {
                if (!requestCompleted && !hasEnoughTimeForVerification()) {
                    console.warn('检测过程中会话超时');
                    clearInterval(timeoutCheckInterval);
                    
                    // 显示超时警告
                    showTimeoutWarning();
                    
                    // 禁用确认按钮
                    const confirmButton = document.getElementById('confirm-login-btn');
                    if (confirmButton) {
                        confirmButton.disabled = true;
                        confirmButton.innerHTML = '<i class="bi bi-x-circle"></i> 会话已超时';
                    }
                }
            }, 1000); // 每秒检查一次

            fetch(`/api/adspower/check-login-status?token=${loginToken}`)
                .then(response => {
                    // 检查 HTTP 状态码
                    if (!response.ok) {
                        console.error(`HTTP error! status: ${response.status}`);
                        return response.json().then(errData => {
                            throw new Error(errData.message || `HTTP ${response.status}`);
                        }).catch(() => {
                            throw new Error(`HTTP ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    requestCompleted = true;
                    clearInterval(timeoutCheckInterval);
                    
                    // 再次检查时间，确保还有时间处理响应
                    if (!hasEnoughTimeForVerification()) {
                        console.warn('请求完成但会话已接近超时');
                        showTimeoutWarning();
                        return;
                    }
                    console.log('登录状态检查结果:', data);

                    // 修正：从正确的路径访问status和new_device
                    const statusData = data.data || {};
                    const status = statusData.status;
                    
                    if (status === 'new_device_detected' && statusData.new_device) {
                        // 检测到新设备，停止定时检查，并询问用户
                        // clearInterval(loginCheckInterval); // No interval to clear
                        isConfirmingDevice = true; // 设置标志位
                        handleNewDevice(statusData.new_device);
                         // Keep button disabled during device confirmation
                    } else if (status === 'expired') {
                        handleSessionExpired();
                    } else if (status === 'error') {
                        console.error('API返回错误:', data.message);
                         alert(`检查登录状态时出错: ${data.message}`);
                         // Re-enable button on error
                         const confirmButton = document.getElementById('confirm-login-btn');
                         if (confirmButton) {
                             confirmButton.disabled = false;
                             confirmButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                         }
                    } else if (status === 'pending') {
                        console.log('状态为pending，继续等待用户操作...');
                        
                        // 显示内联提示而不是alert
                        const alertContainer = document.getElementById('login-success-alert').parentElement;
                        if (alertContainer) {
                            // 创建或更新提示信息
                            let pendingAlert = document.getElementById('pending-alert');
                            if (!pendingAlert) {
                                pendingAlert = document.createElement('div');
                                pendingAlert.id = 'pending-alert';
                                pendingAlert.className = 'alert alert-warning alert-dismissible fade show';
                                pendingAlert.innerHTML = `
                                    <i class="bi bi-exclamation-circle"></i> <strong>未检测到新设备登录</strong>
                                    <p class="mb-0 mt-2">请确保您已在AdsPower客户端成功登录后，再点击验证按钮。</p>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                `;
                                alertContainer.insertBefore(pendingAlert, document.getElementById('login-success-alert'));
                            }
                            pendingAlert.style.display = 'block';
                        }
                        
                        // Re-enable button
                        const confirmButton = document.getElementById('confirm-login-btn');
                        if (confirmButton) {
                            confirmButton.disabled = false;
                            confirmButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                        }
                    } else if (data.success && statusData.loggedIn) {
                         console.warn('意外地通过 check-login-status 检测到登录成功');
                         handleLoginSuccess(); // 作为备用逻辑保留?
                    }
                })
                .catch(error => {
                    requestCompleted = true;
                    clearInterval(timeoutCheckInterval);
                    
                    console.error('检查登录状态或处理响应时出错:', error);
                    
                    // 检查是否是因为超时导致的错误
                    if (!hasEnoughTimeForVerification()) {
                        showTimeoutWarning();
                        return;
                    }
                    
                    alert(`检查登录状态时发生网络错误: ${error.message}`);
                    // Re-enable button on fetch error
                    const confirmButton = document.getElementById('confirm-login-btn');
                    if (confirmButton) {
                        confirmButton.disabled = false;
                        confirmButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                    }
                    // clearInterval(loginCheckInterval); // No interval to clear
                });
        }

        // 处理新设备
        function handleNewDevice(deviceInfo) {
            const deviceName = deviceInfo.name || '未知设备';
            const deviceIp = deviceInfo.ip_address || '未知IP';
            const deviceType = deviceInfo.device_type || '未知类型';

            document.getElementById('modal-device-name').textContent = deviceName;
            document.getElementById('modal-device-ip').textContent = deviceIp;
            document.getElementById('modal-device-type').textContent = deviceType;

            const confirmModalElement = document.getElementById('confirmDeviceModal');
            const confirmModal = new bootstrap.Modal(confirmModalElement);

            const confirmBtn = document.getElementById('modal-confirm-btn');
            const denyBtn = document.getElementById('modal-deny-btn');
            // Clone and replace to remove previous listeners safely
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
            const newDenyBtn = denyBtn.cloneNode(true);
            denyBtn.parentNode.replaceChild(newDenyBtn, denyBtn);


            newConfirmBtn.addEventListener('click', () => {
                confirmModal.hide();
                confirmDeviceApiCall(deviceInfo);
            });

            newDenyBtn.addEventListener('click', () => {
                confirmModal.hide();
                // 调用取消登录 API
                cancelLogin('不是我的设备');
            });

            confirmModal.show();
        }

        // 取消并返回控制面板
        function cancelAndReturn() {
            cancelLogin('用户返回控制面板');
        }

        // 取消登录函数
        function cancelLogin(reason = '用户主动取消') {
            // 先显示取消中的UI
            const cardBody = document.querySelector('.card > .card-body');
            if (cardBody) {
                cardBody.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-secondary" role="status">
                            <span class="visually-hidden">正在取消...</span>
                        </div>
                        <h4 class="mt-3">正在取消登录会话...</h4>
                    </div>
                `;
            }
            
            fetch('/api/adspower/cancel-login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    login_token: loginToken
                })
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(sessionCountdownInterval);
                stopTitleBlink();
                isConfirmingDevice = false;
                loginCompleted = true; // 标记为已完成，防止beforeunload再次触发
                
                // 显示取消成功的UI
                if (cardBody) {
                    cardBody.innerHTML = `
                        <div class="text-center py-5">
                            <i class="bi bi-check-circle text-secondary" style="font-size: 4rem;"></i>
                            <h3 class="mt-3">登录已取消</h3>
                            <p class="text-muted">${reason}</p>
                            <p class="text-muted small">正在返回控制面板...</p>
                            <div class="progress mt-4" style="height: 5px;">
                                <div id="redirect-progress" class="progress-bar bg-secondary" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    `;
                    
                    // 1秒后跳转
                    setTimeout(() => window.location.href = '/dashboard', 1000);
                } else {
                    alert(`登录已取消: ${reason}`);
                    setTimeout(() => window.location.href = '/dashboard', 1000);
                }
            })
            .catch(error => {
                console.error('取消登录失败:', error);
                // 即使取消失败，也跳转回控制面板
                if (cardBody) {
                    cardBody.innerHTML = `
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-circle text-warning" style="font-size: 4rem;"></i>
                            <h3 class="mt-3">操作未完成</h3>
                            <p class="text-muted">正在返回控制面板...</p>
                        </div>
                    `;
                }
                setTimeout(() => window.location.href = '/dashboard', 1000);
            });
        }

        // 调用确认设备 API
        function confirmDeviceApiCall(deviceInfo) {
             fetch('/api/devices/confirm-new', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                     // No Authorization needed here as it uses the login_token
                },
                body: JSON.stringify({
                    login_token: loginToken,
                    device: deviceInfo
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    handleLoginSuccess(data.message || '新设备已成功添加，登录完成！');
                } else {
                    // 设备添加失败，显示错误并提供重试选项
                    showDeviceConfirmError(data.message || '设备添加失败');
                }
            })
            .catch(error => {
                console.error('确认设备 API 调用失败:', error);
                alert('确认设备时发生网络错误，请重试或联系管理员。');
                 // Re-enable button if API call fails
                 const confirmLoginButton = document.getElementById('confirm-login-btn');
                 if (confirmLoginButton) {
                     confirmLoginButton.disabled = false;
                     confirmLoginButton.innerHTML = '<i class=\"bi bi-check2-circle\"></i> 我已在客户端登录，开始验证';
                 }
            })
            .finally(() => {
                 isConfirmingDevice = false; // Reset flag after API call finishes
            });
        }

        // 处理登录成功 (现在由确认API成功后调用)
        function handleLoginSuccess(successMessage = '设备已确认，登录成功!') {
            loginCompleted = true; // 标记登录已完成
            clearInterval(sessionCountdownInterval); // 停止会话倒计时
            // clearInterval(loginCheckInterval); // No interval to clear
            stopTitleBlink(); // 停止标题闪烁
            
            // 隐藏浮动提醒
            const reminder = document.getElementById('floating-reminder');
            if (reminder) {
                reminder.style.display = 'none';
            }

            const cardBody = document.querySelector('.card > .card-body'); // Target the main card body

            if (cardBody) {
                // Replace entire content with success message
                cardBody.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        <h3 class="mt-3">${successMessage}</h3>
                        <p class="text-muted mt-3">正在返回控制面板，请稍候...</p>
                        <p class="text-muted small">将在 <strong>3</strong> 秒后自动跳转</p>
                        <div class="progress mt-4" style="height: 5px;">
                            <div id="redirect-progress" class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                `;

                // Start redirect countdown and progress bar
                let redirectSeconds = 3;
                const countdownElement = cardBody.querySelector('strong');
                const redirectProgressBar = document.getElementById('redirect-progress');

                const redirectInterval = setInterval(() => {
                    redirectSeconds--;
                    if(countdownElement) countdownElement.textContent = redirectSeconds;
                    if(redirectProgressBar) {
                         const percent = (redirectSeconds / 3) * 100;
                         redirectProgressBar.style.width = `${percent}%`;
                    }

                    if (redirectSeconds <= 0) {
                        clearInterval(redirectInterval);
                        const finalMsg = document.createElement('p');
                        finalMsg.className = 'text-center text-muted small mt-3';
                        finalMsg.textContent = '正在跳转...';
                        cardBody.appendChild(finalMsg);
                        window.location.href = '/dashboard';
                    }
                }, 1000);
            } else {
                // Fallback if card body not found
                alert(successMessage + ' 即将返回控制面板。');
                setTimeout(function() {
                    window.location.href = '/dashboard';
                }, 3000);
            }
        }

        // 处理会话过期
        function handleSessionExpired(isTimeout = true) {
             sessionExpired = true; // 标记会话已过期
             clearInterval(sessionCountdownInterval); // Ensure session countdown stops
             stopTitleBlink(); // 停止标题闪烁
             
             // 隐藏浮动提醒
             const reminder = document.getElementById('floating-reminder');
             if (reminder) {
                 reminder.style.display = 'none';
             }
             
             const cardBody = document.querySelector('.card > .card-body');
             if (cardBody) {
                 const message = isTimeout ? "登录会话已过期" : "登录会话已终止";
                 const reason = isTimeout ? "超过3分钟时限" : "会话被取消";
                 
                 cardBody.innerHTML = `
                     <div class="text-center py-5">
                         <i class="bi bi-clock-history text-warning" style="font-size: 4rem;"></i>
                         <h3 class="mt-3 text-warning">${message}</h3>
                         <p class="text-muted">${reason}，请返回控制面板重新开始。</p>
                         <p class="text-muted small">将在 <strong>3</strong> 秒后自动跳转</p>
                         <div class="progress mt-4" style="height: 5px;">
                             <div id="redirect-progress" class="progress-bar bg-warning" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                         </div>
                         <button type="button" class="btn btn-primary mt-3" onclick="window.location.href='/dashboard'">
                             <i class="bi bi-arrow-left"></i> 立即返回
                         </button>
                     </div>
                 `;
                 
                 // 自动跳转倒计时
                 let redirectSeconds = 3;
                 const countdownElement = cardBody.querySelector('strong');
                 const redirectProgressBar = document.getElementById('redirect-progress');
                 
                 const redirectInterval = setInterval(() => {
                     redirectSeconds--;
                     if(countdownElement) countdownElement.textContent = redirectSeconds;
                     if(redirectProgressBar) {
                         const percent = (redirectSeconds / 3) * 100;
                         redirectProgressBar.style.width = `${percent}%`;
                     }
                     
                     if (redirectSeconds <= 0) {
                         clearInterval(redirectInterval);
                         window.location.href = '/dashboard';
                     }
                 }, 1000);
             } else {
                 // Fallback
                 alert(isTimeout ? "登录时间已过期！" : "登录会话已终止。");
                 setTimeout(() => window.location.href = '/dashboard', 1000);
             }
        }

        // 更新倒计时显示和进度条的辅助函数
        function updateCountdownDisplay() {
            const now = new Date();
            const remainingMilliseconds = expirationTime - now;
            const remainingSeconds = Math.max(0, Math.floor(remainingMilliseconds / 1000));

            const minutes = Math.floor(remainingSeconds / 60);
            const seconds = remainingSeconds % 60;
            const timerElement = document.getElementById('countdown-timer');
             if(timerElement) timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

            const progressPercent = (remainingSeconds / totalDurationSeconds) * 100;
            updateProgressBar(progressPercent);

            // Note: Expiration is handled by the interval itself
        }

        // 更新进度条样式的辅助函数
        function updateProgressBar(percent) {
            const progressBar = document.getElementById('login-progress');
            if (!progressBar) return; // Exit if element not found

            let progressBarClass = 'bg-success';
            if (percent <= 30) {
                progressBarClass = 'bg-danger';
            } else if (percent <= 60) {
                progressBarClass = 'bg-warning';
            }
            // Ensure progress doesn't exceed 100 or go below 0
            const safePercent = Math.max(0, Math.min(100, percent));
            progressBar.style.width = safePercent + '%';
            progressBar.className = `progress-bar ${progressBarClass}`; // Make sure base class is kept
            progressBar.setAttribute('aria-valuenow', safePercent);
        }

        // 显示浮动提醒
        function showFloatingReminder() {
            const reminder = document.getElementById('floating-reminder');
            if (reminder) {
                reminder.style.display = 'flex';
                // 10秒后自动隐藏
                setTimeout(() => {
                    reminder.style.display = 'none';
                }, 10000);
            }
        }
        
        // 显示超时警告
        function showTimeoutWarning() {
            const cardBody = document.querySelector('.card > .card-body');
            if (cardBody) {
                stopTitleBlink(); // 停止标题闪烁
                
                const now = new Date();
                const remainingSeconds = Math.max(0, Math.floor((expirationTime - now) / 1000));
                
                cardBody.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 4rem;"></i>
                        <h3 class="mt-3 text-danger">时间不足</h3>
                        <p class="text-muted">剩余时间（${remainingSeconds}秒）不足以完成设备验证。</p>
                        <p class="text-muted">请返回控制面板重新开始登录流程。</p>
                        <p class="text-muted small">将在 <strong>3</strong> 秒后自动跳转</p>
                        <div class="progress mt-4" style="height: 5px;">
                            <div id="redirect-progress" class="progress-bar bg-danger" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <button type="button" class="btn btn-primary mt-3" onclick="window.location.href='/dashboard'">
                            <i class="bi bi-arrow-left"></i> 立即返回
                        </button>
                    </div>
                `;
                
                // 自动跳转倒计时
                let redirectSeconds = 3;
                const countdownElement = cardBody.querySelector('strong');
                const redirectProgressBar = document.getElementById('redirect-progress');
                
                const redirectInterval = setInterval(() => {
                    redirectSeconds--;
                    if(countdownElement) countdownElement.textContent = redirectSeconds;
                    if(redirectProgressBar) {
                        const percent = (redirectSeconds / 3) * 100;
                        redirectProgressBar.style.width = `${percent}%`;
                    }
                    
                    if (redirectSeconds <= 0) {
                        clearInterval(redirectInterval);
                        window.location.href = '/dashboard';
                    }
                }, 1000);
            } else {
                // Fallback
                alert('时间不足，请返回控制面板重新开始！');
                setTimeout(() => window.location.href = '/dashboard', 1000);
            }
        }
        


        // 复制到剪贴板
        function copyToClipboard(elementId, event) {
            const element = document.getElementById(elementId);
            if (!element) return;

            // Use modern Clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(element.value).then(() => {
                    showCopySuccess(event);
                }).catch(err => {
                    console.error('使用 Clipboard API 复制失败: ', err);
                    // Fallback to execCommand
                    copyUsingExecCommand(element, event);
                });
            } else {
                // Fallback for older browsers or insecure contexts
                copyUsingExecCommand(element, event);
            }
        }

        function copyUsingExecCommand(element, event) {
             try {
                element.select();
                // For mobile devices
                element.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showCopySuccess(event);
             } catch (err) {
                 console.error('使用 execCommand 复制失败: ', err);
                 alert('复制失败，请手动复制。');
             }
        }

        function showCopySuccess(event) {
             const btn = event ? event.currentTarget : null;
             if (!btn) return;

             const originalHTML = btn.innerHTML;
             btn.innerHTML = '<i class="bi bi-check-lg"></i>'; // Use a clearer check icon
             btn.classList.add('btn-success');
             btn.classList.remove('btn-outline-secondary');
             btn.disabled = true; // Briefly disable button

             setTimeout(function() {
                 btn.innerHTML = originalHTML;
                 btn.classList.remove('btn-success');
                 btn.classList.add('btn-outline-secondary');
                 btn.disabled = false; // Re-enable button
             }, 1500); // Slightly longer feedback time
        }

        // TOTP初始化函数
        function initTOTP() {
            // No longer relying on {{ totp_secret }} passed to the template.
            // We will call updateTOTP immediately.
            // updateTOTP will then use loginToken to fetch the code if TOTP is configured.
            console.log("[TOTP] Initializing TOTP. Attempting to fetch code via updateTOTP().");
            updateTOTP();
            // The rest of the timer logic (startTOTPTimer) will be triggered 
            // from within updateTOTP upon successful fetch of a TOTP code.
        }

        // Function to fetch and update the TOTP code
        function updateTOTP() {
            console.log("[TOTP] Attempting to fetch TOTP code...");
            const totpSectionElement = document.getElementById('totp-section');
            const totpInputElement = document.getElementById('adspower-totp');

            // Ensure loginToken is available
            if (!loginToken) {
                console.error("[TOTP] login_token is not available. Cannot fetch new TOTP code.");
                if (totpInputElement) totpInputElement.value = '错误';
                if (totpSectionElement) totpSectionElement.style.display = 'block'; // Show section to display error
                // Do not start timer if we can't even fetch.
                return;
            }

            fetch('/api/auth/generate-totp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ login_token: loginToken }) // Send login_token
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().catch(() => ({
                        success: false,
                        message: `HTTP error ${response.status}`
                    })).then(errData => {
                        const error = new Error(errData.message || `HTTP error ${response.status}`);
                        error.response = response;
                        error.data = errData;
                        throw error;
                    });
                }
                return response.json();
            })
            .then(data => {
                if (!totpInputElement || !totpSectionElement) {
                    console.warn("[TOTP] TOTP elements disappeared from DOM.");
                    return; 
                }

                if (data.success) {
                    totpSectionElement.style.display = 'block'; // Show section
                    totpInputElement.value = data.data.code;  // 修正：从data.data中获取code
                    console.log("[TOTP] New code fetched successfully. Resetting timer to " + data.data.remaining_seconds + "s.");
                    timer = data.data.remaining_seconds; // 修正：从data.data中获取remaining_seconds
                    startTOTPTimer(); // Restart countdown for the new cycle
                } else {
                    // Check for specific "TOTP not configured" message from backend
                    if (data.message && (data.message.includes('未配置TOTP') || data.message.toLowerCase().includes('not configured'))) {
                        console.warn('[TOTP] Backend indicates TOTP is not configured for this account. Hiding TOTP section.');
                        totpSectionElement.style.display = 'none'; // Hide section
                        if (timerInterval) clearInterval(timerInterval); // Stop any existing timer
                    } else {
                        console.error('[TOTP] Backend failed to generate new code:', data.message);
                        totpSectionElement.style.display = 'block'; // Show section to display error
                        totpInputElement.value = '错误';
                        timer = 5; // Short retry period
                        startTOTPTimer(); // Restart timer for retry
                    }
                }
            })
            .catch(error => {
                console.error('[TOTP] Network error or API error fetching new code:', error);
                if (totpInputElement) totpInputElement.value = '错误';
                if (totpSectionElement) totpSectionElement.style.display = 'block'; // Show section for error

                // Check if the error object has a 'data' property (from non-OK responses)
                // and if it indicates TOTP is not configured.
                if (error.data && error.data.message && (error.data.message.includes('未配置TOTP') || error.data.message.toLowerCase().includes('not configured'))) {
                    console.warn('[TOTP] API error indicates TOTP is not configured. Hiding TOTP section.');
                    if (totpSectionElement) totpSectionElement.style.display = 'none';
                    if (timerInterval) clearInterval(timerInterval);
                } else {
                    timer = 10; // Longer delay on other network/API errors
                    startTOTPTimer(); // Restart timer for retry
                }
            });
        }

        // Function to start/manage the countdown interval
        function startTOTPTimer() {
            const timerElement = document.getElementById('totp-timer');
            if (!timerElement) {
                console.warn("[TOTP] Timer element not found for startTOTPTimer.");
                return;
            }

            if (timerInterval) {
                 clearInterval(timerInterval);
                 timerInterval = null;
            }

            // Ensure timer has a valid starting value (must be set by updateTOTP first)
            if (timer === undefined || timer === null || timer <= 0) {
                console.warn(`[TOTP] startTOTPTimer called with invalid timer value: ${timer}. Attempting to fetch code via updateTOTP.`);
                // If timer is invalid, it implies updateTOTP might have failed or not run yet with success.
                // Calling updateTOTP here could lead to loops if it consistently fails.
                // It's better if updateTOTP handles its own retries or initial call.
                // For now, if timer isn't valid, we just don't start the interval.
                // The user would see "错误" or a hidden section based on updateTOTP's outcome.
                // Optionally, call updateTOTP one more time if it's meant to recover.
                // updateTOTP(); // Re-evaluate if this is needed or creates loops.
                // Let's prevent starting the interval if timer is not valid.
                const totpSectionElement = document.getElementById('totp-section');
                if(totpSectionElement && totpSectionElement.style.display !== 'none') { // Only if section is visible
                    timerElement.textContent = 'Err'; // Indicate an issue with timer value
                }
                return;
            }

            console.log(`[TOTP] Starting/Restarting countdown from ${timer} seconds.`);
            
            // 记录倒计时开始的精确时间，用于更准确的倒计时
            const startTime = Date.now();
            const initialTimer = timer;

            function tick() {
                 const currentTimerElement = document.getElementById('totp-timer');
                 const currentTotpSectionElement = document.getElementById('totp-section');

                if (!currentTimerElement || (currentTotpSectionElement && currentTotpSectionElement.style.display === 'none')) {
                    clearInterval(timerInterval);
                    console.warn("[TOTP] Timer element disappeared or section hidden during tick. Stopping interval.");
                    return;
                }
                
                // 使用实际经过的时间来计算剩余秒数，而不是简单递减
                const elapsedTime = Math.floor((Date.now() - startTime) / 1000);
                timer = Math.max(0, initialTimer - elapsedTime);

                currentTimerElement.textContent = timer;
                currentTimerElement.classList.remove('bg-danger', 'bg-secondary');
                currentTimerElement.classList.add(timer <= 5 ? 'bg-danger' : 'bg-secondary');

                if (timer <= 0) {
                    clearInterval(timerInterval);
                    updateTOTP(); // Fetch new code (which will eventually restart the timer if successful)
                    return;
                }
            }
            tick(); // Call immediately
            timerInterval = setInterval(tick, 1000);
        }

        // DOMContentLoaded event listener
        document.addEventListener('DOMContentLoaded', function() {
            // 检查必要的数据是否存在
            if (!loginToken || loginToken.trim() === '' || !expirationTimestampISO || expirationTimestampISO.trim() === '') {
                console.error('页面数据缺失：loginToken 或 expirationTimestampISO 为空');
                
                // 显示错误信息
                const cardBody = document.querySelector('.card-body');
                if (cardBody) {
                    cardBody.innerHTML = `
                        <div class="alert alert-danger text-center">
                            <i class="bi bi-exclamation-triangle-fill" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">登录会话无效</h4>
                            <p>缺少必要的登录信息，请返回控制面板重新开始。</p>
                            <p class="small text-muted">可能原因：直接访问了此页面或登录链接已过期。</p>
                            <button type="button" class="btn btn-primary mt-3" onclick="window.location.href='/dashboard'">
                                <i class="bi bi-arrow-left"></i> 返回控制面板
                            </button>
                        </div>
                    `;
                }
                return;
            }
            
            // Check if loginToken is present. If not, TOTP cannot be fetched.
            if (loginToken && loginToken.trim() !== "") {
                 // If loginToken exists, attempt to initialize TOTP.
                 // initTOTP will call updateTOTP, which then makes the API call.
                 // The visibility of the TOTP section and the timer start are handled within those functions.
                 initTOTP();
            } else {
                console.warn("[DOMContentLoaded] login_token is missing. TOTP functionality will not be initialized.");
                // Optionally hide the TOTP section explicitly if it wasn't already.
                const totpSectionElement = document.getElementById('totp-section');
                if (totpSectionElement) {
                    totpSectionElement.style.display = 'none';
                }
            }
            
            updateCountdownDisplay(); 
            startSessionCountdown(); 

            const confirmButton = document.getElementById('confirm-login-btn');
            if (confirmButton) {
                confirmButton.addEventListener('click', function() {
                    console.log('用户确认登录，开始检查状态...');
                    this.disabled = true; 
                    this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 正在验证...';
                    checkLoginStatus(); 
                });
            } else {
                 console.error("未能找到确认登录按钮 'confirm-login-btn'");
            }
            
            // 页面关闭时的清理处理
            window.addEventListener('beforeunload', function(e) {
                // 如果登录还未完成且会话未过期，发送取消请求
                if (!loginCompleted && !sessionExpired && loginToken) {
                    // 使用 navigator.sendBeacon 发送异步请求，确保页面关闭时也能发送
                    const data = JSON.stringify({ login_token: loginToken });
                    navigator.sendBeacon('/api/adspower/cancel-login', data);
                }
            });
        });
    </script>
</body>
</html> 
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结果 - 服务</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            padding-top: 40px;
            padding-bottom: 40px;
            min-height: 100vh;
        }
        .payment-result {
            width: 100%;
            max-width: 500px;
            padding: 15px;
            margin: auto;
        }
        .payment-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
        }
        .success-icon {
            color: #198754;
        }
        .waiting-icon {
            color: #fd7e14;
        }
        .error-icon {
            color: #dc3545;
        }
        .progress-indicator {
            margin: 20px 0;
        }
        .progress-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #ccc;
            display: inline-block;
            margin: 0 5px;
            transition: background-color 0.3s ease;
        }
        .progress-dot.active {
            background-color: #0d6efd;
            animation: pulse 1.5s ease-in-out infinite;
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        .retry-button {
            margin-top: 15px;
        }
        .countdown-text {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="payment-result">
        <div class="card shadow-sm">
            <div class="card-body text-center">
                <div id="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3" id="loading-text">正在确认支付结果，请稍候...</p>
                    <div class="progress-indicator">
                        <span class="progress-dot active"></span>
                        <span class="progress-dot"></span>
                        <span class="progress-dot"></span>
                    </div>
                    <p class="countdown-text" id="countdown-text"></p>
                </div>
                
                <div id="result" style="display: none;">
                    <!-- 支付结果将在这里显示 -->
                </div>
                
                <div class="mt-4">
                    <a href="/dashboard" class="btn btn-primary" id="dashboard-btn" style="display: none;">
                        <i class="bi bi-arrow-left"></i> 返回控制面板
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取订单ID (从 URL 参数 out_trade_no 获取)
        function getOrderId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('out_trade_no');
        }

        let pollInterval;
        let pollAttempts = 0;
        const maxPollAttempts = 30; // 最多轮询30次
        let currentDelay = 3000; // 初始轮询间隔3秒
        const maxDelay = 10000; // 最大轮询间隔10秒
        let countdownInterval;
        let nextPollTime = Date.now() + currentDelay;
        
        // 查询支付状态
        function checkPaymentStatus(orderId) {
            const token = localStorage.getItem('token');
            if (!token) {
                // 可以考虑跳转登录页，或者显示错误提示用户登录
                showError('您尚未登录，无法查询订单状态');
                return;
            }
            
            pollAttempts++;
            console.log(`Polling payment status for ${orderId}, attempt ${pollAttempts}`);
            
            // 更新加载文字
            updateLoadingText(pollAttempts);
            
            // 更新进度指示器
            updateProgressIndicator(pollAttempts);

            fetch(`/api/payments/status/${orderId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 根据支付状态显示不同的结果
                    // 获取嵌套在data对象中的状态
                    const paymentStatus = data.data && data.data.status ? data.data.status : 'unknown';
                    console.log(`Payment ${orderId} status from API: ${paymentStatus}`);
                    
                    if (paymentStatus === 'paid') {
                        console.log(`Payment ${orderId} confirmed as paid.`);
                        stopPolling();
                        showSuccess(data.data); // 传递嵌套的data对象
                    } else if (paymentStatus === 'failed' || paymentStatus === 'cancelled' || paymentStatus === 'error') {
                        // 只有明确失败状态才停止轮询显示错误
                        console.log(`Payment ${orderId} status indicates failure: ${paymentStatus}. Stopping poll.`);
                        stopPolling();
                        showError(`支付失败或已取消 (状态: ${paymentStatus})`);
                    } else {
                        // 所有其他状态(pending, processing, unpaid, unknown等)都视为处理中，继续轮询
                        console.log(`Payment ${orderId} status: ${paymentStatus}. Continue polling (attempt ${pollAttempts}).`);
                        // 检查是否达到最大轮询次数
                        if (pollAttempts >= maxPollAttempts) {
                            console.log(`Max poll attempts reached for ${orderId}. Showing waiting.`);
                            stopPolling();
                            showWaiting(data.data); // 传递嵌套的data对象
                        } else {
                            // 递增轮询延迟，减少服务器压力
                            if (pollAttempts > 5 && currentDelay < maxDelay) {
                                currentDelay = Math.min(currentDelay + 1000, maxDelay);
                            }
                            // 继续显示加载状态，不用更新界面
                            document.getElementById('loading').style.display = 'block';
                            document.getElementById('result').style.display = 'none';
                            document.getElementById('dashboard-btn').style.display = 'none';
                        }
                    }
                } else {
                    // API 调用成功但业务失败 (例如订单不属于该用户)
                    console.error(`API error fetching status for ${orderId}: ${data.message}`);
                    stopPolling();
                    showError(data.message || '获取支付状态失败');
                }
            })
            .catch(error => {
                console.error('获取支付状态出错:', error);
                // 网络错误时提供重试选项
                stopPolling();
                showNetworkError();
            });
        }

        function startPolling() {
            const orderId = getOrderId();
            if (!orderId) {
                showError('无法从URL获取订单信息');
                return;
            }
            // 立即执行一次
            checkPaymentStatus(orderId);
            // 设置定时轮询，使用动态延迟
            schedulePoll(orderId);
            // 开始倒计时显示
            startCountdown();
        }
        
        function schedulePoll(orderId) {
            nextPollTime = Date.now() + currentDelay;
            pollInterval = setTimeout(() => {
                checkPaymentStatus(orderId);
                if (pollAttempts < maxPollAttempts) {
                    schedulePoll(orderId);
                }
            }, currentDelay);
        }

        function stopPolling() {
            if (pollInterval) {
                clearTimeout(pollInterval);
                pollInterval = null;
                console.log("Polling stopped.");
            }
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            // 轮询停止后，显示结果区域和返回按钮
            document.getElementById('loading').style.display = 'none';
            document.getElementById('result').style.display = 'block'; 
            document.getElementById('dashboard-btn').style.display = 'inline-block';
        }
        
        // 显示支付成功
        function showSuccess(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block'; // 确保结果区域可见
            
            let startDate = '未知';
            let endDate = '未知';
            
            if (data && data.subscription) {
                startDate = formatDate(data.subscription.start_date);
                endDate = formatDate(data.subscription.end_date);
            }
            
            resultDiv.innerHTML = `
                <div class="payment-icon success-icon">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
                <h3 class="text-success">支付成功！</h3>
                <p class="mb-4">您的订阅已成功激活或延长。</p>
                
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <strong>订单信息</strong>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-5 text-start"><strong>订单号:</strong></div>
                            <div class="col-sm-7 text-end">${data.order_id}</div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>金额:</strong></div>
                            <div class="col-sm-7 text-end">¥${data.amount}</div>
                        </div>
                         <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>支付时间:</strong></div>
                            <div class="col-sm-7 text-end">${formatDate(data.paid_at)}</div>
                        </div>
                    </div>
                </div>
                
                ${data.subscription ? `
                <div class="card">
                    <div class="card-header bg-light">
                        <strong>订阅信息</strong>
                    </div>
                    <div class="card-body">
                         <div class="row">
                            <div class="col-sm-5 text-start"><strong>订阅计划:</strong></div>
                            <div class="col-sm-7 text-end">${data.subscription.subscription_type_name || data.plan_name || '未知套餐'}</div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>订阅实例:</strong></div>
                            <div class="col-sm-7 text-end">${data.subscription.subscription_instance_name || '未知实例'}</div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>开始日期:</strong></div>
                            <div class="col-sm-7 text-end">${startDate}</div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>到期日期:</strong></div>
                            <div class="col-sm-7 text-end">${endDate}</div>
                        </div>
                    </div>
                </div>
                ` : '<div class="alert alert-warning">未找到关联的订阅信息，请稍后在控制面板查看。</div>'}
            `;
        }
        
        // 显示等待支付 (轮询超时后显示)
        function showWaiting(data) {
            const resultDiv = document.getElementById('result');
             resultDiv.style.display = 'block'; // 确保结果区域可见
            
            resultDiv.innerHTML = `
                <div class="payment-icon waiting-icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
                <h3 class="text-warning">支付确认中</h3>
                <p class="mb-4">我们仍在等待支付服务商的最终确认。</p>
                
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <strong>订单信息</strong>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-5 text-start"><strong>订单号:</strong></div>
                            <div class="col-sm-7 text-end">${data.order_id}</div>
                        </div>
                         <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>金额:</strong></div>
                            <div class="col-sm-7 text-end">¥${data.amount}</div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-5 text-start"><strong>创建时间:</strong></div>
                            <div class="col-sm-7 text-end">${formatDate(data.created_at)}</div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle"></i> 
                    如果您已完成支付，请耐心等待或稍后返回控制面板查看最新状态。如果长时间未更新，请联系客服。
                </div>
                
                <div class="d-flex gap-2 justify-content-center">
                    <button class="btn btn-outline-primary" onclick="retryCheck()">
                        <i class="bi bi-arrow-clockwise"></i> 重新查询状态
                    </button>
                    ${data.payment_url ? `
                    <a href="${data.payment_url}" class="btn btn-primary" target="_blank">
                        <i class="bi bi-credit-card"></i> 继续支付
                    </a>
                    ` : ''}
                </div>
            `;
        }
        
        // 显示错误
        function showError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block'; // 确保结果区域可见
            
            resultDiv.innerHTML = `
                <div class="payment-icon error-icon">
                    <i class="bi bi-x-circle-fill"></i>
                </div>
                <h3 class="text-danger">处理失败</h3>
                <p class="mb-4">${message}</p>
                
                <div class="alert alert-danger">
                    <i class="bi bi-info-circle"></i> 
                    支付过程遇到问题。如果您确认已付款，请联系客服处理。
                </div>
                
                <button class="btn btn-outline-primary retry-button" onclick="retryCheck()">
                    <i class="bi bi-arrow-clockwise"></i> 重新查询状态
                </button>
            `;
        }
        
        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            
            try {
                const date = new Date(dateString);
                // 使用更可靠的方式格式化，避免 NaN 问题
                if (isNaN(date.getTime())) return dateString; // 如果日期无效，返回原始字符串
                return date.toLocaleString('zh-CN', { 
                    year: 'numeric', 
                    month: '2-digit', 
                    day: '2-digit', 
                    hour: '2-digit', 
                    minute:'2-digit', 
                    second:'2-digit',
                    timeZone: 'Asia/Shanghai' // 指定北京时区
                });
            } catch (e) {
                 console.error("Error formatting date:", dateString, e);
                return dateString;
            }
        }
        
        // 添加辅助函数
        function updateLoadingText(attempts) {
            const loadingText = document.getElementById('loading-text');
            if (attempts <= 5) {
                loadingText.textContent = '正在确认支付结果，请稍候...';
            } else if (attempts <= 15) {
                loadingText.textContent = '支付确认中，这可能需要一些时间...';
            } else {
                loadingText.textContent = '仍在等待支付确认，请耐心等待...';
            }
        }
        
        function updateProgressIndicator(attempts) {
            const dots = document.querySelectorAll('.progress-dot');
            const activeIndex = attempts % 3;
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === activeIndex);
            });
        }
        
        function startCountdown() {
            countdownInterval = setInterval(() => {
                const remaining = Math.max(0, nextPollTime - Date.now());
                const seconds = Math.ceil(remaining / 1000);
                document.getElementById('countdown-text').textContent = 
                    seconds > 0 ? `下次查询倒计时：${seconds} 秒` : '正在查询...';
            }, 100);
        }
        
        function showNetworkError() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            
            resultDiv.innerHTML = `
                <div class="payment-icon error-icon">
                    <i class="bi bi-wifi-off"></i>
                </div>
                <h3 class="text-danger">网络连接失败</h3>
                <p class="mb-4">查询支付状态时发生网络错误</p>
                
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle"></i> 
                    请检查您的网络连接，然后重试。如果问题持续存在，请联系客服。
                </div>
                
                <button class="btn btn-primary retry-button" onclick="retryCheck()">
                    <i class="bi bi-arrow-clockwise"></i> 重新查询
                </button>
            `;
        }
        
        function retryCheck() {
            // 重置状态
            pollAttempts = 0;
            currentDelay = 3000;
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            document.getElementById('dashboard-btn').style.display = 'none';
            
            // 重新开始轮询
            startPolling();
        }
        
        // 页面加载时开始轮询
        document.addEventListener('DOMContentLoaded', function() {
             const token = localStorage.getItem('token');
            if (!token) {
                // 如果未登录，提供更友好的引导
                 document.getElementById('loading').style.display = 'none';
                 document.getElementById('result').style.display = 'block';
                 document.getElementById('dashboard-btn').style.display = 'none';
                 
                 const resultDiv = document.getElementById('result');
                 resultDiv.innerHTML = `
                    <div class="payment-icon error-icon">
                        <i class="bi bi-person-x-fill"></i>
                    </div>
                    <h3 class="text-warning">请先登录</h3>
                    <p class="mb-4">您需要登录才能查看支付结果</p>
                    
                    <div class="d-grid gap-2">
                        <a href="/" class="btn btn-primary">
                            <i class="bi bi-box-arrow-in-right"></i> 前往登录
                        </a>
                        <a href="/dashboard" class="btn btn-outline-secondary">
                            <i class="bi bi-house"></i> 返回首页
                        </a>
                    </div>
                `;
                 return;
            }
            startPolling();
        });
    </script>
</body>
</html> 
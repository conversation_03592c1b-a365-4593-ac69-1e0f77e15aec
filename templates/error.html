<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ error_title | default('错误') }}</title>
    <link href="https://gcore.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://gcore.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
            color: #343a40;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .error-container {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            text-align: center;
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .error-icon.expired {
            color: #ffc107;
        }
        .error-icon.cancelled {
            color: #dc3545;
        }
        .error-icon.invalid {
            color: #6c757d;
        }
        h1 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #495057;
        }
        .error-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            color: #6c757d;
        }
        .btn-group-vertical .btn {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        {% if error_type == 'session_expired' %}
            <i class="bi bi-clock-history error-icon expired"></i>
            <h1>登录会话已过期</h1>
        {% elif error_type == 'session_cancelled' %}
            <i class="bi bi-x-circle error-icon cancelled"></i>
            <h1>登录已取消</h1>
        {% elif error_type == 'session_completed' %}
            <i class="bi bi-check-circle error-icon expired"></i>
            <h1>登录已完成</h1>
        {% else %}
            <i class="bi bi-exclamation-triangle error-icon invalid"></i>
            <h1>{{ error_title | default('登录错误') }}</h1>
        {% endif %}
        
        <p class="error-message">{{ message | default(error_message) | default('抱歉，出现了错误。') }}</p>
        
        <div class="btn-group-vertical" role="group">
            <a href="/dashboard" class="btn btn-primary">
                <i class="bi bi-house-door"></i> 返回控制面板
            </a>
            <a href="/" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回首页
            </a>
        </div>
    </div>
</body>
</html> 
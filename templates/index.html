<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ChatGPTPro俱乐部 - 尊贵AI拼车体验，多级拼车服务，专业技术支持，为您节省90%使用成本">
    <title>ChatGPTPro俱乐部 - 尊享AI拼车服务，多级选择，省钱90%</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50; /* 深宝蓝色，体现专业高级感 */
            --hover-color: #1c2e40;
            --accent-color: #3498db; /* 鲜亮蓝，体现科技感 */
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --bg-color: #f8f9fa;
            --secondary-bg: #ecf0f1;
            --card-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            --input-border: #dfe6e9;
            --text-color: #2c3e50;
            --text-muted: #7f8c8d;
            --highlight-color: #f39c12; /* 暖金色，增加高级质感 */
            --gold-color: #f39c12; /* 金色，传达尊贵感 */
            --premium-gradient: linear-gradient(135deg, #f39c12 0%, #fdcb6e 50%, #f39c12 100%); /* 尊贵渐变 */
            --vh: 1vh; /* 真实视口高度，用于计算 */
        }
        
        html, body {
            background-color: var(--bg-color);
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            color: var(--text-color);
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }

        .form-container {
            display: flex;
            width: 100%;
            height: 100vh; /* 回退值 */
            height: calc(var(--vh, 1vh) * 100); /* 使用css变量计算真实高度 */
            margin: 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .content-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            position: relative;
            flex-wrap: nowrap;
        }
        
        /* 适配不同屏幕尺寸，但始终保持左右等分 */
        @media (min-width: 1920px) {
            .benefits-sidebar {
                padding: 60px 50px;
            }

            .auth-form {
                max-width: 430px;
                width: 85%;
            }

            .benefits-title {
                font-size: 44px;
            }

            .benefits-subtitle {
                font-size: 20px;
            }

            .benefit-item-large i {
                font-size: 32px;
                height: 60px;
                width: 60px;
            }

            .benefit-item-large h3 {
                font-size: 24px;
            }

            .benefit-item-large p {
                font-size: 18px;
            }
        }

        @media (max-width: 1280px) {
            .benefits-content {
                max-width: 85%;
            }

            .auth-form {
                padding: min(35px, 3.5vh);
                max-width: 370px;
                width: 90%;
            }

            .benefits-title {
                font-size: 36px;
            }
        }

        @media (max-width: 992px) {
            .auth-form {
                max-width: 360px;
                width: 90%;
            }
        }

        @media (max-height: 800px) {
            .benefits-list {
                margin-bottom: 20px;
            }

            .benefit-item-large {
                margin-bottom: 20px;
            }

            .user-testimonial {
                margin: 25px auto 20px;
                padding: 20px 15px;
            }

            .auth-form {
                padding: min(30px, 3vh);
            }
        }
        
        /* 确保页面铺满且无滚动条 */
        main, .form-container, .content-wrapper {
            touch-action: none;
            -ms-touch-action: none;
        }
        
        .benefits-sidebar {
            flex: 0 0 50%;
            width: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, #1a252f 100%);
            color: white;
            padding: 4vh 3vw;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
            border-radius: 0;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        }
        
        .benefits-content {
            max-width: 75%;
            width: 100%;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        /* 添加排他性提示 */
        .exclusivity-tag {
            position: absolute;
            bottom: 25px;
            left: 25px;
            font-size: 13px;
            font-weight: 500;
            color: rgba(255,255,255,0.85);
            z-index: 5;
            display: flex;
            align-items: center;
            background-color: rgba(0,0,0,0.3);
            padding: 8px 15px;
            border-radius: 20px;
            backdrop-filter: blur(5px);
        }

        .exclusivity-tag i {
            margin-right: 6px;
            color: var(--highlight-color);
        }

        .benefits-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(135deg, rgba(44, 62, 80, 0.7) 0%, rgba(26, 37, 47, 0.85) 100%),
                url('https://images.unsplash.com/photo-1675553509367-1853c83f84a0?w=1200&q=80') center/cover;
            opacity: 1;
            z-index: 1;
            transform: scale(1.05);
            transition: all 0.5s ease;
        }

        .benefits-sidebar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.13) 0%, transparent 40%);
            z-index: 1;
        }
        
        .benefits-title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ffffff 0%, var(--gold-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
            position: relative;
            display: inline-block;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
        }

        .benefits-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 80px;
            height: 3px;
            background: var(--premium-gradient);
            border-radius: 2px;
            box-shadow: 0 2px 6px rgba(212, 175, 55, 0.3);
        }

        .benefits-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 50px;
            position: relative;
            display: inline-block;
        }

        .benefits-subtitle::after {
            content: '';
            position: absolute;
            bottom: -25px;
            left: 0;
            width: 80px;
            height: 3px;
            background-color: var(--highlight-color);
            border-radius: 2px;
        }
        
        .benefit-item-large {
            display: flex;
            align-items: flex-start;
            margin-bottom: 32px;
            transition: all 0.3s ease;
            padding: 5px 0;
        }

        .benefit-item-large:hover {
            transform: translateX(5px);
        }

        .benefit-item-large i {
            font-size: 24px;
            margin-right: 18px;
            color: rgba(0, 0, 0, 0.4); /* 加深图标颜色 */
            background-color: var(--accent-color);
            height: 50px;
            width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); /* 添加轻微文本阴影增强可见度 */
        }

        .benefit-item-large h3 {
            font-size: 20px;
            margin: 0 0 8px 0;
            font-weight: 600;
        }

        .benefit-item-large p {
            margin: 0;
            opacity: 0.9;
            font-size: 15px;
            line-height: 1.5;
        }
        
        .users-count {
            margin: 35px auto 0;
            font-size: 16px;
            opacity: 0.95;
            background: linear-gradient(to right, rgba(255, 255, 255, 0.07), rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.07));
            display: table;
            padding: 14px 32px;
            border-radius: 40px;
            text-align: center;
            position: relative;
            box-shadow:
                0 8px 20px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            transform: translateZ(0);
            transition: all 0.35s ease;
            overflow: hidden;
        }

        .users-count::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 200%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.18), transparent);
            transform: skewX(-15deg);
            animation: shimmerEffect 3s infinite;
        }

        @keyframes shimmerEffect {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .users-count:hover {
            transform: scale(1.05) translateZ(0);
            box-shadow:
                0 12px 25px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        .users-count strong {
            color: var(--gold-color);
            font-size: 20px;
            font-weight: 700;
            display: inline-block;
            position: relative;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .users-count strong::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--premium-gradient);
            opacity: 0.5;
            border-radius: 4px;
        }
        
        .form-signin {
            flex: 0 0 50%;
            width: 50%;
            padding: 0 20px;
            margin: auto 0;
            background-color: transparent;
            transition: opacity 0.3s ease;
            opacity: 0;
            animation: fadeIn 0.3s forwards;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
        
        .form-signin .form-floating {
            margin-bottom: 15px;
        }
        
        .form-signin .form-control {
            height: auto;
            padding: 14px 16px;
            font-size: 15px;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            transition: all 0.25s ease;
            background-color: #fafafa;
        }

        .form-signin .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
            background-color: #fff;
            transform: translateY(-1px);
        }
        
        .form-label {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            color: var(--accent-color);
        }
        
        .verification-group {
            display: flex;
            gap: 12px;
            margin-bottom: 25px;
        }
        
        .verification-group .flex-grow-1 {
            flex-grow: 1;
        }
        
        .verification-group .verification-input {
            margin-bottom: 0;
        }
        
        .verification-group button {
            width: 120px;
            padding: 10px 0;
            font-size: 14px;
            font-weight: 400;
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            background-color: #fff;
            color: var(--accent-color);
            transition: all 0.2s ease;
            align-self: flex-end;
            margin-bottom: 0;
        }

        .verification-group button:hover:not(:disabled) {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--accent-color);
        }
        
        /* 社区链接样式 */
        .social-proof {
            padding: 10px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .social-link {
            display: inline-flex;
            align-items: center;
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 10px 15px;
            border-radius: 6px;
            border: 1px solid var(--accent-color);
            font-size: 15px;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.1);
        }

        .social-link:hover {
            background-color: var(--accent-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
        }
        
        .social-link i {
            margin-right: 8px;
            font-size: 18px;
        }
        
        .user-count {
            color: var(--accent-color);
            font-weight: 600;
        }
        
        /* 用户评价样式 */
        .user-testimonial {
            position: relative;
            padding: 28px 25px;
            background-color: rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            margin: 45px auto 35px;
            border-left: 4px solid var(--gold-color);
            box-shadow:
                0 10px 25px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            max-width: 85%;
            overflow: hidden;
        }

        .user-testimonial::before {
            content: '';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(212, 175, 55, 0.15) 0%, transparent 70%);
            border-radius: 50%;
            z-index: 0;
        }

        .user-testimonial:hover {
            transform: translateY(-8px) translateX(3px);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        }

        /* 添加尊贵感徽章 */
        .premium-badge {
            position: absolute;
            top: 25px;
            right: 25px;
            background: var(--premium-gradient);
            color: #222;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 1px;
            border-radius: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 5;
            text-transform: uppercase;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }


        
        .quote-mark {
            position: absolute;
            top: 18px;
            left: 18px;
            font-size: 52px;
            background: var(--premium-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            opacity: 0.3;
            font-family: Georgia, serif;
            line-height: 1;
            z-index: 1;
        }

        .quote-text {
            font-size: 16px;
            margin-bottom: 14px;
            padding-left: 15px;
            line-height: 1.7;
            font-style: italic;
            position: relative;
            z-index: 2;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.2px;
        }

        .quote-author {
            font-size: 14px;
            text-align: right;
            margin-bottom: 0;
            font-weight: 600;
            color: var(--highlight-color);
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .quote-author::before {
            content: '—';
            margin-right: 5px;
            opacity: 0.7;
        }
        
        .verification-group button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        /* 移动设备上的验证码组样式 */
        @media (max-width: 400px) {
            .verification-group {
                flex-direction: column;
                gap: 5px;
            }
            .verification-group button {
                width: 100%;
                margin-top: 5px;
            }
            .form-title {
                font-size: 22px;
            }
            .form-subtitle {
                font-size: 14px;
            }
            .auth-form {
                padding: min(30px, 3vh) min(30px, 3vh) min(40px, 4vh);
            }
            .social-link {
                padding: 8px 12px;
                font-size: 14px;
            }
        }
        
        /* 验证容器动画效果 */
        #login-verification-container {
            opacity: 0;
            height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 0;
        }
        
        #login-verification-container.visible {
            opacity: 1;
            height: auto;
            margin-bottom: 15px;
        }
        
                
        .btn-link {
            color: var(--accent-color);
            text-decoration: none;
            font-size: 14px;
            font-weight: 400;
            padding: 0;
            transition: color 0.2s ease;
        }

        .btn-link:hover {
            color: #2980b9;
            text-decoration: none;
        }
        
        .auth-form {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.07);
            padding: min(40px, 4vh) min(40px, 4vh) min(50px, 5vh);
            border: 1px solid rgba(212, 175, 55, 0.15);
            max-height: 90vh;
            margin: auto;
            max-width: 400px;
            width: 90%;
        }

        .auth-form.active {
            display: block;
            opacity: 1;
        }
        
        .alert {
            font-size: 13px;
            border-radius: 6px;
            padding: 10px 15px;
            margin-bottom: 15px;
            border: none;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .alert.visible {
            opacity: 1;
        }
        
        .alert i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .alert-danger {
            background-color: #fff4f4;
            color: #d90000;
        }
        
        .alert-success {
            background-color: #f0fff7;
            color: #00a651;
        }
        
        .password-field {
            position: relative;
        }
        
        .password-toggle-icon {
            position: absolute;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            cursor: pointer;
            color: #aaa;
            transition: color 0.2s ease;
            padding: 4px;
            z-index: 2;
        }
        
        .password-toggle-icon:hover {
            color: var(--primary-color);
        }
        
        .btn {
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 16px;
            padding: 12px 24px;
            border-radius: 8px;
            margin-top: 5px;
        }

        .btn-primary {
            background-color: var(--accent-color);
            background-image: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
            border-color: var(--accent-color);
            position: relative;
            overflow: hidden;
            z-index: 1;
            box-shadow:
                0 6px 16px rgba(52, 152, 219, 0.35),
                0 2px 4px rgba(52, 152, 219, 0.2),
                0 0 0 1px rgba(52, 152, 219, 0.1) inset;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            padding: 14px 28px;
            font-size: 17px;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: translateX(-100%);
            z-index: -1;
        }

        .btn-primary::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(90deg,
                var(--gold-color),
                var(--accent-color),
                var(--gold-color),
                var(--accent-color),
                var(--gold-color));
            z-index: -2;
            filter: blur(8px);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .btn-primary:hover:not(:disabled) {
            background-color: #2980b9;
            border-color: #2980b9;
            box-shadow:
                0 8px 25px rgba(52, 152, 219, 0.5),
                0 3px 6px rgba(52, 152, 219, 0.3),
                0 0 0 1px rgba(52, 152, 219, 0.2) inset;
            transform: translateY(-3px);
        }

        .btn-primary:hover:not(:disabled)::before {
            animation: shimmer 1.5s infinite;
        }

        .btn-primary:hover:not(:disabled)::after {
            opacity: 0.3;
        }

        .btn-primary:active:not(:disabled) {
            transform: translateY(0);
            box-shadow:
                0 4px 12px rgba(52, 152, 219, 0.3),
                0 0 0 1px rgba(52, 152, 219, 0.2) inset;
            transition: all 0.1s ease;
        }

        @keyframes shimmer {
            100% {
                transform: translateX(100%);
            }
        }
        
        .btn:active:not(:disabled) {
            transform: translateY(1px);
        }
        
        .btn.loading .spinner-border {
            width: 1rem;
            height: 1rem;
            margin-right: 0.5rem;
            vertical-align: -0.15em;
        }
        
        .form-check-input {
            cursor: pointer;
            border-color: #ccc;
            width: 16px;
            height: 16px;
        }
        
        .form-check-input:checked {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        
        .form-check-label {
            cursor: pointer;
            font-size: 14px;
            user-select: none;
            padding-left: 4px;
        }
        
        .form-text {
            color: var(--text-muted);
            font-size: 13px;
            margin-top: 5px;
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            font-size: 12px;
            color: var(--danger-color);
            margin-top: 4px;
        }
        
        .form-control.is-invalid {
            border-color: var(--danger-color);
            background-image: none;
            padding-right: 12px;
        }
        
        .form-control.is-valid {
            border-color: var(--success-color);
            background-image: none;
            padding-right: 12px;
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 32px;
            position: relative;
        }

        .form-header::after {
            content: '';
            position: absolute;
            bottom: -16px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        .form-title {
            font-size: 26px;
            font-weight: 600;
            margin-bottom: 0;
            color: #333;
        }

        .form-subtitle {
            font-size: 16px;
            color: var(--text-muted);
            margin-top: 8px;
            margin-bottom: 0;
        }
        
        /* 移动端营销亮点样式 */
        .benefits-panel {
            background-color: rgba(52, 152, 219, 0.05);
            border-radius: 12px;
            padding: 16px;
            border-left: 3px solid var(--accent-color);
            margin-bottom: 28px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }

        .benefits-panel:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
            transform: translateY(-2px);
        }
        
        /* 水平排列的优势面板 */
        .d-md-flex.benefits-panel {
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            gap: 5px;
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            color: #444;
        }
        
        .benefit-item:last-child {
            margin-bottom: 0;
        }
        
        .benefit-item i {
            color: var(--accent-color);
            margin-right: 8px;
            font-size: 14px;
        }
        
        /* 水平排列的benefititem */
        .d-md-flex .benefit-item {
            margin-bottom: 0;
            flex: 1;
            justify-content: center;
            text-align: center;
            padding: 6px 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .d-md-flex .benefit-item:hover {
            background-color: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }
        
        .auth-footer {
            text-align: center;
            margin-top: 25px;
            font-size: 14px;
            padding-bottom: 5px;
        }
        
        .auth-footer .text-muted {
            color: var(--text-muted);
        }
        
        .auth-footer a {
            color: var(--accent-color);
            text-decoration: none;
        }
        
        .telegram-links {
            margin-top: 20px;
            margin-bottom: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .divider {
            margin: 20px 0;
            text-align: center;
            position: relative;
        }
        
        .divider::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #eee;
            z-index: 1;
        }
        
        .divider span {
            padding: 0 10px;
            background-color: #fff;
            position: relative;
            z-index: 2;
            color: var(--text-muted);
            font-size: 13px;
        }
    </style>
</head>
<body>
    <main class="form-container">
        <div class="content-wrapper">
            <div class="benefits-sidebar d-none d-md-flex">
                <div class="benefits-content">
                    <h2 class="benefits-title">ChatGPTPro俱乐部</h2>
                    <p class="benefits-subtitle">尊贵AI助手拼车体验</p>

                    <div class="benefits-list">
                        <div class="benefit-item-large">
                            <i class="bi bi-people-fill"></i>
                            <div>
                                <h3>精准拼车</h3>
                                <p>1人独享车、5人车、10人车，多级选择</p>
                            </div>
                        </div>
                        <div class="benefit-item-large">
                            <i class="bi bi-piggy-bank-fill"></i>
                            <div>
                                <h3>尊享省钱</h3>
                                <p>拼车模式，为您节省90%使用成本</p>
                            </div>
                        </div>
                        <div class="benefit-item-large">
                            <i class="bi bi-headset"></i>
                            <div>
                                <h3>专业服务</h3>
                                <p>多位成员提供7×24小时技术支持</p>
                            </div>
                        </div>
                        <div class="benefit-item-large">
                            <i class="bi bi-shield-check"></i>
                            <div>
                                <h3>尊贵体验</h3>
                                <p>快速响应，问题处理，稳定可靠</p>
                            </div>
                        </div>
                    </div>

                    <div class="user-testimonial">
                        <div class="quote-mark">"</div>
                        <p class="quote-text">加入5人共享后，GPT-4o生成的参考图帮我突破了设计瓶颈，素材准备时间从2小时缩短到20分钟。图像质量好到让客户惊讶，整个设计流程更加流畅高效。</p>
                        <p class="quote-author">陈总，创意公司合作伙伴</p>
                    </div>


                    <div class="users-count">
                        <span>已有 <strong>200+</strong> 尊贵会员加入俱乐部</span>
                    </div>
                </div>
            </div>

            <div class="form-signin">
            <!-- 登录表单 -->
            <div id="login-form" class="auth-form active">
                <div class="form-header">
                    <h1 class="form-title">尊享会员登录</h1>
                    <p class="form-subtitle">欢迎回到ChatGPTPro俱乐部</p>
                </div>

                <!-- 仅在移动设备上显示简化的优势提示 -->
                <div class="benefits-panel mb-4 d-md-none">
                    <div class="benefit-item">
                        <i class="bi bi-person-fill"></i>
                        <span>独享/精品车</span>
                    </div>
                    <div class="benefit-item">
                        <i class="bi bi-stars"></i>
                        <span>尊贵享受</span>
                    </div>
                    <div class="benefit-item">
                        <i class="bi bi-shield-fill-check"></i>
                        <span>专属服务</span>
                    </div>
                </div>

                <div class="alert alert-danger" id="login-error" style="display: none;"></div>
                <div class="alert alert-success" id="login-success" style="display: none;"></div>

                <div class="mb-4">
                    <label for="login-email" class="form-label">邮箱</label>
                    <input type="email" class="form-control" id="login-email" placeholder="请输入邮箱">
                </div>

                <div class="mb-4 password-field">
                    <label for="login-password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="login-password" placeholder="请输入密码">
                    <i class="bi bi-eye-slash password-toggle-icon" id="toggle-login-password"></i>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            记住我
                        </label>
                    </div>
                    <a class="btn-link" id="toggle-reset">忘记密码？</a>
                </div>

                <div id="login-verification-container">
                    <div class="verification-group">
                        <div class="verification-input flex-grow-1">
                            <label for="login-verification-code" class="form-label">验证码</label>
                            <input type="text" class="form-control" id="login-verification-code" placeholder="请输入验证码">
                        </div>
                        <button class="btn btn-outline-primary" id="login-send-code">
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                            <span class="button-text">获取验证码</span>
                        </button>
                    </div>
                </div>

                <div class="mt-4">
                    <button class="w-100 btn btn-primary mb-3" id="login-btn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        <span class="button-text">登录并享受尊贵服务</span>
                    </button>
                    <a href="/api/auth/keycloak/login" class="w-100 btn btn-secondary mb-3">
                        <i class="bi bi-shield-lock"></i> 使用Keycloak登录
                    </a>
                </div>
                
                <div class="auth-footer">
                    <span>还没有账号？</span>
                    <a href="javascript:void(0)" id="toggle-register">立即注册</a>
                </div>
                
                <div class="auth-footer mt-2">
                    <p class="text-muted small mb-2">登录即表示您同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></p>
                    
                    <div class="telegram-links">
                        <a href="https://t.me/gptproclub" target="_blank" class="social-link">
                            <i class="bi bi-telegram"></i>
                            <span><strong>官方频道</strong></span>
                        </a>
                        <a href="https://t.me/chatgptpro_notification" target="_blank" class="social-link">
                            <i class="bi bi-bell-fill"></i>
                            <span><strong>通知频道</strong></span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-form" class="auth-form">
                <div class="form-header">
                    <h1 class="form-title">尊贵会员注册</h1>
                    <p class="form-subtitle">选择您的专属拼车服务</p>
                </div>


                <div class="alert alert-danger" id="register-error" style="display: none;"></div>
                <div class="alert alert-success" id="register-success" style="display: none;"></div>

                <div class="mb-4">
                    <label for="register-email" class="form-label">邮箱</label>
                    <input type="email" class="form-control" id="register-email" placeholder="请输入邮箱">
                </div>

                <div class="mb-4 password-field">
                    <label for="register-password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="register-password" placeholder="请输入密码">
                    <i class="bi bi-eye-slash password-toggle-icon" id="toggle-register-password"></i>
                    <div class="form-text">密码长度至少8位，包含字母和数字</div>
                </div>

                <div class="verification-group">
                    <div class="verification-input flex-grow-1">
                        <label for="register-verification-code" class="form-label">验证码</label>
                        <input type="text" class="form-control" id="register-verification-code" placeholder="请输入验证码">
                    </div>
                    <button class="btn btn-outline-primary" id="register-send-code">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        <span class="button-text">获取验证码</span>
                    </button>
                </div>

                
                <div class="mt-4">
                    <button class="w-100 btn btn-primary mb-3" id="register-btn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        <span class="button-text">立即注册</span>
                    </button>
                </div>
                <p class="text-center small text-success mb-3">
                    <i class="bi bi-gift"></i>
                    <strong>新用户专享：</strong>注册即可获得1天免费体验
                </p>
                
                <div class="auth-footer">
                    <span>已有账号？</span>
                    <a href="javascript:void(0)" id="toggle-login-from-register">立即登录</a>
                </div>
                
                <div class="auth-footer mt-2">
                    <p class="text-muted small mb-2">注册即表示您同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></p>
                    
                    <div class="telegram-links">
                        <a href="https://t.me/gptproclub" target="_blank" class="social-link">
                            <i class="bi bi-telegram"></i>
                            <span><strong>官方频道</strong></span>
                        </a>
                        <a href="https://t.me/chatgptpro_notification" target="_blank" class="social-link">
                            <i class="bi bi-bell-fill"></i>
                            <span><strong>通知频道</strong></span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 重置密码表单 -->
            <div id="reset-form" class="auth-form">
                <div class="form-header">
                    <h1 class="form-title">重置密码</h1>
                    <p class="form-subtitle">找回您的ChatGPTPro俱乐部密码</p>
                </div>
                
                <div class="alert alert-danger" id="reset-error" style="display: none;"></div>
                <div class="alert alert-success" id="reset-success" style="display: none;"></div>
                
                <div class="mb-4">
                    <label for="reset-email" class="form-label">邮箱</label>
                    <input type="email" class="form-control" id="reset-email" placeholder="请输入邮箱">
                </div>
                
                <div class="verification-group">
                    <div class="verification-input flex-grow-1">
                        <label for="reset-verification-code" class="form-label">验证码</label>
                        <input type="text" class="form-control" id="reset-verification-code" placeholder="请输入验证码">
                    </div>
                    <button class="btn btn-outline-primary" id="reset-send-code">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        <span class="button-text">获取验证码</span>
                    </button>
                </div>

                <div class="mb-4 password-field">
                    <label for="reset-new-password" class="form-label">新密码</label>
                    <input type="password" class="form-control" id="reset-new-password" placeholder="请输入新密码">
                    <i class="bi bi-eye-slash password-toggle-icon" id="toggle-reset-password"></i>
                    <div class="form-text">密码长度至少8位，包含字母和数字</div>
                </div>

                <div class="mt-4">
                    <button class="w-100 btn btn-primary mb-3" id="reset-btn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        <span class="button-text">重置密码</span>
                    </button>
                </div>
                
                <div class="auth-footer">
                    <a href="javascript:void(0)" id="toggle-login-from-reset">返回登录</a>
                </div>
                
                <div class="auth-footer mt-2">
                    <div class="telegram-links">
                        <a href="https://t.me/gptproclub" target="_blank" class="social-link">
                            <i class="bi bi-telegram"></i>
                            <span><strong>官方频道</strong></span>
                        </a>
                        <a href="https://t.me/chatgptpro_notification" target="_blank" class="social-link">
                            <i class="bi bi-bell-fill"></i>
                            <span><strong>通知频道</strong></span>
                        </a>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 确保页面铺满整个窗口
        function resizeApp() {
            document.documentElement.style.setProperty('--vh', window.innerHeight * 0.01 + 'px');
        }

        window.addEventListener('resize', resizeApp);
        window.addEventListener('orientationchange', resizeApp);

        // 页面加载初始化
        document.addEventListener('DOMContentLoaded', function() {
            resizeApp();
            
            // 检查是否已登录
            const token = localStorage.getItem('token');
            let user;
            const storedUserString = localStorage.getItem('user');
            if (storedUserString === null || storedUserString === undefined || storedUserString === 'undefined') {
                // Handles null, undefined, or the literal string "undefined"
                try {
                    user = JSON.parse('{}'); // Safely parse an empty object string
                } catch (e) {
                    console.error('Error parsing default empty object string for user:', e);
                    user = {}; // Ultimate fallback
                }
                if (storedUserString === 'undefined') {
                    console.warn('localStorage contained "undefined" for user key. It has been reset to {}.');
                    localStorage.removeItem('user'); // Clean up "undefined" string
                }
            } else {
                try {
                    user = JSON.parse(storedUserString);
                } catch (e) {
                    console.error('Failed to parse user from localStorage, value:', storedUserString, e);
                    try {
                        user = JSON.parse('{}'); // Fallback to empty object string on error
                    } catch (e2) {
                        console.error('Error parsing default empty object string for user after initial error:', e2);
                        user = {}; // Ultimate fallback
                    }
                    localStorage.removeItem('user'); // Remove potentially corrupted item
                }
            }
            
            if (token && user && user.id) {
                // 已登录，跳转到相应页面
                if (user.is_admin) {
                    window.location.href = '/admin';
                } else {
                    window.location.href = '/dashboard';
                }
            } else {
                // 自动填充上次登录邮箱
                const savedEmail = localStorage.getItem('lastLoginEmail');
                if (savedEmail) {
                    document.getElementById('login-email').value = savedEmail;
                }
                
                // 确保登录表单可见
                showForm('login-form');
                
                // 初始化事件监听
                setupEventListeners();
            }
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 表单输入验证
            document.addEventListener('input', function(e) {
                if (e.target.classList.contains('form-control')) {
                    validateInput(e.target);
                }
            });

            document.addEventListener('blur', function(e) {
                if (e.target.classList.contains('form-control') && e.target.value.trim() !== '') {
                    validateInput(e.target, true);
                }
            });

            // 表单切换事件
            document.getElementById('toggle-register').addEventListener('click', () => showForm('register-form'));
            document.getElementById('toggle-login-from-register').addEventListener('click', () => showForm('login-form'));
            document.getElementById('toggle-reset').addEventListener('click', () => showForm('reset-form'));
            document.getElementById('toggle-login-from-reset').addEventListener('click', () => showForm('login-form'));

            // 密码可见性切换
            document.getElementById('toggle-login-password').addEventListener('click', () => togglePasswordVisibility('login-password', 'toggle-login-password'));
            document.getElementById('toggle-register-password').addEventListener('click', () => togglePasswordVisibility('register-password', 'toggle-register-password'));
            document.getElementById('toggle-reset-password').addEventListener('click', () => togglePasswordVisibility('reset-new-password', 'toggle-reset-password'));

            // 发送验证码事件
            document.getElementById('register-send-code').addEventListener('click', function() {
                sendVerificationCode('register-email', 'register', this);
            });

            document.getElementById('login-send-code').addEventListener('click', function() {
                sendVerificationCode('login-email', 'login', this);
            });

            document.getElementById('reset-send-code').addEventListener('click', function() {
                sendVerificationCode('reset-email', 'reset', this);
            });

            // 表单提交事件
            document.getElementById('login-btn').addEventListener('click', handleLogin);
            document.getElementById('register-btn').addEventListener('click', handleRegister);
            document.getElementById('reset-btn').addEventListener('click', handleResetPassword);

        }
        
        // 表单切换
        function showForm(formId) {
            // 隐藏所有表单
            const forms = document.querySelectorAll('.auth-form');
            forms.forEach(form => {
                form.classList.remove('active');
                form.style.opacity = '0';
            });
            
            // 显示目标表单
            const activeForm = document.getElementById(formId);
            if (activeForm) {
                activeForm.classList.add('active');
                
                // 给浏览器一点时间来完成DOM更新
                setTimeout(() => {
                    activeForm.style.opacity = '1';
                    
                    // 清除表单消息
                    clearMessages(formId);
                    
                    // 重置表单验证状态
                    const inputs = activeForm.querySelectorAll('.form-control');
                    inputs.forEach(input => {
                        input.classList.remove('is-invalid', 'is-valid');
                        const feedback = input.nextElementSibling;
                        if (feedback && feedback.classList.contains('invalid-feedback')) {
                            feedback.remove();
                        }
                    });
                    
                    // 聚焦第一个输入框
                    const firstInput = activeForm.querySelector('input[type="email"]');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 50);
            }
        }
        
        // 密码可见性切换
        function togglePasswordVisibility(inputId, iconId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(iconId);
            
            if (passwordInput && toggleIcon) {
                if (passwordInput.type === "password") {
                    passwordInput.type = "text";
                    toggleIcon.classList.remove("bi-eye-slash");
                    toggleIcon.classList.add("bi-eye");
                } else {
                    passwordInput.type = "password";
                    toggleIcon.classList.remove("bi-eye");
                    toggleIcon.classList.add("bi-eye-slash");
                }
            }
        }
        
        // 登录处理
        function handleLogin() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const code = document.getElementById('login-verification-code').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            const button = document.getElementById('login-btn');
            const formId = 'login-form';
            
            // 防止重复提交
            if (button.disabled) {
                return;
            }
            
            // 验证邮箱和密码
            if (!validateInput(document.getElementById('login-email'), true) || 
                !validateInput(document.getElementById('login-password'), true)) {
                return; // 验证失败
            }
            
            const loginData = { 
                email: email, 
                password: password,
                remember_me: rememberMe
            };
            
            const verificationContainer = document.getElementById('login-verification-container');
            if (verificationContainer.classList.contains('visible')) {
                if (!validateInput(document.getElementById('login-verification-code'), true)) {
                    return; // 验证码验证失败
                }
                loginData.verification_code = code;
            }
            
            clearMessages(formId);
            showLoading(button);
            
            fetch('/api/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(loginData)
            })
            .then(handleResponse)
            .then(data => {
                hideLoading(button);
                
                if (data.success) {
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    localStorage.setItem('lastLoginEmail', email);
                    localStorage.setItem('auth_type', 'local'); // 标记为本地登录
                    
                    // 处理记住我选项
                    if (rememberMe) {
                        // 保存到cookie，7天过期
                        const date = new Date();
                        date.setTime(date.getTime() + (7 * 24 * 60 * 60 * 1000));
                        const expires = "expires=" + date.toUTCString();
                        document.cookie = "remember_token=" + data.data.token + ";" + expires + ";path=/;SameSite=Strict";
                    } else {
                        // 清除相关cookie
                        document.cookie = "remember_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;SameSite=Strict";
                    }
                    
                    // 显示成功消息
                    showSuccess(formId, '登录成功，正在跳转...');
                    
                    // 根据角色跳转
                    setTimeout(() => {
                        if (data.data.user && data.data.user.is_admin) {
                            window.location.href = '/admin';
                        } else {
                            window.location.href = '/dashboard';
                        }
                    }, 800);
                } else {
                    // 处理特殊情况：需要邮箱验证
                    if (data.message && data.message.includes('验证您的邮箱')) {
                        // 显示验证码输入
                        verificationContainer.classList.add('visible');
                        
                        showError(formId, data.message + " 请输入收到的验证码。");
                        
                        // 自动聚焦验证码输入框
                        document.getElementById('login-verification-code').focus();
                    } else {
                        // 其他错误
                        showError(formId, data.message || '登录失败');
                        
                        // 错误时还原密码输入框，方便重新输入
                        document.getElementById('login-password').value = '';
                        document.getElementById('login-password').focus();
                    }
                }
            })
            .catch(error => {
                hideLoading(button);
                console.error('Login Error:', error);
                showError(formId, '登录请求失败，请检查您的网络连接');
            });
        }
        
        // 注册处理
        function handleRegister() {
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const code = document.getElementById('register-verification-code').value;
            const button = document.getElementById('register-btn');
            const formId = 'register-form';

            // 防止重复提交
            if (button.disabled) {
                return;
            }

            // 表单验证
            if (!validateForm(formId)) {
                return; // 验证不通过
            }

            
            clearMessages(formId);
            showLoading(button);

            fetch('/api/auth/register', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  email: email,
                  password: password,
                  verification_code: code
                })
            })
            .then(handleResponse)
            .then(data => {
                hideLoading(button);
                
                if (data.success) {
                    // 处理成功注册
                    showSuccess(formId, '注册成功！即将进入登录页面...');

                    // 清除表单
                    document.getElementById('register-email').value = '';
                    document.getElementById('register-password').value = '';
                    document.getElementById('register-verification-code').value = '';

                    setTimeout(() => {
                        showForm('login-form');
                    }, 2000);
                } else {
                    // 处理注册失败
                    showError(formId, data.message || '注册失败，请稍后再试');
                    // 验证码错误时自动聚焦
                    if (data.message && data.message.includes('验证码')) {
                        document.getElementById('register-verification-code').focus();
                    }
                }
            })
            .catch(error => {
                hideLoading(button);
                console.error('Register Error:', error);
                showError(formId, '网络请求失败，请检查您的网络连接');
            });
        }
        
        // 重置密码处理
        function handleResetPassword() {
            const email = document.getElementById('reset-email').value;
            const code = document.getElementById('reset-verification-code').value;
            const newPassword = document.getElementById('reset-new-password').value;
            const button = document.getElementById('reset-btn');
            const formId = 'reset-form';
            
            // 防止重复提交
            if (button.disabled) {
                return;
            }
            
            // 表单验证
            if (!validateForm(formId)) {
                return; // 验证不通过
            }
            
            clearMessages(formId);
            showLoading(button);
            
            fetch('/api/auth/reset-password', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, code: code, new_password: newPassword })
            })
            .then(handleResponse)
            .then(data => {
                hideLoading(button);
                
                if (data.success) {
                    // 显示成功消息
                    showSuccess(formId, '密码重置成功！即将跳转到登录页面...');
                    
                    // 清除字段
                    document.getElementById('reset-email').value = '';
                    document.getElementById('reset-verification-code').value = '';
                    document.getElementById('reset-new-password').value = '';
                    
                    // 延迟后跳转
                    setTimeout(() => {
                        showForm('login-form');
                    }, 1500);
                } else {
                    // 显示错误消息
                    showError(formId, data.message || '重置密码失败');
                    // 聚焦验证码输入框以便重新输入
                    document.getElementById('reset-verification-code').value = '';
                    document.getElementById('reset-verification-code').focus();
                }
            })
            .catch(error => {
                hideLoading(button);
                console.error('Reset Password Error:', error);
                showError(formId, '重置密码请求失败，请检查您的网络连接');
            });
        }
        
        // 发送验证码
        function sendVerificationCode(emailInputId, codeType, button) {
            const email = document.getElementById(emailInputId).value;
            const formId = button.closest('.auth-form').id;
            
            // 防止重复提交
            if (button.disabled) {
                return;
            }
            
            // 验证邮箱
            if (!validateInput(document.getElementById(emailInputId), true)) {
                return;
            }
            
            clearMessages(formId);
            showLoading(button);
            
            fetch('/api/auth/send-verification', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, type: codeType })
            })
            .then(handleResponse)
            .then(data => {
                hideLoading(button);
                
                if (data.success) {
                    showSuccess(formId, '验证码已发送，请查收邮件');
                    startButtonCountdown(button);
                } else {
                    showError(formId, data.message || '发送验证码失败');
                }
            })
            .catch(error => {
                hideLoading(button);
                console.error('Send Code Error:', error);
                showError(formId, '发送验证码请求失败，请稍后再试');
            });
        }
        
        // 响应处理
        function handleResponse(response) {
            return new Promise((resolve, reject) => {
                try {
                    // 尝试解析响应
                    if (response.headers.get('content-type')?.includes('application/json')) {
                        return response.json().then(data => {
                            resolve(data);
                        });
                    } else {
                        return response.text().then(text => {
                            resolve({
                                success: response.ok,
                                message: text || response.statusText
                            });
                        });
                    }
                } catch (error) {
                    console.error('Response parsing error:', error);
                    reject(error);
                }
            });
        }
        
        // 按钮加载状态
        function showLoading(button) {
            button.disabled = true;
            const spinner = button.querySelector('.spinner-border');
            if (spinner) {
                spinner.classList.remove('d-none');
            }
        }
        
        // 隐藏加载状态
        function hideLoading(button) {
            button.disabled = false;
            const spinner = button.querySelector('.spinner-border');
            if (spinner) {
                spinner.classList.add('d-none');
            }
        }
        
        // 显示成功消息
        function showSuccess(formId, message) {
            const successElement = document.getElementById(`${formId.split('-')[0]}-success`);
            const errorElement = document.getElementById(`${formId.split('-')[0]}-error`);
            
            // 隐藏错误信息
            if (errorElement) {
                errorElement.style.display = 'none';
            }
            
            if (successElement) {
                successElement.innerHTML = `<i class="bi bi-check-circle"></i>${message}`;
                successElement.style.display = 'block';
                
                // 添加动画效果
                setTimeout(() => {
                    successElement.classList.add('visible');
                }, 10);
            }
        }
        
        // 显示错误消息
        function showError(formId, message) {
            const errorElement = document.getElementById(`${formId.split('-')[0]}-error`);
            const successElement = document.getElementById(`${formId.split('-')[0]}-success`);
            
            // 隐藏成功信息
            if (successElement) {
                successElement.style.display = 'none';
            }
            
            if (errorElement) {
                errorElement.innerHTML = `<i class="bi bi-exclamation-triangle"></i>${message}`;
                errorElement.style.display = 'block';
                
                // 添加动画效果
                setTimeout(() => {
                    errorElement.classList.add('visible');
                }, 10);
            }
        }
        
        // 清除消息
        function clearMessages(formId) {
            const baseId = formId.split('-')[0];
            const successElement = document.getElementById(`${baseId}-success`);
            const errorElement = document.getElementById(`${baseId}-error`);
            
            if (successElement) {
                successElement.style.display = 'none';
                successElement.classList.remove('visible');
            }
            
            if (errorElement) {
                errorElement.style.display = 'none';
                errorElement.classList.remove('visible');
            }
        }
        
        // 验证码发送倒计时
        function startButtonCountdown(button, seconds = 60) {
            let remaining = seconds;
            const originalText = button.querySelector('.button-text').textContent;
            button.disabled = true;
            
            const interval = setInterval(() => {
                button.querySelector('.button-text').textContent = `${remaining}秒后重发`;
                remaining--;
                
                if (remaining < 0) {
                    clearInterval(interval);
                    button.querySelector('.button-text').textContent = originalText;
                    button.disabled = false;
                }
            }, 1000);
        }
        
        // 表单整体验证
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            const inputs = form.querySelectorAll('.form-control');
            let isValid = true;
            let firstInvalidInput = null;
            
            inputs.forEach(input => {
                // 验证每个输入字段
                if (!validateInput(input, true) && isValid) {
                    isValid = false;
                    firstInvalidInput = input;
                }
            });
            
            // 聚焦第一个无效的输入框
            if (firstInvalidInput) {
                firstInvalidInput.focus();
            }
            
            return isValid;
        }
        
        // 输入验证函数
        function validateInput(input, showMessage = false) {
            const inputId = input.id;
            let isValid = true;
            let errorMessage = '';
            
            // 清除之前的验证状态
            input.classList.remove('is-invalid', 'is-valid');
            
            // 删除已有的错误消息
            const parent = input.parentNode;
            const existingFeedback = parent.querySelector('.invalid-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            
            // 如果输入框为空且不显示消息，则跳过验证
            const isEmpty = input.value.trim() === '';
            if (isEmpty && !showMessage) {
                return true;
            }
            
            // 邮箱验证
            if (inputId.includes('email')) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (isEmpty) {
                    isValid = false;
                    errorMessage = '请输入邮箱地址';
                } else if (!emailRegex.test(input.value)) {
                    isValid = false;
                    errorMessage = '请输入有效的邮箱格式';
                }
            }
            
            // 密码验证
            else if (inputId.includes('password')) {
                // 注册或重置密码时，使用更严格的验证
                if (inputId === 'register-password' || inputId === 'reset-new-password') {
                    if (isEmpty) {
                        isValid = false;
                        errorMessage = '请输入密码';
                    } else if (input.value.length < 8) {
                        isValid = false;
                        errorMessage = '密码至少需要8个字符';
                    } else if (!/[a-zA-Z]/.test(input.value) || !/[0-9]/.test(input.value)) {
                        isValid = false;
                        errorMessage = '密码应包含字母和数字';
                    }
                } else if (isEmpty) {
                    isValid = false;
                    errorMessage = '请输入密码';
                }
            }
            
            // 验证码验证
            else if (inputId.includes('verification-code')) {
                if (isEmpty) {
                    isValid = false;
                    errorMessage = '请输入验证码';
                } else if (!/^\d{6}$/.test(input.value)) {
                    isValid = false;
                    errorMessage = '验证码应为6位数字';
                }
            }
            
            // 应用验证结果
            if (!isValid && showMessage) {
                input.classList.add('is-invalid');
                
                if (errorMessage) {
                    const feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    feedback.textContent = errorMessage;
                    parent.appendChild(feedback);
                }
            } else if (!isEmpty && isValid) {
                input.classList.add('is-valid');
            }
            
            return isValid;
        }
    </script>
</body>
</html>
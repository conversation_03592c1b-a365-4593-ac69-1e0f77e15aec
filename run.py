"""
Flask Application Entry Point
用于开发环境的应用启动脚本
"""
import os
import click
from adspower_manager import create_app
from adspower_manager.helpers import init_default_data
from extensions import db

# 根据环境变量创建应用实例
config_name = os.environ.get('APP_ENV', 'production')
app = create_app(config_name=config_name)


@click.command('init-db')
@click.pass_context
def initialize_database_command(ctx):
    """Creates initial database tables and default data."""
    with app.app_context():
        # 使用 Flask-Migrate 管理数据库表创建
        # db.create_all() 不应该直接使用，应该通过 flask db upgrade
        
        # 初始化默认数据
        from adspower_manager.helpers import init_default_data
        init_default_data(app)
        
    click.echo('数据库初始化完成。')


# 注册CLI命令
app.cli.add_command(initialize_database_command)


# 注册维护模式检查
def register_maintenance_mode_check(app_instance):
    """Register maintenance mode check"""
    @app_instance.before_request
    def check_maintenance_mode():
        from flask import request, render_template
        
        if request.path.startswith('/static/') or request.path.startswith('/api/'):
            return None
            
        maintenance_mode = os.environ.get('MAINTENANCE_MODE', '').lower() == 'true'
        if maintenance_mode:
            has_bypass_cookie = request.cookies.get('bypass_maintenance') == 'true'
            if not has_bypass_cookie:
                return render_template('maintenance.html')
        return None


# 注册维护模式
register_maintenance_mode_check(app)


if __name__ == '__main__':
    # 获取配置
    port = app.config.get('PORT', 5000)
    debug_mode = app.config.get('DEBUG', False)
    
    # 获取应用的logger
    app.logger.info(f"正在启动Flask应用，地址: http://0.0.0.0:{port} (调试模式={debug_mode})")
    
    # 启动应用
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
"""
Helper functions for the AdsPower Manager application
"""
import logging
from extensions import db
from .models import AdspowerAccount, User, SubscriptionType

logger = logging.getLogger(__name__)


def register_accounts_from_db(app_instance):
    """Register active AdsPower accounts from database to WebDriverManager"""
    from .webdriver_pool import get_account_driver_manager
    
    logger.info("正在从数据库注册活跃账号...")
    try:
        # Ensure this runs within an app context
        with app_instance.app_context():
            # 检查数据库表是否存在
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            if 'adspower_accounts' not in tables:
                logger.warning("数据库表 'adspower_accounts' 不存在，跳过账号注册。")
                return
            
            accounts = AdspowerAccount.query.filter_by(is_active=True).all()
        if not accounts:
            logger.info("数据库中没有找到活跃账号需要注册。")
            return
        
        manager = get_account_driver_manager()
        if not manager:
            logger.error("AccountWebDriverManager不可用，无法注册账号。")
            return

        registered_count = 0
        for acc in accounts:
            try:
                cookies = acc.cookies
                manager.add_managed_account(
                    account_id=acc.id,
                    username=acc.username,
                    password=acc.password,
                    totp_secret=acc.totp_secret,
                    cookies=cookies
                )
                registered_count += 1
                logger.info(f"已注册账号: {acc.username} (ID: {acc.id})")
            except Exception as reg_err:
                logger.error(f"注册账号失败 {acc.username} (ID: {acc.id}): {reg_err}", exc_info=True)
        logger.info(f"完成账号注册。已注册: {registered_count}/{len(accounts)}")
    except Exception as db_err:
        logger.error(f"从数据库获取/注册账号时出错: {db_err}", exc_info=True)


def init_default_data(app_instance):
    """Initializes default data like admin user, subscription types."""
    # 使用模块级别的logger，保持一致性
    logger_local = logging.getLogger(__name__)
    try:
        with app_instance.app_context():
            # Create default admin user
            admin_email = app_instance.config.get('DEFAULT_ADMIN_EMAIL', '<EMAIL>')
            admin_password = app_instance.config.get('DEFAULT_ADMIN_PASSWORD', 'admin123')
            
            admin = User.query.filter_by(email=admin_email).first()
            if not admin:
                admin = User(
                    email=admin_email,
                    is_admin=True,
                    is_active=True
                )
                admin.set_password(admin_password)
                db.session.add(admin)
                logger_local.info(f"已创建默认管理员账号: {admin_email}")
            
            # Create default normal user
            user_email = app_instance.config.get('DEFAULT_USER_EMAIL', '<EMAIL>')
            user_password = app_instance.config.get('DEFAULT_USER_PASSWORD', 'user123')
            
            user = User.query.filter_by(email=user_email).first()
            if not user:
                user = User(
                    email=user_email,
                    is_admin=False,
                    is_active=True
                )
                user.set_password(user_password)
                db.session.add(user)
                logger_local.info(f"已创建默认普通用户账号: {user_email}")
            
            # Create default subscription type
            test_sub_code = 'test'
            test_subscription = SubscriptionType.query.filter_by(code=test_sub_code).first()
            if not test_subscription and app_instance.config.get('CREATE_DEFAULT_SUB_TYPE', True):
                test_subscription = SubscriptionType(
                    code=test_sub_code,
                    name='测试套餐',
                    max_devices=app_instance.config.get('DEFAULT_SUB_MAX_DEVICES', 1),
                    price=app_instance.config.get('DEFAULT_SUB_PRICE', 1.0),
                    days=app_instance.config.get('DEFAULT_SUB_DAYS', 1),
                    requirements=None,
                    is_public=True
                )
                db.session.add(test_subscription)
                logger_local.info(f"已创建默认订阅类型: {test_sub_code}")
            
            db.session.commit()
            logger_local.info("默认数据初始化完成。")
            
    except Exception as e:
        if 'db' in locals() and hasattr(db, 'session'):
            db.session.rollback()
        logger_local.error(f"初始化默认数据时出错: {str(e)}", exc_info=True)
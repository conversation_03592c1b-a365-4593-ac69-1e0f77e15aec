import threading
import logging
import json
# Use relative imports for services and models within the same package
from .services import SubscriptionService # Keep only necessary service imports
from .models import AdspowerAccount, db, Device, LoginSession, Subscription, User, SubscriptionInstance # 添加Subscription、User和SubscriptionInstance的导入
from .adspower_api import get_adspower_api
from .webdriver_pool import get_account_driver_manager # Keep only manager access
from flask import current_app # Keep current_app for flask context
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
import atexit
import time # Keep time for logging/timestamps if needed
from sqlalchemy import and_
from datetime import datetime, timedelta

# Configuration for logger
logger = logging.getLogger(__name__)

scheduler = None # Global scheduler instance for APScheduler

# 拆分后的任务1：退出订阅过期用户的所有设备
def logout_expired_users_devices(app_context):
    """定时任务：退出订阅已过期用户的所有设备"""
    job_name = "Logout Expired Users Devices"
    with app_context():
        logger.info(f"[{job_name}] 定时任务: 开始检查并退出订阅过期用户的设备...")
        adspower_api = get_adspower_api()
        logger.debug(f"[{job_name}] 已获取AdsPower API实例")
        total_devices_logged_out = 0
        processed_users = 0

        try:
            # 获取所有没有活跃订阅的用户
            current_time = datetime.utcnow()
            # 查找没有任何订阅或所有订阅都已过期的用户
            users_without_active_subscription = []
            
            all_users = User.query.all()
            logger.debug(f"[{job_name}] 找到 {len(all_users)} 个用户")
            
            for user in all_users:
                active_subscription = Subscription.query.filter(
                    Subscription.user_id == user.id,
                    Subscription.end_date > current_time
                ).first()
                
                if not active_subscription:
                    users_without_active_subscription.append(user)
            
            logger.info(f"[{job_name}] 找到 {len(users_without_active_subscription)} 个没有活跃订阅的用户")

            for user in users_without_active_subscription:
                processed_users += 1
                user_log_prefix = f"用户 {user.email} (ID: {user.id})"
                logger.debug(f"[{job_name}] 开始处理第 {processed_users}/{len(users_without_active_subscription)} 个用户: {user_log_prefix}")

                # 查找该用户的所有设备
                devices = Device.query.filter_by(user_id=user.id).all()
                if not devices:
                    logger.info(f"[{job_name}] {user_log_prefix} 没有关联设备，无需操作")
                    continue

                devices_count = len(devices)
                logger.info(f"[{job_name}] {user_log_prefix} 发现 {devices_count} 个关联设备，准备退出")

                # 退出每个设备
                for device in devices:
                    device_log_prefix = f"{user_log_prefix} 设备 ID: {device.id}, 名称: {device.device_name}"
                    
                    # 处理设备退出的通用逻辑
                    logout_result = _logout_device_common(device, device_log_prefix, adspower_api, job_name)
                    if logout_result:
                        total_devices_logged_out += 1
            
            logger.info(f"[{job_name}] 定时任务: 订阅过期用户设备退出完成，共退出 {total_devices_logged_out} 个设备")
            return total_devices_logged_out

        except Exception as e:
            logger.error(f"[{job_name}] 定时任务: 退出订阅过期用户设备任务出错: {e}", exc_info=True)
            try:
                db.session.rollback()
            except Exception as rb_err:
                logger.error(f"[{job_name}] 定时任务: 回滚退出设备任务时出错: {rb_err}", exc_info=True)
            return 0

# 拆分后的任务2：退出不匹配订阅实例的设备
def logout_mismatched_devices(app_context):
    """定时任务：退出不属于用户当前订阅实例的设备"""
    job_name = "Logout Mismatched Devices"
    with app_context():
        logger.info(f"[{job_name}] 定时任务: 开始检查并退出不匹配订阅实例的设备...")
        adspower_api = get_adspower_api()
        logger.debug(f"[{job_name}] 已获取AdsPower API实例")
        total_devices_logged_out = 0
        processed_users = 0

        try:
            # 获取所有有活跃订阅的用户
            current_time = datetime.utcnow()
            users_with_active_subscription = User.query.join(Subscription).filter(
                Subscription.end_date > current_time
            ).distinct().all()
            
            logger.info(f"[{job_name}] 找到 {len(users_with_active_subscription)} 个有活跃订阅的用户")

            for user in users_with_active_subscription:
                processed_users += 1
                user_log_prefix = f"用户 {user.email} (ID: {user.id})"
                logger.debug(f"[{job_name}] 开始处理第 {processed_users}/{len(users_with_active_subscription)} 个用户: {user_log_prefix}")

                # 获取用户当前的活跃订阅
                active_subscription = Subscription.query.filter(
                    Subscription.user_id == user.id,
                    Subscription.end_date > current_time
                ).first()
                
                if not active_subscription:
                    logger.warning(f"[{job_name}] {user_log_prefix} 意外地没有找到活跃订阅")
                    continue
                
                logger.info(f"[{job_name}] {user_log_prefix} 有活跃订阅 (ID: {active_subscription.id}, 实例ID: {active_subscription.subscription_instance_id})")
                
                # 获取当前订阅实例关联的所有AdsPower账号ID
                valid_adspower_account_ids = []
                if active_subscription.subscription_instance_id:
                    subscription_instance = SubscriptionInstance.query.get(active_subscription.subscription_instance_id)
                    if subscription_instance:
                        valid_adspower_account_ids = [acc.id for acc in subscription_instance.adspower_accounts.filter_by(is_active=True).all()]
                        logger.debug(f"[{job_name}] {user_log_prefix} 当前订阅实例的有效AdsPower账号IDs: {valid_adspower_account_ids}")

                # 查找该用户的所有设备
                devices = Device.query.filter_by(user_id=user.id).all()
                if not devices:
                    logger.info(f"[{job_name}] {user_log_prefix} 没有关联设备，无需操作")
                    continue

                devices_count = len(devices)
                logger.debug(f"[{job_name}] {user_log_prefix} 发现 {devices_count} 个关联设备，准备检查")

                # 检查并处理每个设备
                for device in devices:
                    device_log_prefix = f"{user_log_prefix} 设备 ID: {device.id}, 名称: {device.device_name}"
                    
                    # 检查设备是否属于当前订阅实例
                    if device.adspower_account_id not in valid_adspower_account_ids:
                        logger.info(f"[{job_name}] {device_log_prefix} - 设备不属于用户当前订阅实例（账号ID: {device.adspower_account_id} 不在有效列表中），需要退出")
                        
                        # 处理设备退出
                        logout_result = _logout_device_common(device, device_log_prefix, adspower_api, job_name)
                        if logout_result:
                            total_devices_logged_out += 1
                    else:
                        logger.debug(f"[{job_name}] {device_log_prefix} - 设备属于当前订阅实例，保留")
            
            logger.info(f"[{job_name}] 定时任务: 不匹配订阅实例设备退出完成，共退出 {total_devices_logged_out} 个设备")
            return total_devices_logged_out

        except Exception as e:
            logger.error(f"[{job_name}] 定时任务: 退出不匹配设备任务出错: {e}", exc_info=True)
            try:
                db.session.rollback()
            except Exception as rb_err:
                logger.error(f"[{job_name}] 定时任务: 回滚退出设备任务时出错: {rb_err}", exc_info=True)
            return 0

# 通用的设备退出处理函数
def _logout_device_common(device, device_log_prefix, adspower_api, job_name):
    """
    通用的设备退出处理逻辑
    返回 True 表示设备成功退出，False 表示失败
    """
    # 检查必要的信息
    if not device.adspower_account_id:
        logger.warning(f"[{job_name}] {device_log_prefix} 没有关联的AdsPower账号，无法远程退出，尝试直接删除记录")
        try:
            db.session.delete(device)
            db.session.commit()
            logger.info(f"[{job_name}] {device_log_prefix} 本地记录已删除 (无关联账号)")
            return True
        except Exception as del_err:
            logger.error(f"[{job_name}] {device_log_prefix} 删除本地记录时出错: {del_err}", exc_info=True)
            db.session.rollback()
            return False

    adspower_account = AdspowerAccount.query.get(device.adspower_account_id)
    if not adspower_account:
        logger.warning(f"[{job_name}] {device_log_prefix} 找不到关联的AdsPower账号 {device.adspower_account_id}，无法远程退出，尝试直接删除记录")
        try:
            db.session.delete(device)
            db.session.commit()
            logger.info(f"[{job_name}] {device_log_prefix} 本地记录已删除 (关联账号不存在)")
            return True
        except Exception as del_err:
            logger.error(f"[{job_name}] {device_log_prefix} 删除本地记录时出错: {del_err}", exc_info=True)
            db.session.rollback()
            return False

    if not device.device_name or not device.device_type:
        logger.warning(f"[{job_name}] {device_log_prefix} 缺少名称或类型信息，无法远程退出，尝试直接删除记录")
        try:
            db.session.delete(device)
            db.session.commit()
            logger.info(f"[{job_name}] {device_log_prefix} 本地记录已删除 (缺少设备名称/类型)")
            return True
        except Exception as del_err:
            logger.error(f"[{job_name}] {device_log_prefix} 删除本地记录时出错: {del_err}", exc_info=True)
            db.session.rollback()
            return False

    # 尝试远程退出设备
    logger.info(f"[{job_name}] {device_log_prefix} 尝试退出 (账号: {adspower_account.username}, 类型: {device.device_type})")
    success, message = adspower_api.logout_device(adspower_account, device.device_name, device.device_type)

    # 无论成功与否，都删除本地记录
    try:
        if success:
            logger.info(f"[{job_name}] {device_log_prefix} 成功退出: {message}")
        else:
            logger.warning(f"[{job_name}] {device_log_prefix} 退出失败: {message}")
        
        logger.debug(f"[{job_name}] 从数据库中删除设备 {device.id}...")
        db.session.delete(device)
        db.session.commit()
        logger.info(f"[{job_name}] {device_log_prefix} 本地记录已删除")
        return True
    except Exception as del_err:
        logger.error(f"[{job_name}] {device_log_prefix} 从数据库删除设备时出错: {del_err}", exc_info=True)
        db.session.rollback()
        return False

# 新增的清理未知设备任务
def cleanup_unknown_devices(app_context):
    """定时任务：检查AdsPower账号中的设备，退出不在数据库中的设备"""
    job_name = "Cleanup Unknown Devices"
    with app_context():
        logger.info(f"[{job_name}] 定时任务: 开始检查并清理未知设备...")
        adspower_api = get_adspower_api()
        logger.debug(f"[{job_name}] 已获取AdsPower API实例")
        
        try:
            # 获取所有活跃的AdsPower账号
            logger.debug(f"[{job_name}] 查询所有活跃的AdsPower账号...")
            active_accounts = AdspowerAccount.query.filter_by(is_active=True).all()
            if not active_accounts:
                logger.info(f"[{job_name}] 定时任务: 没有活跃的AdsPower账号，跳过清理未知设备")
                return 0
                
            logger.debug(f"[{job_name}] 找到 {len(active_accounts)} 个活跃账号")
            # 用于统计退出的设备数量
            total_logged_out = 0
            
            # 获取当前处于登录过程中的会话（基于会话有效期）
            logger.debug(f"[{job_name}] 查询仍在有效期内的登录会话...")
            current_time = datetime.utcnow()
            # 查询所有未过期的会话（基于expiration_timestamp）
            pending_sessions = LoginSession.query.filter(
                LoginSession.expiration_timestamp > current_time
            ).all()
            
            # 收集处于登录过程中的账号IDs
            accounts_in_login_process = set()
            for session in pending_sessions:
                if session.adspower_account_id:
                    accounts_in_login_process.add(session.adspower_account_id)
                    logger.info(f"[{job_name}] 定时任务: 账号 {session.adspower_account_id} (邮箱: {session.adspower_account.username if session.adspower_account else '未知'}) 正在登录过程中，不进行设备清理")
            
            logger.info(f"[{job_name}] 定时任务: 共有 {len(accounts_in_login_process)} 个账号正在登录过程中，将跳过处理")
            
            # 处理每个账号
            accounts_processed = 0
            for account in active_accounts:
                accounts_processed += 1
                account_log_prefix = f"账号 {account.username} (ID: {account.id})"
                logger.debug(f"[{job_name}] 处理第 {accounts_processed}/{len(active_accounts)} 个账号: {account_log_prefix}")
                # 如果账号正在登录过程中，跳过处理
                if account.id in accounts_in_login_process:
                    logger.info(f"[{job_name}] 定时任务: 跳过 {account_log_prefix}，因为它正在登录过程中")
                    continue
                
                try:
                    # 获取账号的设备信息
                    logger.debug(f"[{job_name}] 获取 {account_log_prefix} 的设备信息...")
                    devices_info = adspower_api.get_devices_info(account)
                    if devices_info is None:
                        logger.warning(f"[{job_name}] 定时任务: 无法获取 {account_log_prefix} 的设备信息，跳过")
                        continue
                    
                    if not devices_info:
                        logger.info(f"[{job_name}] 定时任务: {account_log_prefix} 没有已登录设备")
                        continue
                    
                    logger.debug(f"[{job_name}] {account_log_prefix} 有 {len(devices_info)} 个已登录设备")
                    
                    # 获取数据库中该账号的所有设备
                    db_devices = Device.query.filter_by(adspower_account_id=account.id).all()
                    # 创建设备ID集合和name+type集合用于比较
                    db_device_ids = {device.device_id for device in db_devices if device.device_id}
                    db_device_name_types = {(device.device_name.lower(), device.device_type.lower()) 
                                          for device in db_devices 
                                          if device.device_name and device.device_type}
                    
                    logger.debug(f"[{job_name}] 定时任务: 数据库中 {account_log_prefix} 的设备IDs: {db_device_ids}")
                    logger.debug(f"[{job_name}] 定时任务: 数据库中 {account_log_prefix} 的设备name+types: {db_device_name_types}")
                    
                    # 检查每个设备是否在数据库中
                    for device in devices_info:
                        # 获取设备标识信息
                        device_id = (device.get('id') or 
                                   device.get('device_id') or 
                                   (device.get('raw_data', {}).get('device_id') if isinstance(device.get('raw_data'), dict) else None))
                        device_name = device.get('name') or device.get('device_name')
                        device_type = device.get('device_type')
                        
                        is_known_device = False
                        
                        # 优先通过device_id判断
                        if device_id and device_id in db_device_ids:
                            is_known_device = True
                            logger.debug(f"[{job_name}] 定时任务: 设备ID '{device_id}' 在数据库中找到")
                        # 如果没有device_id或未找到，通过name+type判断
                        elif device_name and device_type:
                            device_tuple = (device_name.lower(), device_type.lower())
                            if device_tuple in db_device_name_types:
                                is_known_device = True
                                logger.debug(f"[{job_name}] 定时任务: 设备 '{device_name}' ({device_type}) 通过name+type在数据库中找到")
                        
                        if not is_known_device and device_name:
                            logger.info(f"[{job_name}] 定时任务: 发现未知设备: {device_name} (类型: {device_type}, ID: {device_id})，尝试退出")
                            logger.debug(f"[{job_name}] 调用AdsPower API退出设备: {device_name}...")
                            success, message = adspower_api.logout_device(account, device_name, device_type)
                            if success:
                                total_logged_out += 1
                                logger.info(f"[{job_name}] 定时任务: 成功退出未知设备: {device_name} - {message}")
                            else:
                                logger.warning(f"[{job_name}] 定时任务: 退出未知设备 {device_name} 失败: {message}")
                
                except Exception as account_e:
                    logger.error(f"[{job_name}] 定时任务: 处理 {account_log_prefix} 的设备时出错: {account_e}", exc_info=True)
                    continue
            
            logger.info(f"[{job_name}] 定时任务: 设备清理完成，共退出 {total_logged_out} 个未知设备")
            return total_logged_out
            
        except Exception as e:
            logger.error(f"[{job_name}] 定时任务: 清理未知设备任务出错: {e}", exc_info=True)
            try:
                db.session.rollback()
            except Exception as rb_err:
                logger.error(f"[{job_name}] 定时任务: 回滚清理未知设备任务时出错: {rb_err}", exc_info=True)
            return 0

# 新增的清理系统中已不存在于AdsPower的设备记录任务
def cleanup_orphaned_device_records(app_context):
    """定时任务：清理系统数据库中存在但在AdsPower账号中已不存在的设备记录"""
    job_name = "Cleanup Orphaned Devices"
    with app_context():
        logger.info(f"[{job_name}] 定时任务: 开始清理系统中的孤立设备记录...")
        
        # 检查数据库表是否存在
        from extensions import db
        inspector = db.inspect(db.engine)
        tables = inspector.get_table_names()
        if 'adspower_accounts' not in tables:
            logger.warning(f"[{job_name}] 数据库表 'adspower_accounts' 不存在，跳过清理任务")
            return 0
            
        adspower_api = get_adspower_api()
        logger.debug(f"[{job_name}] 已获取AdsPower API实例")
        
        try:
            # 统计删除的设备数量
            total_deleted = 0
            
            # 获取所有活跃的AdsPower账号
            logger.debug(f"[{job_name}] 查询所有活跃的AdsPower账号...")
            active_accounts = AdspowerAccount.query.filter_by(is_active=True).all()
            if not active_accounts:
                logger.info(f"[{job_name}] 定时任务: 没有活跃的AdsPower账号，跳过清理")
                return 0
            
            logger.debug(f"[{job_name}] 找到 {len(active_accounts)} 个活跃账号")
            
            # 处理每个账号
            for account in active_accounts:
                account_log_prefix = f"账号 {account.username} (ID: {account.id})"
                logger.debug(f"[{job_name}] 检查 {account_log_prefix} 的设备记录...")
                
                try:
                    # 获取该账号在AdsPower中的实际设备列表
                    devices_info = adspower_api.get_devices_info(account)
                    if devices_info is None:
                        logger.warning(f"[{job_name}] 无法获取 {account_log_prefix} 的设备信息，跳过")
                        continue
                    
                    # 收集AdsPower中存在的设备ID和name+type组合
                    adspower_device_ids = set()
                    adspower_device_name_types = set()
                    
                    for device_info in devices_info:
                        # 获取设备ID
                        device_id = (device_info.get('id') or 
                                   device_info.get('device_id') or 
                                   (device_info.get('raw_data', {}).get('device_id') if isinstance(device_info.get('raw_data'), dict) else None))
                        if device_id:
                            adspower_device_ids.add(device_id)
                        
                        # 获取name+type组合
                        device_name = device_info.get('name') or device_info.get('device_name')
                        device_type = device_info.get('device_type')
                        if device_name and device_type:
                            adspower_device_name_types.add((device_name.lower(), device_type.lower()))
                    
                    logger.debug(f"[{job_name}] {account_log_prefix} 在AdsPower中有 {len(adspower_device_ids)} 个设备ID, {len(adspower_device_name_types)} 个name+type组合")
                    
                    # 获取数据库中该账号的所有设备记录
                    db_devices = Device.query.filter_by(adspower_account_id=account.id).all()
                    logger.debug(f"[{job_name}] {account_log_prefix} 在数据库中有 {len(db_devices)} 个设备记录")
                    
                    # 检查每个数据库设备记录是否在AdsPower中存在
                    for device in db_devices:
                        is_orphaned = True
                        
                        # 优先通过device_id判断
                        if device.device_id and device.device_id in adspower_device_ids:
                            is_orphaned = False
                        # 如果没有device_id或未找到，通过name+type判断
                        elif device.device_name and device.device_type:
                            device_tuple = (device.device_name.lower(), device.device_type.lower())
                            if device_tuple in adspower_device_name_types:
                                is_orphaned = False
                        
                        if is_orphaned:
                            device_log_prefix = f"设备 {device.device_name} (ID: {device.id}, 用户: {device.user_id})"
                            logger.info(f"[{job_name}] 发现孤立设备记录: {device_log_prefix}，准备删除")
                            
                            try:
                                db.session.delete(device)
                                db.session.commit()
                                total_deleted += 1
                                logger.info(f"[{job_name}] 成功删除孤立设备记录: {device_log_prefix}")
                            except Exception as del_err:
                                logger.error(f"[{job_name}] 删除孤立设备记录 {device_log_prefix} 时出错: {del_err}", exc_info=True)
                                db.session.rollback()
                
                except Exception as account_err:
                    logger.error(f"[{job_name}] 处理 {account_log_prefix} 时出错: {account_err}", exc_info=True)
                    continue
            
            logger.info(f"[{job_name}] 定时任务: 孤立设备记录清理完成，共删除 {total_deleted} 条记录")
            return total_deleted
            
        except Exception as e:
            logger.error(f"[{job_name}] 定时任务: 清理孤立设备记录任务出错: {e}", exc_info=True)
            try:
                db.session.rollback()
            except Exception as rb_err:
                logger.error(f"[{job_name}] 定时任务: 回滚清理任务时出错: {rb_err}", exc_info=True)
            return 0

# 新增的同步cookies到数据库的任务
def sync_cookies_to_database_job(app_context):
    """定时任务：将所有AdsPower账号的内存缓存cookies同步到数据库"""
    job_name = "Sync Cookies"
    with app_context():
        logger.info(f"[{job_name}] 定时任务: 开始同步AdsPower账号cookies到数据库...")
        
        try:
            # 获取WebDriver管理器以执行同步操作
            driver_manager = get_account_driver_manager()
            if not driver_manager:
                logger.warning(f"[{job_name}] 定时任务: 无法获取AccountWebDriverManager实例，跳过cookies同步")
                return 0
            
            # 直接调用AccountWebDriverManager的同步方法    
            cookies_updated = driver_manager.sync_cookies_to_database(app_context)
            
            logger.info(f"[{job_name}] 定时任务: cookies同步完成，更新了 {cookies_updated} 个账号的cookies")
            return cookies_updated
        
        except Exception as e:
            logger.error(f"[{job_name}] 定时任务: cookies同步任务出错: {e}", exc_info=True)
            try:
                db.session.rollback()
            except Exception as rb_err:
                logger.error(f"[{job_name}] 定时任务: 回滚cookies同步任务时出错: {rb_err}", exc_info=True)
            return 0

# 定时更新 AccountWebDriverManager 中的托管账号
def update_managed_accounts_job(app_context):
    """定期从数据库加载活跃账号并更新到 AccountWebDriverManager"""
    job_name = "Update Managed Accounts"
    logger.info(f"[{job_name}] 定时任务: 正在更新AccountWebDriverManager中的托管账号...")
    with app_context(): # 使用传入的应用上下文
        try:
            logger.debug(f"[{job_name}] 查询活跃的AdsPower账号...")
            accounts = AdspowerAccount.query.filter_by(is_active=True).all()
            if not accounts:
                logger.info(f"[{job_name}] 定时任务: 数据库中未找到活跃账号。")
                # 如果没有活跃账号，可能需要通知管理器移除所有现有账号？
                # 暂时保持不变，仅在有活跃账号时更新
                # return
            
            logger.debug(f"[{job_name}] 找到 {len(accounts)} 个活跃账号，获取账号管理器...")
            manager = get_account_driver_manager()
            if not manager:
                 logger.warning(f"[{job_name}] 定时任务: AccountWebDriverManager尚未初始化。跳过更新。")
                 return

            # 检查管理器是否仍在运行
            if not manager.running:
                logger.warning(f"[{job_name}] 定时任务: AccountWebDriverManager管理线程未运行。跳过更新。")
                return

            updated_count = 0
            error_count = 0
            removed_count = 0
            try:
                logger.debug(f"[{job_name}] 获取当前托管账号ID列表...")
                current_managed_ids = manager.get_managed_account_ids() # 获取当前托管的IDs
            except AttributeError:
                 logger.error(f"[{job_name}] 定时任务: AccountWebDriverManager实例缺少'get_managed_account_ids'方法。", exc_info=True)
                 return # 无法继续
            except Exception as get_ids_err:
                 logger.error(f"[{job_name}] 定时任务: 获取托管账号ID时出错: {get_ids_err}", exc_info=True)
                 return # 无法安全继续

            # 添加/更新数据库中的活跃账号到管理器
            logger.debug(f"[{job_name}] 开始同步数据库账号到管理器...")
            db_active_ids = set()
            for acc in accounts:
                db_active_ids.add(str(acc.id)) # 收集数据库中活跃的ID
                try:
                    cookies = acc.cookies # 从 cookies 字段获取
                    logger.debug(f"[{job_name}] 添加/更新账号 {acc.username} (ID: {acc.id})...")
                    # 将账号信息添加到管理器
                    manager.add_managed_account(
                        account_id=str(acc.id),
                        username=acc.username,
                        password=acc.password, # 注意：频繁传递密码的安全性
                        totp_secret=acc.totp_secret, # 注意：安全性
                        cookies=cookies
                    )
                    updated_count += 1
                    logger.debug(f"[{job_name}] 成功更新/注册账号: {acc.username}")
                except AttributeError:
                     logger.error(f"[{job_name}] 定时任务: AccountWebDriverManager实例缺少'add_managed_account'方法。", exc_info=True)
                     error_count += 1
                except Exception as add_err:
                    logger.error(f"[{job_name}] 定时任务: 添加/更新托管账号 {acc.username} (ID: {acc.id}) 时出错: {add_err}", exc_info=True)
                    error_count += 1

            # 从管理器中移除不再活跃的账号
            logger.debug(f"[{job_name}] 检查需要从管理器中移除的账号...")
            ids_to_remove = current_managed_ids - db_active_ids
            for acc_id_str in ids_to_remove:
                try:
                    logger.debug(f"[{job_name}] 从管理器中移除账号 ID: {acc_id_str}...")
                    manager.remove_managed_account(acc_id_str)
                    removed_count += 1
                    logger.info(f"[{job_name}] 定时任务: 已从管理器中移除不活跃账号 ID {acc_id_str}。")
                except AttributeError:
                     logger.error(f"[{job_name}] 定时任务: AccountWebDriverManager实例缺少'remove_managed_account'方法。", exc_info=True)
                     error_count += 1
                except Exception as remove_err:
                    logger.error(f"[{job_name}] 定时任务: 移除托管账号 ID {acc_id_str} 时出错: {remove_err}", exc_info=True)
                    error_count += 1

            logger.info(f"[{job_name}] 定时任务: 托管账号同步完成。更新/添加: {updated_count}，移除: {removed_count}，错误: {error_count}")
        except Exception as job_err:
            logger.error(f"[{job_name}] 定时任务: 托管账号更新任务期间出错: {job_err}", exc_info=True)
            # 考虑是否需要回滚，但此任务通常不直接修改数据库状态
            # try: db.session.rollback() 
            # except: pass

# --- APScheduler based init function ---
def init_scheduler(app):
    """初始化 APScheduler 并添加任务"""
    global scheduler # Declare intent to modify the global scheduler variable
    logger.info("开始初始化调度器...")

    if scheduler is not None and scheduler.running:
        logger.warning("调度器已经在运行，跳过初始化。")
        return

    # --- Create the scheduler instance FIRST ---
    logger.info("正在初始化APScheduler后台调度器...")
    scheduler = BackgroundScheduler(daemon=True)
    logger.debug("BackgroundScheduler实例已创建")

    # --- Now add jobs ---
    logger.info("正在添加APScheduler任务...")

    # Job 1: 退出订阅过期用户的设备
    try:
        logger.debug("添加退出订阅过期用户设备任务，间隔：1分钟...")
        scheduler.add_job(
            func=logout_expired_users_devices,
            trigger=IntervalTrigger(minutes=1),
            id='logout_expired_users_devices',
            name='退出订阅过期用户设备',
            args=[app.app_context],
            replace_existing=True
        )
        logger.info("已添加退出订阅过期用户设备任务，间隔：1分钟")
    except Exception as add_job_e:
        logger.error(f"添加退出订阅过期用户设备任务时出错: {add_job_e}", exc_info=True)
    
    # Job 2: 退出不匹配订阅实例的设备
    try:
        logger.debug("添加退出不匹配订阅实例设备任务，间隔：1分钟...")
        scheduler.add_job(
            func=logout_mismatched_devices,
            trigger=IntervalTrigger(minutes=1),
            id='logout_mismatched_devices',
            name='退出不匹配订阅实例设备',
            args=[app.app_context],
            replace_existing=True
        )
        logger.info("已添加退出不匹配订阅实例设备任务，间隔：1分钟")
    except Exception as add_job_e:
        logger.error(f"添加退出不匹配订阅实例设备任务时出错: {add_job_e}", exc_info=True)
    
    # Job 3: Clean up unknown devices
    try:
        logger.debug("添加清理未知设备任务，间隔：1分钟...")
        scheduler.add_job(
            func=cleanup_unknown_devices,
            trigger=IntervalTrigger(minutes=1),
            id='cleanup_unknown_devices',
            name='清理未知设备',
            args=[app.app_context],
            replace_existing=True
        )
        logger.info("已添加清理未知设备任务，间隔：1分钟")
    except Exception as add_job_e:
        logger.error(f"添加清理未知设备任务时出错: {add_job_e}", exc_info=True)
        
    # Job 4: Clean up orphaned device records
    try:
        logger.debug("添加清理孤立设备记录任务，间隔：1分钟...")
        scheduler.add_job(
            func=cleanup_orphaned_device_records,
            trigger=IntervalTrigger(minutes=1),
            id='cleanup_orphaned_device_records',
            name='清理孤立设备记录',
            args=[app.app_context],
            replace_existing=True
        )
        logger.info("已添加清理孤立设备记录任务，间隔：1分钟")
    except Exception as add_job_e:
        logger.error(f"添加清理孤立设备记录任务时出错: {add_job_e}", exc_info=True)
    
    # Job 5: 清理过期的登录会话
    try:
        logger.debug("添加清理过期登录会话任务，间隔：60秒...")
        scheduler.add_job(
            func=cleanup_expired_login_sessions,
            trigger=IntervalTrigger(seconds=60),
            id='cleanup_expired_login_sessions',
            name='清理过期登录会话',
            args=[app.app_context],
            replace_existing=True
        )
        logger.info("已添加清理过期登录会话任务，间隔：60秒")
    except Exception as add_job_e:
        logger.error(f"添加清理过期登录会话任务时出错: {add_job_e}", exc_info=True)
        
    # Job 6: Sync cookies to database
    # try:
    #     logger.debug("添加cookies同步任务，间隔：30秒...")
    #     scheduler.add_job(
    #         func=sync_cookies_to_database_job,
    #         trigger=IntervalTrigger(seconds=30),
    #         id='sync_cookies_to_database',
    #         name='同步Cookies到数据库',
    #         args=[app.app_context],
    #         replace_existing=True
    #     )
    #     logger.info("已添加cookies同步任务，间隔：30秒")
    # except Exception as add_job_e:
    #     logger.error(f"添加cookies同步任务时出错: {add_job_e}")
        
    # # Job 5: Update managed accounts (用户要求移除)
    # try:
    #     logger.debug(f"添加更新管理账号任务，间隔：{REFRESH_INTERVAL}秒...")
    #     logger.debug("添加更新托管账号任务，间隔：5分钟...")
    #     scheduler.add_job(
    #         func=update_managed_accounts_job,
    #         trigger=IntervalTrigger(minutes=5), # Runs every 5 minutes
    #         id='update_managed_accounts_job',
    #         name='更新托管账号',
    #         replace_existing=True,
    #         kwargs={'app_context': app.app_context} # Pass context to the job function itself
    #     )
    #     logger.info("已添加任务: 更新托管账号 (间隔: 5分钟)")
    # except Exception as e:
    #     logger.error(f"添加任务'更新托管账号'失败: {e}", exc_info=True)

    # --- Start the scheduler ---
    try:
        logger.info("正在启动调度器...")
        scheduler.start()
        logger.info("调度器启动成功。")
    except Exception as e:
        logger.error(f"启动调度器失败: {e}", exc_info=True)
        scheduler = None # Reset scheduler if start fails
        # Should we raise an error here to prevent app from running without scheduler?
        # raise RuntimeError("Failed to start APScheduler") from e
        return # Indicate failure

    # Register the shutdown function using atexit
    logger.info("正在使用atexit注册调度器关闭钩子。")
    atexit.register(shutdown_scheduler)
    logger.debug("调度器初始化完成")

# --- Simplified Shutdown Function for APScheduler ---
def shutdown_scheduler():
    """关闭 APScheduler"""
    global scheduler
    logger.info("正在尝试关闭调度器...")
    if scheduler and scheduler.running:
        try:
            logger.debug("调度器正在运行，执行关闭操作...")
            # wait=False might cause issues if jobs need to finish cleanly? Test carefully.
            # Let's try wait=True first for safety, unless atexit timeout is a concern.
            scheduler.shutdown(wait=True) # Wait for running jobs to complete
            logger.info("调度器成功关闭（等待任务完成）。")
        except Exception as e:
            logger.error(f"关闭调度器时出错: {e}", exc_info=True)
            # Should we try shutdown(wait=False) as fallback?
    else:
        logger.info("调度器未运行或未初始化。")
    # Clear the global reference after shutdown attempt
    scheduler = None
    logger.debug("调度器引用已清除")

# --- Ensure app.py calls cleanup correctly ---
# Note: The cleanup_resources function in app.py should handle calling
# stop_account_manager() and shutdown_driver_pool().
# This shutdown_scheduler only handles the APScheduler itself.
# Make sure atexit registration order is correct if dependencies exist. 

def background_scheduler_task(app):
    """后台调度任务"""
    try:
        # 检查过期的订阅 (现在由 SubscriptionService 处理，无需在此处重复)
        # logger.debug("Scheduler: 检查过期订阅...")
        # SubscriptionService.check_expired_subscriptions()
        # logger.debug("Scheduler: 过期订阅检查完成")
        
        # 检查并关闭长时间不活动的会话（如果需要的话）
        # ...

        # 检查AdsPower账号状态并同步Cookies到数据库
        account_manager = get_account_driver_manager(create_if_none=False)
        if account_manager:
            logger.debug("Scheduler: 开始同步账号Cookies到数据库...")
            try:
                # 使用 Flask 应用上下文执行数据库操作
                synced_count = account_manager.sync_cookies_to_database(app_context=app.app_context)
                logger.debug(f"Scheduler: 账号Cookies同步完成，更新了 {synced_count} 个账号")
            except Exception as sync_err:
                logger.error(f"Scheduler: 同步Cookies到数据库时出错: {sync_err}", exc_info=True)
        else:
            logger.warning("Scheduler: AccountWebDriverManager 未初始化，无法同步Cookies")
        
    except Exception as e:
        logger.error(f"执行后台调度任务时出错: {e}", exc_info=True)

def cleanup_expired_login_sessions(app_context):
    """清理过期的登录会话"""
    job_name = "Cleanup Login Sessions"
    with app_context():
        logger.info(f"[{job_name}] 开始清理过期的登录会话...")
        now = datetime.utcnow()
        
        try:
            # 查找所有过期但未完成的会话
            expired_sessions = LoginSession.query.filter(
                LoginSession.expiration_timestamp < now,
                LoginSession.completed_time.is_(None),
                LoginSession.login_status != 'expired',
                LoginSession.login_status != 'cancelled'
            ).all()
            
            if not expired_sessions:
                logger.debug(f"[{job_name}] 没有需要清理的过期会话")
                return
            
            for session in expired_sessions:
                session.login_status = 'expired'
                session.logout_time = now
                logger.info(f"[{job_name}] 标记过期会话 {session.id} (用户: {session.user_id}, 账号: {session.adspower_account_id})")
            
            db.session.commit()
            logger.info(f"[{job_name}] 成功清理了 {len(expired_sessions)} 个过期会话")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"[{job_name}] 清理过期会话时出错: {e}", exc_info=True)

from flask import Blueprint, render_template, redirect, url_for, request, current_app, g, session, jsonify # Added g, session
import logging
from ..models import SubscriptionType
from ..utils import jsonify_response

logger = logging.getLogger(__name__)

# 导入蓝图实例
from . import main_bp

@main_bp.route('/')
def index_route():
    """
    主页路由
    - 未登录用户: 显示欢迎页面（包含套餐展示）
    - 已登录用户: 重定向到相应的控制面板
    """
    # 这里无法直接检查JWT token，因为它存储在localStorage
    # 所以创建一个包含JavaScript检查逻辑的页面
    return render_template('welcome.html')

@main_bp.route('/dashboard')
def dashboard_route():
    # Basic check if user is logged in (via session or a decorator if you have one for web pages)
    # This is a placeholder, actual authentication check for web pages might differ from API token auth
    # For example, if using Flask-Login or session-based auth for web.
    # If relying on JWT in localStorage and frontend routing, this backend route might just serve the template.
    return render_template('dashboard.html')

@main_bp.route('/login')
def login_route():
    """
    登录路由
    - USE_SSO=False: 使用本地登录页面
    - USE_SSO=True: 使用Keycloak单点登录
    """
    use_sso = current_app.config.get('USE_SSO', True)
    
    if not use_sso:
        # 使用本地登录
        logger.info("认证模式: 本地登录 (USE_SSO=False)")
        return render_template('index.html')
    else:
        # 使用单点登录
        logger.info("认证模式: 单点登录 (USE_SSO=True)")
        
        # 检查OIDC配置是否完整
        if not current_app.config.get('OIDC_ISSUER_URL'):
            logger.warning("OIDC_ISSUER_URL未配置，回退到本地登录")
            return render_template('index.html')
        
        if not current_app.config.get('OIDC_CLIENT_ID') or not current_app.config.get('OIDC_CLIENT_SECRET'):
            logger.warning("OIDC客户端配置不完整，回退到本地登录")
            return render_template('index.html')
        
        try:
            return redirect(url_for('api.keycloak_login'))
        except Exception as e:
            logger.error(f"无法重定向到Keycloak，错误: {e}", exc_info=True)
            logger.info("单点登录服务不可用，回退到本地登录")
            return render_template('index.html')

@main_bp.route('/admin')
def admin_dashboard_route():
    # Add admin-specific auth check if needed for direct web access
    return render_template('admin.html')

@main_bp.route('/admin/adspower')
def admin_adspower_route():
    # This is a redirect to an anchor within the admin page
    return redirect(url_for('.admin_dashboard_route', _anchor='adspower')) # Use . for current blueprint

@main_bp.route('/payments/epay/result')
def epay_payment_result_route():
    order_id = request.args.get('out_trade_no')
    # This page might need to fetch payment status via an API call from the frontend
    # or be passed more context if rendering server-side directly after payment.
    return render_template('epay_result.html', order_id=order_id)

@main_bp.route('/adspower/direct-login')
def adspower_direct_login():
    """
    AdsPower 直接登录页面路由
    接收登录参数并渲染登录页面
    """
    from datetime import datetime, timezone
    from ..models import LoginSession
    
    token = request.args.get('token')
    username = request.args.get('username')
    password = request.args.get('password')
    account_id = request.args.get('account_id')
    expires = request.args.get('expires')
    
    # 检查必要参数
    if not token:
        return render_template('error.html', 
                             message='登录链接无效：缺少登录令牌',
                             error_type='invalid_link'), 400
    
    # 检查登录会话是否存在且有效
    login_session = LoginSession.query.filter_by(login_token=token).first()
    
    if not login_session:
        return render_template('error.html',
                             message='登录链接无效：会话不存在',
                             error_type='invalid_session'), 404
    
    # 检查会话是否已取消
    if login_session.login_status == 'cancelled':
        return render_template('error.html',
                             message='登录会话已取消',
                             error_type='session_cancelled'), 410
    
    # 检查会话是否已完成
    if login_session.completed_time:
        return render_template('error.html',
                             message='登录会话已完成',
                             error_type='session_completed'), 410
    
    # 检查会话是否已过期
    now_utc = datetime.now(timezone.utc).replace(tzinfo=None)
    if login_session.expiration_timestamp < now_utc:
        return render_template('error.html',
                             message='登录会话已过期',
                             error_type='session_expired'), 410
    
    # 渲染 AdsPower 登录页面模板
    # 注意：模板期望的参数名是 login_token 和 expiration_timestamp_iso
    return render_template('adspower_login.html', 
                           login_token=token,  # 修改：使用模板期望的参数名
                           username=username,
                           password=password,
                           account_id=account_id,
                           expiration_timestamp_iso=expires)  # 修改：使用模板期望的参数名

@main_bp.route('/api/public/subscription-types', methods=['GET'])
def get_public_subscription_types():
    """获取所有公开的订阅类型（无需登录）
    
    返回:
    {
        "success": true,
        "data": {
            "types": [
                {
                    "id": 1,
                    "code": "monthly",
                    "name": "月付会员",
                    "max_devices": 5,
                    "price": 49.99,
                    "days": 30,
                    "requirements": null,
                    "is_public": true,
                    "description": "适合个人使用",
                    "is_recommended": false
                },
                ...
            ]
        }
    }
    """
    try:
        logger.info("[公开订阅类型] 获取公开订阅类型列表请求")
        
        # 只获取公开的订阅类型
        subscription_types = SubscriptionType.query.filter_by(is_public=True).all()
        
        # 转换为JSON格式
        types_data = []
        for type_obj in subscription_types:
            types_data.append({
                "id": type_obj.id,
                "code": type_obj.code,
                "name": type_obj.name,
                "max_devices": type_obj.max_devices,
                "price": type_obj.price,
                "days": type_obj.days,
                "requirements": type_obj.requirements,
                "is_public": type_obj.is_public,
                "description": type_obj.description if hasattr(type_obj, 'description') else None
            })
        
        logger.info(f"[公开订阅类型] 获取到 {len(types_data)} 种公开订阅类型")
        
        return jsonify_response(success=True,
                                message="公开订阅类型列表获取成功",
                                data={"types": types_data})
    except Exception as e:
        logger.error(f"[公开订阅类型] 获取公开订阅类型失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"获取订阅类型失败: {str(e)}",
                                data=None,
                                status_code=500) 
from flask import jsonify

def jsonify_response(success=True, message=None, data=None, status_code=200):
    response_dict = {'success': success}
    if message:
        response_dict['message'] = message
    response_dict['data'] = data # Always include data, even if None
    return jsonify(response_dict), status_code

def get_real_ip(request):
    """
    获取真实的客户端 IP 地址
    
    优先级：
    1. X-Forwarded-For 头部（反向代理转发的真实 IP）
    2. X-Real-IP 头部（某些代理服务器使用）
    3. request.remote_addr（直接连接时的 IP）
    
    Args:
        request: Flask request 对象
        
    Returns:
        str: 客户端 IP 地址
    """
    # 优先从 X-Forwarded-For 获取
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        # X-Forwarded-For 可能包含多个 IP（客户端, 代理1, 代理2...）
        # 取第一个作为真实客户端 IP
        real_ip = forwarded_for.split(',')[0].strip()
        return real_ip
    
    # 其次尝试 X-Real-IP
    real_ip = request.headers.get('X-Real-IP')
    if real_ip:
        return real_ip
    
    # 最后使用 remote_addr
    return request.remote_addr 
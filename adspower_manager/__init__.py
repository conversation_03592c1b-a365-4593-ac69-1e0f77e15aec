"""
AdsPower Manager Application Package
Flask应用的主包，包含应用工厂函数
"""
import os
import sys
import threading
import atexit
import logging
from flask import Flask, g, current_app
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager

# Import extensions
from extensions import db, mail, bcrypt

# Global logger
logger = logging.getLogger(__name__)

# Global variable to hold the scheduler thread
scheduler_thread = None


def create_app(config_name=None, **kwargs):
    """Application factory function."""
    app = Flask(__name__, instance_relative_config=True, 
                template_folder='../templates', 
                static_folder='../static')

    # Determine configuration type
    if config_name is None:
        config_name = os.environ.get('APP_ENV', 'production')  # Default to production

    if config_name == 'production':
        config_object = 'config.ProductionConfig'
    elif config_name == 'testing':
        config_object = 'config.TestingConfig'
    else:
        # 默认使用生产环境配置
        logger.warning(f"无效或未指定 APP_ENV ('{config_name}')，默认使用生产环境配置")
        config_object = 'config.ProductionConfig'
        config_name = 'production'

    app.config.from_object(config_object)
    
    # Load instance config if it exists
    app.config.from_pyfile('application.cfg', silent=True)

    # Override with any explicit kwargs passed to create_app (useful for testing)
    app.config.update(kwargs)
    
    # Ensure instance path exists
    try:
        if not os.path.exists(app.instance_path):
            os.makedirs(app.instance_path)
    except OSError as e:
        logger.error(f"Failed to create instance path at {app.instance_path}: {e}", exc_info=True)

    # Configure Logging
    log_level_from_config = app.config.get('LOG_LEVEL', logging.INFO)
    
    # Check if running a CLI command
    is_cli_command_not_run = len(sys.argv) > 1 and sys.argv[0].endswith('flask') and sys.argv[1] != 'run'
    is_help_command = '--help' in sys.argv

    # 统一的日志格式：ISO8601时间格式，包含模块名、文件名和行号
    log_format = '[%(asctime)s] %(levelname)s in %(module)s (%(filename)s:%(lineno)d): %(message)s'
    date_format = '%Y-%m-%dT%H:%M:%S'
    
    if is_cli_command_not_run and is_help_command:
        logging.basicConfig(level=logging.ERROR, format=log_format, datefmt=date_format)
    elif is_cli_command_not_run:
        logging.basicConfig(level=log_level_from_config, format=log_format, datefmt=date_format)
    else:
        logging.basicConfig(level=log_level_from_config, 
                            format=log_format, datefmt=date_format)
                            
    logger.info(f"Flask 应用 '{app.name}' 已使用 '{config_name}' 配置创建。")
    
    # Security checks
    if not app.config.get('SECRET_KEY') and config_name == 'production':
        logger.critical("CRITICAL: SECRET_KEY is not set in production configuration!")
    if not app.config.get('JWT_SECRET_KEY') and config_name == 'production':
        logger.warning("WARNING: JWT_SECRET_KEY is not set in production configuration!")
        if app.config.get('SECRET_KEY'):
            app.config['JWT_SECRET_KEY'] = app.config['SECRET_KEY']
        else:
            logger.critical("CRITICAL: Neither SECRET_KEY nor JWT_SECRET_KEY is set for JWT in production!")

    # Initialize Extensions
    db.init_app(app)
    mail.init_app(app)
    bcrypt.init_app(app)
    Migrate(app, db, directory='migrations')
    
    # Initialize JWT
    if not app.config.get('JWT_SECRET_KEY'):
        logger.error("JWT_SECRET_KEY is not set. JWT features will not work correctly.")
    JWTManager(app)

    # Register Blueprints
    from .api import api
    from .admin import admin_bp
    from .main import main_bp
    
    app.register_blueprint(api)
    app.register_blueprint(admin_bp)
    app.register_blueprint(main_bp)

    # Request Hooks
    @app.before_request
    def load_services_into_g_before_request():
        from .services.auth_service import AuthService, DeviceAuthService, TwoFactorAuthService
        from .services.device_service import DeviceService
        from .services.payment_service import PaymentService
        from .services.subscription_service import SubscriptionService
        from .services.epay_service import EpayService
        
        if 'auth_service' not in g:
            g.auth_service = AuthService(current_app)
        if 'device_auth_service' not in g:
            g.device_auth_service = DeviceAuthService()
        if 'two_factor_auth_service' not in g:
            g.two_factor_auth_service = TwoFactorAuthService()
        if 'device_service' not in g:
            g.device_service = DeviceService()
        if 'subscription_service' not in g:
            g.subscription_service = SubscriptionService()
        if 'epay_service' not in g:
            g.epay_service = EpayService()
        if 'payment_service' not in g:
            g.payment_service = PaymentService()

    # OIDC Initialization
    from .api.routes import init_oauth as init_api_oauth
    with app.app_context():
        init_api_oauth(app)
        logger.info("OIDC OAuth 客户端已初始化 (api_routes)。")

    # Initialize Resources (WebDriver Pool, Scheduler, etc.)
    # 检查是否应该初始化资源：
    # 1. 不是 CLI 命令
    # 2. 明确启用 WebDriver（通过 ENABLE_WEBDRIVER 配置）或者在生产环境
    should_init_resources = not is_cli_command_not_run and (
        app.config.get('ENABLE_WEBDRIVER', False) or 
        (not app.config.get('TESTING', False))
    )
    
    if should_init_resources:
        logger.info("Initializing application resources (WebDriver, Scheduler)...")
        try:
            from .webdriver_pool import init_driver_pool, start_account_manager, get_account_driver_manager
            from .scheduler import init_scheduler
            if app.config.get('USE_PROTOCOL_MODE', False):
                # 使用协议 跳过selenium初始化
                logger.info("Using protocol mode, skipping WebDriverPool initialization.")
            else:
                logger.info("Initializing WebDriverPool...")
                init_driver_pool(pool_size=app.config.get('WEBDRIVER_POOL_SIZE', 10), 
                                driver_timeout=app.config.get('WEBDRIVER_DRIVER_TIMEOUT', 1800), 
                                check_interval=app.config.get('WEBDRIVER_CHECK_INTERVAL', 300))
                logger.info("WebDriverPool initialized.")
                
                logger.info("Initializing AccountWebDriverManager...")
                manager = get_account_driver_manager()
                logger.info("AccountWebDriverManager obtained.")
                
                logger.info("Starting AccountWebDriverManager background thread...")
                start_account_manager()
                logger.info("AccountWebDriverManager started.")
            
            # Register accounts from DB
            with app.app_context():
                from .helpers import register_accounts_from_db
                register_accounts_from_db(app)
            
            logger.info("Initializing scheduler...")
            global scheduler_thread
            scheduler_thread = init_scheduler(app)
            if scheduler_thread and scheduler_thread.is_alive():
                logger.info("Scheduler started successfully.")
            else:
                logger.warning("Scheduler failed to start or is not alive.")
                
            logger.info("Application resources initialized successfully.")
            app._resources_initialized = True
                
        except Exception as e:
            logger.error(f"Error during application resource initialization: {e}", exc_info=True)
    else:
        logger.info("跳过资源初始化（测试模式或 CLI 命令）。")
        app._resources_initialized = False

    # Register atexit cleanup function
    @atexit.register
    def cleanup_resources_on_exit():
        if not getattr(app, '_resources_initialized', False):
            logger.info("Skipping resource cleanup as they were not initialized.")
            return
            
        logger.info("Flask application shutting down. Cleaning up resources...")
        
        # Stop AccountWebDriverManager
        try:
            if 'adspower_manager.webdriver_pool' in sys.modules:
                webdriver_pool_module = sys.modules['adspower_manager.webdriver_pool']
                if hasattr(webdriver_pool_module, 'stop_account_manager'):
                    from .webdriver_pool import stop_account_manager
                    stop_thread = threading.Thread(target=stop_account_manager)
                    stop_thread.daemon = True
                    stop_thread.start()
                    stop_thread.join(timeout=10.0)
                    if stop_thread.is_alive():
                        logger.warning("AccountWebDriverManager stop operation timed out.")
                    else:
                        logger.info("AccountWebDriverManager stopped.")
        except Exception as e:
            logger.error(f"Error stopping AccountWebDriverManager: {e}", exc_info=True)
    
        # Shutdown WebDriver Pool
        try:
            if 'adspower_manager.webdriver_pool' in sys.modules:
                from .webdriver_pool import shutdown_driver_pool
                shutdown_thread = threading.Thread(target=shutdown_driver_pool)
                shutdown_thread.daemon = True
                shutdown_thread.start()
                shutdown_thread.join(timeout=10.0)
                if shutdown_thread.is_alive():
                    logger.warning("WebDriverPool shutdown operation timed out.")
                else:
                    logger.info("WebDriverPool shutdown completed.")
        except Exception as e:
            logger.error(f"Error shutting down WebDriverPool: {e}", exc_info=True)
    
        logger.info("Resource cleanup attempt complete.")

    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        from .utils import jsonify_response
        return jsonify_response(success=False, message="资源未找到", status_code=404)

    @app.errorhandler(500)
    def internal_error(error):
        from .utils import jsonify_response
        db.session.rollback()
        return jsonify_response(success=False, message="服务器内部错误", status_code=500)

    return app
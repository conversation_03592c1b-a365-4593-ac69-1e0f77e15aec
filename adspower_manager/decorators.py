import functools
from flask import g, request, current_app
from .utils import jsonify_response
from .services.auth_service import AuthService # Assuming AuthService might be needed
import logging

logger = logging.getLogger(__name__)

# 权限检查装饰器
def login_required(f):
    """
    检查用户是否已登录的装饰器
    验证请求中的JWT令牌，并将用户信息添加到g对象
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if 'auth_service' not in g:
            g.auth_service = AuthService(current_app)
        
        auth_header = request.headers.get('Authorization')
        token = None
        logger.debug(f"[认证] 收到Authorization头: {auth_header}")
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(" ")[1]
            logger.debug(f"[认证] 提取到令牌: {token[:10]}...{token[-10:] if len(token) > 20 else ''}")
        else:
            logger.warning(f"[认证] 请求头中未找到有效的Bearer令牌: {auth_header}")

        if not token:
            logger.warning("[认证] 请求缺少有效的Bearer令牌")
            return jsonify_response(success=False, message='未提供认证令牌', data=None, status_code=401)

        user, message = g.auth_service.verify_token(token)

        if not user:
            return jsonify_response(success=False, message=message, data=None, status_code=401)

        g.user = user
        logger.info(f"[认证成功] 用户 {user.id} ({user.email}) 通过令牌认证")
        return f(*args, **kwargs)
    return decorated_function

# 管理员权限检查装饰器
def admin_required(f):
    """
    检查用户是否具有管理员权限的装饰器
    必须在login_required之后使用
    """
    @functools.wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if not g.user.is_admin:
            logger.warning(f"[权限不足] 用户 {g.user.id} ({g.user.email}) 尝试访问管理员资源")
            return jsonify_response(success=False, message="需要管理员权限", data=None, status_code=403)
        
        logger.info(f"[管理员操作] 管理员 {g.user.id} ({g.user.email}) 访问管理员资源")
        return f(*args, **kwargs)
    return decorated_function 
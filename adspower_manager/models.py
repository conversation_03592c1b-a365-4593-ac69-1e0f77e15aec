from datetime import datetime, timedelta
import json
import math
import logging
from sqlalchemy.types import Text # Import Text type
from extensions import db, bcrypt  # 从 extensions 导入

# 配置日志
logger = logging.getLogger(__name__)

# # 邮箱验证码模型 # Removed EmailVerification model
# class EmailVerification(db.Model):
#     """邮箱验证码模型"""
#     
#     __tablename__ = 'email_verifications'
#     
#     id = db.Column(db.Integer, primary_key=True)
#     email = db.Column(db.String(120), nullable=False)
#     code = db.Column(db.String(10), nullable=False)
#     code_type = db.Column(db.String(20), nullable=False)  # register, login, reset
#     is_used = db.Column(db.Boolean, default=False)
#     created_at = db.Column(db.DateTime, default=datetime.utcnow)
#     expires_at = db.Column(db.DateTime, nullable=False)
#     used_at = db.Column(db.DateTime, nullable=True)
#     
#     def is_expired(self):
#         """检查验证码是否已过期"""
#         return datetime.utcnow() > self.expires_at
#     
#     def __repr__(self):
#         return f"<EmailVerification {self.email} {self.code_type}>"

class User(db.Model):
    """用户模型，存储用户基本信息"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # # 邮箱验证状态 # Removed email verification fields
    # is_email_verified = db.Column(db.Boolean, default=False)
    # email_verified_at = db.Column(db.DateTime, nullable=True)
    
    subscriptions = db.relationship('Subscription', backref='user', lazy=True)
    devices = db.relationship('Device', backref='user', lazy=True) # Added backref to User for Device
    
    def set_password(self, password):
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
    
    def check_password(self, password):
        return bcrypt.check_password_hash(self.password_hash, password)
    
    # def verify_email(self): # Removed verify_email method
    #     self.is_email_verified = True
    #     self.email_verified_at = datetime.utcnow()
    
    def get_active_subscription(self):
        """获取用户当前有效的订阅 (状态为 'active' 且未过期)"""
        return Subscription.query.filter(
            Subscription.user_id == self.id,
            Subscription.end_date > datetime.utcnow()
        ).first()

    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

    def __repr__(self):
        return f'<User {self.email}>'

class SubscriptionType(db.Model):
    """订阅类型模型，存储不同的订阅套餐信息"""
    __tablename__ = 'subscription_types'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(32), unique=False, nullable=True)  # MODIFIED: Made optional and non-unique
    name = db.Column(db.String(64), nullable=False)  # 显示名称，如月付会员、学生会员
    price = db.Column(db.Float, nullable=False, default=0)  # 价格
    days = db.Column(db.Integer, nullable=False, default=30)  # 订阅有效期天数
    max_devices = db.Column(db.Integer, nullable=False, default=1)  # 用户通过此订阅类型可同时使用的最大设备数
    default_subscription_instance_capacity = db.Column(db.Integer, nullable=True) # 为此类型自动创建新订阅实例时，该订阅实例的默认用户容量
    description = db.Column(db.Text, nullable=True)  # 套餐描述
    is_public = db.Column(db.Boolean, default=True)  # 是否在购买页面公开显示
    requirements = db.Column(db.Text) # 适用条件，例如需要学生证 (原字段保留)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联: 一个订阅类型可以有多个订阅实例 (车次)
    subscription_instances = db.relationship('SubscriptionInstance', backref='subscription_type', lazy='dynamic')
    # 关联: 一个订阅类型可以对应多个用户的订阅记录
    subscriptions = db.relationship('Subscription', backref='subscription_type_details', lazy='dynamic', foreign_keys='Subscription.subscription_type_id', primaryjoin="SubscriptionType.id == Subscription.subscription_type_id")


    def __repr__(self):
        return f'<SubscriptionType ID: {self.id}, Name: {self.name}, Code: {self.code if self.code else "N/A"}>' # MODIFIED: Handle optional code

class SubscriptionInstance(db.Model):
    """订阅实例模型 (原ResourceGroup)，代表一个具体的"车次"或资源池"""
    __tablename__ = 'subscription_instances'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False) # 例如 "月付标准版-车次1"
    subscription_type_id = db.Column(db.Integer, db.ForeignKey('subscription_types.id'), nullable=False) # 原 subscription_type_code
    capacity = db.Column(db.Integer, nullable=False) # 此实例能容纳的最大用户数量
    is_active = db.Column(db.Boolean, default=True)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联: 一个订阅实例可以有多个AdsPower账号
    adspower_accounts = db.relationship('AdspowerAccount', backref='subscription_instance', lazy='dynamic')
    # 关联: 一个订阅实例可以被多个用户的订阅记录所使用
    user_subscriptions = db.relationship('Subscription', backref='subscription_instance', lazy='dynamic')
    
    # subscription_type (backref from SubscriptionType)

    def get_available_slots(self):
        """计算此订阅实例剩余的可用用户槽位数（只计算未过期的订阅）"""
        if not self.capacity: # 如果容量未设置或为0，则认为没有可用槽位
            return 0
        # 只计算未过期的用户订阅
        current_time = datetime.utcnow()
        active_users_count = self.user_subscriptions.filter(
            Subscription.end_date > current_time
        ).count()
        return self.capacity - active_users_count

    def get_least_used_adspower_account(self):
        """查找此实例下当前设备连接数最少且没有活跃登录会话的AdspowerAccount"""
        active_accounts = self.adspower_accounts.filter_by(is_active=True).all()
        if not active_accounts:
            return None

        min_devices_count = float('inf')
        target_account = None
        current_time = datetime.utcnow()

        for acc in active_accounts:
            # 只计算来自未过期订阅的用户的设备
            current_devices_on_acc = Device.query.join(User).join(Subscription).filter(
                Device.adspower_account_id == acc.id,
                Subscription.end_date > current_time
            ).count()
            
            # 检查是否超过设备限制
            if current_devices_on_acc >= acc.max_devices:
                continue
            
            # 检查是否有活跃的登录会话
            active_session_count = LoginSession.query.filter(
                LoginSession.adspower_account_id == acc.id,
                LoginSession.completed_time.is_(None),
                LoginSession.expiration_timestamp > datetime.utcnow()
            ).filter(
                db.or_(
                    LoginSession.login_status == 'pending',
                    LoginSession.login_status == 'active',
                    LoginSession.login_status.is_(None)  # 兼容旧数据
                )
            ).count()
            
            # 如果有活跃会话，跳过此账号
            if active_session_count > 0:
                logger.warning(f"账号 {acc.username} (ID: {acc.id}) 有 {active_session_count} 个活跃会话，跳过")
                continue
            
            # 找设备数最少的账号
            if current_devices_on_acc < min_devices_count:
                min_devices_count = current_devices_on_acc
                target_account = acc

        return target_account
    
    def get_active_users_count(self):
        """获取此订阅实例的活跃用户数（不包括过期订阅）"""
        current_time = datetime.utcnow()
        return self.user_subscriptions.filter(
            Subscription.end_date > current_time
        ).count()
    
    def get_expired_users_count(self):
        """获取此订阅实例的过期用户数"""
        current_time = datetime.utcnow()
        return self.user_subscriptions.filter(
            Subscription.end_date <= current_time
        ).count()

    def __repr__(self):
        return f'<SubscriptionInstance {self.name} (Type: {self.subscription_type_id}, Capacity: {self.capacity})>'

class Subscription(db.Model):
    """订阅模型，管理用户订阅状态"""
    __tablename__ = 'subscriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subscription_type_id = db.Column(db.Integer, db.ForeignKey('subscription_types.id'), nullable=False) # 原 subscription_type_code
    subscription_instance_id = db.Column(db.Integer, db.ForeignKey('subscription_instances.id'), nullable=False) # 用户被分配到的订阅实例ID
    
    payment_id = db.Column(db.Integer, db.ForeignKey('payments.id'), nullable=True)  # 支付ID (与Payment表关联)
    
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime, nullable=False)
    max_devices = db.Column(db.Integer, default=1, nullable=False)  # 购买时确定的最大设备数（历史记录）
    # status = db.Column(db.String(32), default='active', nullable=False) # active, expired, cancelled

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # user (backref from User)
    # subscription_instance (backref from SubscriptionInstance)
    # subscription_type_details (backref from SubscriptionType)
    # payment (relationship to Payment table)
    payment = db.relationship('Payment', backref=db.backref('subscription', uselist=False))


    def is_expired(self):
        """检查订阅是否已过期"""
        return datetime.utcnow() > self.end_date
    
    @property
    def price(self):
        """通过关联的支付记录获取价格"""
        if self.payment:
            return self.payment.amount
        return None
    
    @property
    def is_paid(self):
        """判断订阅是否通过支付创建"""
        return self.payment_id is not None
    
    def extend(self, days):
        """延长订阅天数
        
        Args:
            days: 要延长的天数
            
        Returns:
            新的到期日期
        """
        # 如果订阅已过期，从当前时间开始计算
        if self.is_expired():
            self.start_date = datetime.utcnow()
            self.end_date = self.start_date + timedelta(days=days)
        else:
            # 如果订阅未过期，从原到期日开始延长
            self.end_date = self.end_date + timedelta(days=days)
        
        return self.end_date
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'subscription_type_id': self.subscription_type_id,
            'subscription_instance_id': self.subscription_instance_id,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'payment_id': self.payment_id,
            'price': self.price,
            'max_devices': self.max_devices,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        status_display = "有效" if not self.is_expired() else "已过期" # Renamed for clarity
        # Attempt to get plan name from relationship, fallback to type_id
        plan_display = f"TypeID: {self.subscription_type_id}"
        if self.subscription_type_details: # Assuming subscription_type_details is the backref to SubscriptionType
            plan_display = self.subscription_type_details.name
        
        return f'<Subscription #{self.id} (Plan: {plan_display}, User: {self.user_id}) - {status_display}>'

class AdspowerAccount(db.Model):
    """ADSpower账号模型，存储ADSpower账号信息"""
    __tablename__ = 'adspower_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), nullable=False)  # Removed unique=True
    password = db.Column(db.String(128), nullable=False) # 应加密存储
    totp_secret = db.Column(db.String(128), nullable=True)  # 2FA密钥
    
    subscription_instance_id = db.Column(db.Integer, db.ForeignKey('subscription_instances.id', name='fk_adspower_accounts_subscription_instance_id'), nullable=True) # 所属订阅实例ID
    
    is_active = db.Column(db.Boolean, default=True) # 此AdsPower账号本身是否可用
    max_devices = db.Column(db.Integer, default=9, nullable=False)  # 此AdsPower账号允许的最大设备登录数 (固定为9)
    cookies = db.Column(db.Text, nullable=True) # 存储Cookies JSON字符串
    description = db.Column(db.Text, nullable=True) # 描述信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) # Added updated_at
    
    # 关联的订阅类型 (此字段可能需要重新评估，因为账号现在属于SubscriptionInstance)
    # subscription_type = db.Column(db.String(32))  # monthly, student, trial, basic等 
    #  ^^^ Consider removing or finding a new purpose if SubscriptionInstance handles typing/grouping sufficiently.

    # subscription_instance (backref from SubscriptionInstance)
    devices = db.relationship('Device', backref='adspower_account', lazy='dynamic') # One AdspowerAccount to many Devices

    __table_args__ = (db.UniqueConstraint('username', name='uq_adspower_accounts_username'),
                     )

    def __repr__(self):
        return f'<AdspowerAccount {self.username} (Instance: {self.subscription_instance_id})>'

    def has_capacity(self):
        """检查是否还有设备容量 (只计算来自未过期订阅的设备)"""
        from .models import Device, User, Subscription # Local import to avoid circular dependency at top level
        current_time = datetime.utcnow()
        current_devices_count = Device.query.join(User).join(Subscription).filter(
            Device.adspower_account_id == self.id,
            Subscription.end_date > current_time
        ).count()
        return current_devices_count < self.max_devices

class Device(db.Model):
    """设备模型，存储用户的设备信息"""
    __tablename__ = 'devices'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    adspower_account_id = db.Column(db.Integer, db.ForeignKey('adspower_accounts.id'), nullable=False) # 当前设备登录所使用的AdsPower账号ID
    
    device_id = db.Column(db.String(100), nullable=True, unique=True, index=True) # 设备唯一标识符 (来自AdsPower API)
    device_name = db.Column(db.String(100), nullable=True) # 设备名称 (例如 'John's Windows PC')
    device_ip = db.Column(db.String(50), nullable=True) # 设备注册时的IP
    device_type = db.Column(db.String(50), nullable=True) # 设备类型 (例如 'Windows', 'Mac', 'Android') - 根据 AdsPower 解析
    # status = db.Column(db.String(20), default='active', nullable=False) # 设备状态: active, inactive, expired
    created_at = db.Column(db.DateTime, default=datetime.utcnow) # 创建时间
    # extra_info = db.Column(Text, nullable=True) # 存储JSON格式的额外信息
    
    # user (backref from User)
    # adspower_account (backref from AdspowerAccount)
    
    def __repr__(self):
        return f'<Device {self.device_name or self.id} (User: {self.user_id}, AdspowerAccount: {self.adspower_account_id})>'

    # def set_extra_info(self, info_dict):
    #     \"\"\"将字典序列化为JSON并存储\"\"\"
    #     try:
    #         self.extra_info = json.dumps(info_dict)
    #     except TypeError as e:
    #         logger.error(f"序列化设备额外信息失败 (Device ID: {self.device_id}): {e}")
    #         self.extra_info = "{}" # 存入空JSON对象以避免None

    # def get_extra_info(self):
    #     \"\"\"从JSON字符串反序列化为字典\"\"\"
    #     if not self.extra_info:
    #         return {}
    #     try:
    #         return json.loads(self.extra_info)
    #     except json.JSONDecodeError as e:
    #         logger.error(f"反序列化设备额外信息失败 (Device ID: {self.device_id}): {e}")
    #         return {} # 返回空字典以避免错误

class PaymentRecord(db.Model): # This seems to be an older/alternative Payment model. Consolidate if possible.
    """支付记录"""
    __tablename__ = 'payment_records' # Added __tablename__
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # 支付金额
    payment_method = db.Column(db.String(50))  # 支付方式：易支付等
    transaction_id = db.Column(db.String(128))  # 交易号
    payment_status = db.Column(db.String(20))  # 支付状态
    payment_time = db.Column(db.DateTime)  # 支付时间
    subscription_days = db.Column(db.Integer)  # 购买的订阅天数
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联
    user = db.relationship('User', backref='payment_records') # Ensure User model has this backref if needed elsewhere
    
    def __repr__(self):
        return f'<PaymentRecord {self.id} User:{self.user_id} Amount:{self.amount}>'

class LoginSession(db.Model):
    """登录会话记录"""
    __tablename__ = 'login_sessions' # Added __tablename__
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    adspower_account_id = db.Column(db.Integer, db.ForeignKey('adspower_accounts.id'), nullable=True) # Made nullable as session might be for user login not AdsPower
    login_token = db.Column(db.String(128), unique=True, nullable=False, index=True)
    login_time = db.Column(db.DateTime, default=datetime.utcnow)
    logout_time = db.Column(db.DateTime, nullable=True)
    completed_time = db.Column(db.DateTime, nullable=True)  # 登录完成时间
    ip_address = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    login_status = db.Column(db.String(20), nullable=True)  # pending, active, completed, expired
    device_info = db.Column(db.Text, nullable=True)  # 设备信息，JSON格式
    session_duration_seconds = db.Column(db.Integer, default=180)  # 会话有效期（秒）
    # initial_devices_count = db.Column(db.Integer, nullable=True)  # 初始设备数量，用于检测登录成功
    # initial_devices_info = db.Column(db.Text, nullable=True)  # 存储初始设备信息的JSON字符串
    # known_devices = db.Column(db.Text) # Storing list of known device IDs/hashes as JSON string
    expiration_timestamp = db.Column(db.DateTime, nullable=False)
    known_devices_snapshot = db.Column(db.Text, nullable=True) # <--- 添加此行: 存储本次登录 *开始时* 获取的设备列表 JSON 快照
    
    # 关联
    user = db.relationship('User', backref='login_sessions', lazy=True)
    adspower_account = db.relationship('AdspowerAccount', backref='login_sessions', lazy=True)
    
    def get_remaining_seconds(self):
        """获取会话剩余有效时间（秒）"""
        if self.login_status == 'completed' or self.login_status == 'expired' or self.completed_time:
            return 0
            
        elapsed = (datetime.utcnow() - self.login_time).total_seconds()
        remaining = self.session_duration_seconds - int(elapsed)
        
        # 如果会话已经过期但状态未更新
        if remaining <= 0 or datetime.utcnow() > self.expiration_timestamp:
            # self.login_status = 'expired' # Status update should be handled by a dedicated process or when checked
            return 0
            
        return remaining
    
    def __repr__(self):
        return f'<LoginSession {self.id} User:{self.user_id}>'

class Payment(db.Model):
    """支付记录模型，存储用户的支付信息"""
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    order_id = db.Column(db.String(64), nullable=False, unique=True)  # 系统内部订单号
    payment_id = db.Column(db.String(64), nullable=True, unique=True)  # 支付平台ID (Made nullable as it might not exist before payment initiation)
    
    amount = db.Column(db.Float, nullable=False)  # 支付金额
    currency = db.Column(db.String(10), default='CNY')  # 货币类型
    payment_method = db.Column(db.String(32), nullable=True)  # 支付方式：epay等
    status = db.Column(db.String(32), default='pending')  # pending, paid, cancelled, refunded, error
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    paid_at = db.Column(db.DateTime, nullable=True)
    
    # subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=True)  # 关联的订阅ID (A payment creates/extends a subscription)
    # ^^ This creates a circular dependency with Subscription.payment_id. Use backref from Subscription.

    subscription_type_id = db.Column(db.Integer, db.ForeignKey('subscription_types.id'), nullable=True) # 新增: 关联到 SubscriptionType 的 ID
    transaction_id = db.Column(db.String(128), nullable=True)  # 第三方支付交易号
    subscription_days = db.Column(db.Integer, nullable=True)  # 购买的订阅天数 (e.g. from SubscriptionType)
    remarks = db.Column(db.Text, nullable=True)
    
    # 与用户的关联
    user = db.relationship('User', backref='payments')
    # subscription (backref from Subscription model)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'order_id': self.order_id,
            'payment_id': self.payment_id,
            'amount': self.amount,
            'currency': self.currency,
            'payment_method': self.payment_method,
            'status': self.status,
            'subscription_type_id': self.subscription_type_id,
            'transaction_id': self.transaction_id,
            'subscription_days': self.subscription_days,
            'remarks': self.remarks,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'paid_at': self.paid_at.isoformat() if self.paid_at else None
        }
    
    def __repr__(self):
        return f'<Payment {self.order_id} - {self.status}>'

class DeviceAudit(db.Model):
    """设备审计记录表，用于追踪设备操作历史和防止滥用"""
    __tablename__ = 'device_audits'
    
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id', ondelete='SET NULL'), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 操作者ID，可能是管理员
    
    # 审计信息
    action = db.Column(db.String(50), nullable=False)  # 操作类型：register, delete, login, logout, verify_failed等
    action_source = db.Column(db.String(50), default='user')  # 操作来源：user（用户手动）, admin（管理员）, system（系统自动）
    description = db.Column(db.Text)  # 操作描述
    ip_address = db.Column(db.String(50))  # 操作IP地址
    user_agent = db.Column(db.Text)  # 浏览器信息
    
    # 设备快照信息（设备删除后仍可查看）
    device_snapshot = db.Column(db.JSON)  # 设备信息快照：device_id, device_name, device_type, adspower_account等
    
    # 变更记录
    old_value = db.Column(db.JSON)  # 修改前的值
    new_value = db.Column(db.JSON)  # 修改后的值
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    device = db.relationship('Device', foreign_keys=[device_id])
    user = db.relationship('User', foreign_keys=[user_id], backref='device_audits')
    operator = db.relationship('User', foreign_keys=[operator_id])
    
    def __repr__(self):
        return f'<DeviceAudit {self.id} - {self.action} by User:{self.user_id}>'

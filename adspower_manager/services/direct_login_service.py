import logging
import secrets
import json
from datetime import datetime, timedelta

from flask import url_for, request

from ..models import db, User, AdspowerAccount, LoginSession, Subscription, Device, SubscriptionInstance
from sqlalchemy import or_ as db_or_
from ..webdriver_pool import get_account_driver_manager, AccountWebDriverManager
from .auth_service import AuthService # Need AuthService to check subscriptions
from ..adspower_api import get_adspower_api

logger = logging.getLogger(__name__)

class DirectLoginService:
    """
    处理准备AdsPower账户直接登录会话的过程。
    职责包括：
    - 检查用户订阅状态。
    - 根据订阅查找合适的、可用的AdsPower账户。
    - 验证所选AdsPower账户实例的实时可用性。
    - 创建临时的LoginSession记录。
    """

    def __init__(self):
        self.auth_service = AuthService()
        self.driver_manager = get_account_driver_manager()

    def prepare_login(self, user_id):
        """
        为指定用户准备直接登录会话。

        Args:
            user_id (int): 请求登录的用户ID。

        Returns:
            dict: 包含结果的字典:
                  {
                      'success': bool,
                      'message': str,
                      'error_code': str | None,
                      'data': { ... 登录详情 ... } | None
                  }
        """
        logger.info(f"[直接登录服务] 正在为用户ID: {user_id} 准备登录会话")

        # 1. 检查用户订阅
        subscription = self.auth_service.get_user_subscription(user_id)
        if not subscription:
            logger.warning(f"[直接登录服务] 用户 {user_id} 无有效订阅.")
            return {
                'success': False,
                'message': '您没有有效的订阅',
                'error_code': 'no_subscription',
                'data': None
            }
        logger.info(f"[直接登录服务] 用户 {user_id} 拥有有效订阅: ID={subscription.id}, 类型ID={subscription.subscription_type_id}, 实例ID={subscription.subscription_instance_id}")

        # 1.1 检查用户设备限制
        active_device_count = Device.query.filter_by(user_id=user_id).count()
        if active_device_count >= subscription.max_devices:
            logger.warning(f"[直接登录服务] 用户 {user_id} 已达到设备上限 ({active_device_count}/{subscription.max_devices}). 拒绝访问.")
            return {
                'success': False,
                'message': f'您已达到设备数量上限 ({subscription.max_devices}台)，请在"设备管理"中登出不再使用的设备。',
                'error_code': 'DEVICE_LIMIT_REACHED',
                'data': None
            }
        logger.info(f"[直接登录服务] 用户 {user_id} 设备数量检查通过 ({active_device_count}/{subscription.max_devices}). 继续操作...")

        # 2. 根据订阅的 instance_id 获取 SubscriptionInstance
        if not subscription.subscription_instance_id:
            logger.error(f"[直接登录服务] 用户 {user_id} 的有效订阅 {subscription.id} 没有关联到任何订阅实例 (subscription_instance_id is None)。")
            return {
                'success': False,
                'message': '您的订阅信息异常，未关联到资源组，请联系客服。',
                'error_code': 'subscription_not_linked_to_instance',
                'data': None
            }
        
        sub_instance = SubscriptionInstance.query.filter_by(id=subscription.subscription_instance_id).first()
        if not sub_instance:
            logger.error(f"[直接登录服务] 用户 {user_id} 的订阅 {subscription.id} 关联的实例ID {subscription.subscription_instance_id} 未在SubscriptionInstance表中找到。")
            return {
                'success': False,
                'message': '您的订阅关联的资源组信息不存在，请联系客服。',
                'error_code': 'instance_not_found',
                'data': None
            }
        
        if not sub_instance.is_active:
            logger.warning(f"[直接登录服务] 用户 {user_id} 订阅关联的实例 '{sub_instance.name}' (ID: {sub_instance.id}) 当前未激活。")
            return {
                'success': False,
                'message': f"您所在的资源组 '{sub_instance.name}' 当前不可用，请稍后重试或联系客服。",
                'error_code': 'instance_not_active',
                'data': None
            }
        
        logger.info(f"[直接登录服务] 用户 {user_id} 订阅关联到实例: '{sub_instance.name}' (ID: {sub_instance.id})")

        # 3. 从该实例中获取设备数最少的可用AdsPower账户
        suitable_adspower_account = sub_instance.get_least_used_adspower_account()
        
        if not suitable_adspower_account:
            # 检查是否有账号但都被占用
            total_accounts = sub_instance.adspower_accounts.filter_by(is_active=True).count()
            if total_accounts > 0:
                # 有账号但都被占用或已满
                logger.warning(f"[直接登录服务] 实例 '{sub_instance.name}' (ID: {sub_instance.id}) 中有 {total_accounts} 个账号，但都被占用或已满。用户ID: {user_id}")
                return {
                    'success': False,
                    'message': f"您所在的资源组 '{sub_instance.name}' 的所有账号都在使用中，请稍等片刻后再试。",
                    'error_code': 'all_accounts_busy',
                    'data': None
                }
            else:
                # 真的没有账号
                logger.warning(f"[直接登录服务] 实例 '{sub_instance.name}' (ID: {sub_instance.id}) 中没有配置任何AdsPower账户。用户ID: {user_id}")
                return {
                    'success': False,
                    'message': f"您所在的资源组 '{sub_instance.name}' 当前暂无可用账号资源，请联系客服。",
                    'error_code': 'no_adspower_account_in_instance',
                    'data': None
                }
        
        logger.info(f"[直接登录服务] 从实例 '{sub_instance.name}' 中为用户 {user_id} 选择的AdsPower账户: {suitable_adspower_account.username} (ID: {suitable_adspower_account.id})")

        # --- 后续逻辑基于选定的 suitable_adspower_account 进行 ---
        # suitable_adspower_account 已经是我们需要的账户了，不需要再遍历 candidate_accounts

        initial_devices_for_selected_account = None # 用于存储选定账户的设备信息
        adspower_api = get_adspower_api() # 获取API实例

        account_id_str_loop = str(suitable_adspower_account.id)
        log_prefix_loop = f"[直接登录服务] 用户ID: {user_id}, 检查选定账户: {suitable_adspower_account.username} (ID: {account_id_str_loop}) (来自实例 '{sub_instance.name}')"

        # 3.1 数据库设备数量再次确认 (get_least_used_adspower_account 内部已检查, 但这里可以做个最终防御性检查)
        db_device_count = Device.query.filter_by(adspower_account_id=suitable_adspower_account.id).count()
        logger.info(f"{log_prefix_loop} 数据库记录设备数: {db_device_count}, 账户上限: {suitable_adspower_account.max_devices}")
        if db_device_count >= suitable_adspower_account.max_devices:
            logger.error(f"{log_prefix_loop} 严重错误：get_least_used_adspower_account 返回了一个数据库记录设备数已达上限的账户。这不应该发生。")
            # 这种情况通常表示 get_least_used_adspower_account 的逻辑有缺陷或数据不一致
            return {
                'success': False,
                'message': f"分配账号时发生内部错误，请稍后重试或联系客服。 (Error Code: DLS-DB-COUNT-FAIL)",
                'error_code': 'internal_allocation_error_db_count',
                'data': None
            }

        # 3.1.5 检查是否有活跃的登录会话占用此账号
        active_sessions = LoginSession.query.filter(
            LoginSession.adspower_account_id == suitable_adspower_account.id,
            LoginSession.completed_time.is_(None),
            LoginSession.expiration_timestamp > datetime.utcnow()
        ).filter(
            db_or_(
                LoginSession.login_status == 'pending',
                LoginSession.login_status == 'active',
                LoginSession.login_status.is_(None)  # 兼容没有设置状态的旧数据
            )
        ).first()
        
        if active_sessions:
            logger.warning(f"{log_prefix_loop} 账号 {suitable_adspower_account.username} 有活跃的登录会话 (ID: {active_sessions.id})，无法分配给新用户")
            return {
                'success': False,
                'message': f"账号 '{suitable_adspower_account.username}' 正在被其他用户使用，请稍后再试",
                'error_code': 'account_in_use',
                'data': None
            }
        
        # 3.2 API 实时设备数量检查 (同时获取设备信息) - 这一步仍然重要，因为可能在上次检查后又有新设备连上
        logger.info(f"{log_prefix_loop} 正在通过API检查实时设备数并获取设备信息...")
        devices_list_from_api = None
        try:
            devices_list_from_api = adspower_api.get_devices_info(suitable_adspower_account)
            
            if devices_list_from_api is None: 
                logger.warning(f"{log_prefix_loop} API获取设备列表失败 (返回None)，无法确认实时设备数。将拒绝本次登录尝试。")
                # 考虑到用户体验和资源准确性，如果无法获取实时列表，则不应继续
                return {
                    'success': False,
                    'message': f"暂时无法连接到账号 '{suitable_adspower_account.username}' 以获取实时设备信息，请稍后再试。",
                    'error_code': 'api_get_devices_failed_at_login',
                    'data': None
                }

            real_time_device_count_from_api = len(devices_list_from_api)
            logger.info(f"{log_prefix_loop} API实时设备数: {real_time_device_count_from_api}, 账户上限: {suitable_adspower_account.max_devices}")
            
            if real_time_device_count_from_api >= suitable_adspower_account.max_devices:
                logger.warning(f"{log_prefix_loop} API实时设备数已达上限 ({real_time_device_count_from_api}/{suitable_adspower_account.max_devices})。这可能意味着在选择账户后，又有新设备连上。")
                # 即使 get_least_used_adspower_account 找到了一个看似可用的账号，
                # 但在实际分配前的瞬间，该账号的实时设备数可能又满了。
                return {
                    'success': False,
                    'message': f"您选择的账号 '{suitable_adspower_account.username}' 当前已满员，请稍后再试。",
                    'error_code': 'account_full_at_final_check',
                    'data': None
                }
        except Exception as api_e:
            logger.error(f"{log_prefix_loop} 调用API检查实时设备数时发生错误: {api_e}，将拒绝本次登录。", exc_info=True)
            return {
                'success': False,
                'message': f"连接到账号 '{suitable_adspower_account.username}' 获取设备信息时发生错误，请稍后再试。",
                'error_code': 'api_check_exception_at_login',
                'data': None
            }

        # 如果所有检查都通过，选定此账户并保存其设备信息
        initial_devices_for_selected_account = devices_list_from_api # 保存这次API调用的结果
        logger.info(f"{log_prefix_loop} 账户检查通过，已缓存其设备信息。")
        # 调试：打印设备信息
        logger.info(f"[DEBUG] 缓存的设备数量: {len(devices_list_from_api) if devices_list_from_api else 0}")
        if devices_list_from_api:
            for idx, dev in enumerate(devices_list_from_api):
                logger.info(f"[DEBUG] 缓存设备 {idx+1}: {json.dumps(dev, ensure_ascii=False)}")
        
        # 将 suitable_adspower_account 赋值给 adspower_account 以便后续代码使用
        adspower_account = suitable_adspower_account # adspower_account 是后续流程中使用的变量名

        # 注释掉原有的账户选择循环，因为我们已经通过 SubscriptionInstance 找到了特定账户
        # if not adspower_account:
        #     logger.warning(f"[直接登录服务] 未找到订阅类型为 '{subscription_type}' 且设备未满的可用AdsPower账户 (用户ID: {user_id})")
        #     return {
        #         'success': False,
        #         'message': f"暂时没有可用的 '{subscription_type}' 类型账号(设备已满或检查失败)，请稍后再试",
        #         'error_code': 'no_account_available_or_full',
        #         'data': None
        #     }
        
        account_id_str = str(adspower_account.id)
        log_prefix = f"[直接登录服务] 用户ID: {user_id}, 最终选定账户: {adspower_account.username} (ID: {account_id_str}) via instance '{sub_instance.name}'"
        logger.info(f"{log_prefix} 准备进行登录会话创建。")

        # 3. 使用之前保存的设备快照 (现在是步骤4)
        logger.info(f"{log_prefix} 使用已缓存的设备信息作为初始快照...")
        # adspower_api = get_adspower_api() # 不再需要在这里获取API实例
        initial_devices = initial_devices_for_selected_account # 直接使用缓存的设备信息
        initial_devices_snapshot_json = None # 初始化

        try:
            # initial_devices 在账户选择阶段就已经被赋值，这里直接判断和序列化
            if initial_devices is None: # 理论上如果账户被选中，这里不应是None
                logger.error(f"{log_prefix} 选定账户的初始设备快照为空 (initial_devices is None)，这不应发生。可能获取状态失败。")
                return {
                    'success': False,
                    'message': f"所选账户 ({adspower_account.username}) 获取状态失败，请稍后重试。",
                    'error_code': 'account_snapshot_missing_after_selection',
                    'data': None
                }
            elif not initial_devices: # 空列表
                logger.info(f"{log_prefix} 成功获取初始设备快照: 发现0个设备. 继续登录流程.")
                initial_devices_snapshot_json = json.dumps([])
            else: # 有设备
                initial_devices_snapshot_json = json.dumps(initial_devices)
                logger.info(f"{log_prefix} 成功获取初始设备快照 (设备数量: {len(initial_devices)}).")
                # 调试：打印快照内容
                logger.info(f"[DEBUG] 保存的快照内容: {initial_devices_snapshot_json}")

        except Exception as e:
            logger.error(f"{log_prefix} 处理初始设备快照时发生意外错误: {e}", exc_info=True)
            return {
                'success': False,
                'message': "获取初始设备快照时发生意外错误",
                'error_code': 'snapshot_processing_error',
                'data': None
            }

        # 4. Create LoginSession (Continue only if initial_devices was not None)
        logger.info(f"{log_prefix} 正在创建LoginSession...")
        try:
            login_token = secrets.token_urlsafe(32)
            client_ip = request.remote_addr if request else None
            user_agent_string = request.user_agent.string if request and request.user_agent else None
            expiration_time = datetime.utcnow() + timedelta(seconds=180)

            login_session = LoginSession(
                user_id=user_id,
                adspower_account_id=adspower_account.id,
                login_token=login_token,
                login_status='pending',  # 明确设置初始状态
                expiration_timestamp=expiration_time,
                ip_address=client_ip,
                user_agent=user_agent_string,
                known_devices_snapshot=initial_devices_snapshot_json
            )
            db.session.add(login_session)
            db.session.commit()
            logger.info(f"{log_prefix} LoginSession {login_session.id} created successfully.")

            # 5. Prepare response data (重新生成 login_url)
            login_page_url = None
            try:
                login_page_url = url_for(
                    'main.adspower_direct_login',
                    token=login_token,
                    username=adspower_account.username,
                    password=adspower_account.password,
                    account_id=adspower_account.id,
                    expires=expiration_time.isoformat() + "Z",
                    _external=True
                )
                logger.info(f"{log_prefix} 生成的登录URL (已移除TOTP密钥): {login_page_url}")
            except RuntimeError as url_e:
                logger.error(f"{log_prefix} Failed to generate login_url using url_for: {url_e}. Returning data without URL.", exc_info=True)

            return {
                'success': True,
                'message': '获取登录信息成功',
                'error_code': None,
                'data': {
                    'login_token': login_token,
                    'username': adspower_account.username,
                    'password': adspower_account.password,
                    'account_id': adspower_account.id,
                    'expiration_timestamp_iso': expiration_time.isoformat() + "Z",
                    'login_url': login_page_url
                }
            }
        except Exception as e:
            logger.exception(f"{log_prefix} Error creating LoginSession or preparing response: {e}")
            try:
                db.session.rollback()
            except Exception as rb_err:
                logger.error(f"{log_prefix} Database rollback failed after LoginSession creation error: {rb_err}", exc_info=True)
            return {
                'success': False,
                'message': "创建登录会话时发生内部错误",
                'error_code': 'session_creation_failed',
                'data': None
            }

# --- Helper to get the service instance ---
# (Consider using Flask app context patterns for better dependency management later)
_direct_login_service = None

def get_direct_login_service():
    global _direct_login_service
    if _direct_login_service is None:
        _direct_login_service = DirectLoginService()
    return _direct_login_service 
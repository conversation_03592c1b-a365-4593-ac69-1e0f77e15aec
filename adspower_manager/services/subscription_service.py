from datetime import datetime, timedelta
import logging
import random
import string
# 使用相对导入从上级目录导入 models
from ..models import db, User, Subscription, Device, AdspowerAccount, SubscriptionType, SubscriptionInstance

# 配置日志
logger = logging.getLogger(__name__)

class SubscriptionService:
    """订阅服务类，处理用户订阅的创建、更新和管理"""

    @classmethod
    def allocate_adspower_account(cls, user_id, subscription_id):
        """为用户分配ADSpower账号 (示例逻辑，可能需要根据实际情况调整)

        Args:
            user_id: 用户ID
            subscription_id: 订阅ID

        Returns:
            是否分配成功
        """
        try:
            # 查询可用的ADSpower账号
            available_account = AdspowerAccount.query.filter(
                AdspowerAccount.is_active == True,
                AdspowerAccount.current_devices < AdspowerAccount.max_devices
            ).order_by(
                AdspowerAccount.current_devices  # 优先选择设备数较少的账号
            ).first()

            if not available_account:
                logger.error("[资源不足] ADSpower账号已全部分配 - 影响:无法为新用户提供服务, 建议:增加账号或清理失效账号")
                return False

            # 创建默认设备 (这部分逻辑可能需要根据新流程调整或移除)
            # device = Device(
            #     user_id=user_id,
            #     adspower_account_id=available_account.id,
            #     device_name=f"Default_Device_{user_id}",
            #     device_ip="0.0.0.0",
            #     status="inactive",
            #     created_at=datetime.utcnow()
            # )
            # db.session.add(device)

            # 更新ADSpower账号设备计数 (如果需要的话)
            # available_account.current_devices += 1

            # db.session.commit()
            logger.info(f"[账号分配成功] 用户ID:{user_id}, ADSpower账号ID:{available_account.id}, 该账号当前设备数:{available_account.current_devices}/{available_account.max_devices}")
            # 需要将账号信息关联到用户或订阅，例如 UserAdspowerAccount
            return True

        except Exception as e:
            logger.error(f"分配ADSpower账号时出错: {e}", exc_info=True)
            db.session.rollback()
            return False

    @classmethod
    def has_used_trial(cls, user_id):
        """检查用户是否已使用过试用订阅

        Args:
            user_id: 用户ID

        Returns:
            是否使用过试用
        """
        trial = Subscription.query.filter_by(
            user_id=user_id,
            is_trial=True
        ).first()

        return trial is not None

    @classmethod
    def get_active_subscription(cls, user_id):
        """获取用户的活跃订阅

        Args:
            user_id: 用户ID

        Returns:
            活跃的Subscription对象或None
        """
        active_sub = Subscription.query.filter(
            Subscription.user_id == user_id,
            Subscription.end_date > datetime.utcnow()
        ).order_by(
            Subscription.end_date.desc()  # 获取结束时间最晚的订阅
        ).first()

        return active_sub

    @staticmethod
    def generate_instance_name(subscription_type, instance_count_for_type):
        """生成实例名称

        Args:
            subscription_type: SubscriptionType 对象
            instance_count_for_type: 该类型已有的实例计数

        Returns:
            实例名称字符串
        """
        # 移除形容词和名词的选择
        # adj = random.choice(ADJECTIVES)
        # noun = random.choice(NOUNS)
        unique_suffix = instance_count_for_type + 1
        # 使用套餐的显示名称作为前缀
        base_name = subscription_type.name
        new_instance_name = f"{base_name}{unique_suffix}号车" # 直接使用套餐名+序号+号车
        # 确保名称不重复
        while SubscriptionInstance.query.filter_by(name=new_instance_name).first():
            unique_suffix += 1
            new_instance_name = f"{base_name}{unique_suffix}号车"
        return new_instance_name

    @staticmethod
    def generate_instance_capacity(subscription_type):
        """获取实例容量

        Args:
            subscription_type: SubscriptionType 对象

        Returns:
            实例容量整数
        """
        capacity = subscription_type.default_subscription_instance_capacity
        if not capacity or capacity <= 0:
            logger.warning(f"订阅类型 '{subscription_type.name}' (ID: {subscription_type.id}) 的 default_subscription_instance_capacity 未设置或无效 ({capacity})。将默认设置为1。")
            return 1
        return capacity

    @staticmethod
    def create_or_extend_subscription(user_id, subscription_type_id, payment_id=None):
        """创建或延长订阅 (重构自 payment_service.py)

        Args:
            user_id: 用户ID
            subscription_type_id: 订阅计划类型ID
            payment_id: 支付ID

        Returns:
            (Subscription, message) 元组
        """
        try:
            # 获取订阅类型信息
            subscription_type = SubscriptionType.query.get(subscription_type_id)
            if not subscription_type:
                logger.error(f"[业务错误] 订阅类型ID:{subscription_type_id}不存在 - 影响:无法创建订阅")
                return None, f"找不到订阅类型ID: {subscription_type_id}"

            # 检查是否已有活跃订阅 (只检查结束日期大于当前的)
            existing_sub = Subscription.query.filter(
                Subscription.user_id == user_id,
                Subscription.end_date > datetime.utcnow()
            ).first()

            if existing_sub:
                # 如果已有相同类型的活跃订阅，则延长订阅
                if existing_sub.subscription_type_id == subscription_type.id: # Compare IDs now
                    new_end_date = existing_sub.extend(subscription_type.days)
                    existing_sub.payment_id = payment_id # 更新支付ID
                    existing_sub.updated_at = datetime.utcnow()
                    db.session.add(existing_sub) 
                    db.session.commit()
                    logger.info(f"[订阅续费成功] 用户ID:{user_id}, 订阅ID:{existing_sub.id}, 类型:{subscription_type.name}, 延长:{subscription_type.days}天, 新到期:{new_end_date}")
                    return existing_sub, "订阅已成功续期"
                else:
                    # 如果是不同类型的订阅，阻止创建新订阅
                    current_plan_name = f"TypeID: {existing_sub.subscription_type_id}"
                    try:
                        existing_plan_type = SubscriptionType.query.get(existing_sub.subscription_type_id)
                        if existing_plan_type:
                            current_plan_name = existing_plan_type.name
                    except Exception as e_plan_name:
                        logger.warning(f"获取用户现有套餐名称时出错: {e_plan_name}")
                    
                    logger.warning(f"[订阅冲突] 用户ID:{user_id}尝试购买:{subscription_type.name}(类型ID:{subscription_type_id}), 但已有:{current_plan_name}(类型ID:{existing_sub.subscription_type_id}), 决策:拒绝购买")
                    return None, f"您当前已有有效的订阅 '{current_plan_name}', 无法同时购买 '{subscription_type.name}'. 请等待当前订阅结束后再购买新的订阅，或联系客服处理。"

            # --- 如果是购买新订阅 (没有同类型的活跃订阅) ---
            assigned_instance_id = None
            # 1. 查找可用的 SubscriptionInstance
            available_instance = None
            active_instances = SubscriptionInstance.query.filter_by(
                subscription_type_id=subscription_type.id,
                is_active=True
            ).all()

            # 优先选择有adspower账号且人数最少的实例
            min_users_count = float('inf')
            instances_with_adspower = []
            instances_without_adspower = []
            
            for instance in active_instances:
                available_slots = instance.get_available_slots()
                if available_slots > 0:
                    current_users_count = instance.user_subscriptions.count()
                    has_adspower = instance.adspower_accounts.count() > 0
                    
                    if has_adspower:
                        instances_with_adspower.append((instance, current_users_count))
                        logger.info(f"找到有AdsPower账号的实例: ID={instance.id}, Name='{instance.name}', 当前用户数={current_users_count}, AdsPower账号数={instance.adspower_accounts.count()}, 可用槽位={available_slots}")
                    else:
                        instances_without_adspower.append((instance, current_users_count))
                        logger.info(f"找到无AdsPower账号的实例: ID={instance.id}, Name='{instance.name}', 当前用户数={current_users_count}, 可用槽位={available_slots}")
            
            # 优先从有adspower账号的实例中选择人数最少的
            if instances_with_adspower:
                instances_with_adspower.sort(key=lambda x: x[1])  # 按人数排序
                available_instance = instances_with_adspower[0][0]
                logger.info(f"优先选择有AdsPower账号且人数最少的实例: ID={available_instance.id}, Name='{available_instance.name}', 当前用户数={instances_with_adspower[0][1]}")
            elif instances_without_adspower:
                # 如果没有有adspower账号的实例，则从无账号的实例中选择人数最少的
                instances_without_adspower.sort(key=lambda x: x[1])  # 按人数排序
                available_instance = instances_without_adspower[0][0]
                logger.warning(f"没有找到有AdsPower账号的可用实例，选择无账号但人数最少的实例: ID={available_instance.id}, Name='{available_instance.name}', 当前用户数={instances_without_adspower[0][1]}")
            
            if available_instance:
                assigned_instance_id = available_instance.id
                logger.info(f"最终为用户 {user_id} 选择了人数最少的实例: ID={available_instance.id}, Name='{available_instance.name}', 当前用户数={available_instance.user_subscriptions.count()}")
            else:
                # 没有可用实例，创建新的 SubscriptionInstance
                logger.info(f"套餐 (类型ID: {subscription_type_id}) 无可用实例，为用户 {user_id} 创建新实例...")
                instance_count_for_type = SubscriptionInstance.query.filter_by(subscription_type_id=subscription_type.id).count()
                
                # 使用修正后的生成方法
                new_instance_name = SubscriptionService.generate_instance_name(subscription_type, instance_count_for_type)
                new_instance_capacity = SubscriptionService.generate_instance_capacity(subscription_type)
                
                # 简化描述信息
                # adj_for_desc = random.choice(ADJECTIVES) # For description, can be different from name parts
                # noun_for_desc = random.choice(NOUNS)
                # 生成序号，与名称中的序号保持一致，用于描述
                description_suffix = instance_count_for_type + 1
                # 确保描述中的序号与最终名称的序号一致，如果名称因重复而递增了序号
                temp_check_name = f"{subscription_type.name}{description_suffix}号车"
                while SubscriptionInstance.query.filter_by(name=temp_check_name).first():
                    description_suffix +=1
                    temp_check_name = f"{subscription_type.name}{description_suffix}号车"

                new_instance_description = f"{subscription_type.name} 套餐的第 {description_suffix} 号车队"

                new_instance = SubscriptionInstance(
                    name=new_instance_name,
                    subscription_type_id=subscription_type.id,
                    capacity=new_instance_capacity,
                    is_active=True,
                    description=new_instance_description # 使用简化后的描述
                )
                db.session.add(new_instance)
                db.session.flush() # 需要先 flush 获取 new_instance.id
                assigned_instance_id = new_instance.id
                logger.info(f"为用户 {user_id} 成功创建新实例: ID={new_instance.id}, Name='{new_instance_name}', Capacity={new_instance.capacity}")
                logger.warning(f"新创建的订阅实例 ID={new_instance.id} ('{new_instance_name}') 尚未关联任何AdsPower账号。请管理员及时配置。")

            if not assigned_instance_id:
                logger.error(f"未能为用户 {user_id} 的套餐 (类型ID: {subscription_type_id}) 分配或创建订阅实例。")
                return None, "无法分配到合适的资源组（车次），请联系管理员。"

            # 创建新订阅记录
            now = datetime.utcnow()
            new_subscription = Subscription(
                user_id=user_id,
                subscription_type_id=subscription_type.id,
                subscription_instance_id=assigned_instance_id, 
                start_date=now,
                end_date=now + timedelta(days=subscription_type.days),
                payment_id=payment_id,
                max_devices=subscription_type.max_devices,
                created_at=now,
                updated_at=now
            )

            db.session.add(new_subscription)
            db.session.commit()

            logger.info(f"已为用户 {user_id} 创建新订阅 {new_subscription.id} (套餐类型ID: {subscription_type_id}), 分配到实例 {assigned_instance_id}, 到期时间: {new_subscription.end_date}")
            return new_subscription, "新订阅创建成功"

        except Exception as e:
            logger.error(f"创建或延长订阅时出错: {e}", exc_info=True)
            db.session.rollback()
            return None, f"创建或延长订阅失败: {str(e)}"

    # 可以添加其他订阅相关的方法，如取消订阅、获取订阅历史等
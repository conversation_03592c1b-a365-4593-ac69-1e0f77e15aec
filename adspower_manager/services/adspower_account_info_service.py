"""
AdsPower账号信息聚合服务
负责聚合账号的数据库信息和运行时状态，提供统一的接口
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from ..models import AdspowerAccount, Device, db, User, Subscription
from ..webdriver_pool import get_account_driver_manager
from ..adspower_api import get_adspower_api

logger = logging.getLogger(__name__)


class AdsPowerAccountInfoService:
    """
    AdsPower账号信息聚合服务
    不存储状态，只负责从各个数据源聚合账号信息
    """
    
    @staticmethod
    def get_account_full_info(account_id: int) -> Optional[Dict[str, Any]]:
        """
        获取账号的完整信息，包括数据库信息和运行时状态
        
        Args:
            account_id: 账号ID
            
        Returns:
            包含完整账号信息的字典，如果账号不存在则返回None
        """
        # 1. 从数据库获取基本信息
        account = AdspowerAccount.query.get(account_id)
        if not account:
            logger.warning(f"账号 {account_id} 不存在")
            return None
        
        # 2. 获取设备数量（只计算来自未过期订阅的用户的设备）
        current_time = datetime.utcnow()
        device_count = Device.query.join(User).join(Subscription).filter(
            Device.adspower_account_id == account_id,
            Subscription.end_date > current_time
        ).count()
        
        # 获取过期订阅的设备数
        expired_device_count = Device.query.join(User).join(Subscription).filter(
            Device.adspower_account_id == account_id,
            Subscription.end_date <= current_time
        ).count()
        
        # 3. 从WebDriver管理器获取运行时状态
        runtime_status = AdsPowerAccountInfoService._get_runtime_status(account_id)
        
        # 4. 计算综合健康状态（传入过期设备数信息）
        account._expired_device_count = expired_device_count  # 临时存储过期设备数
        health_info = AdsPowerAccountInfoService._calculate_health_status(
            account, device_count, runtime_status
        )
        
        # 5. 聚合所有信息
        return {
            'id': account.id,
            'username': account.username,
            'is_active': account.is_active,
            'max_devices': account.max_devices,
            'current_devices': device_count,
            'expired_devices': expired_device_count,  # 过期订阅的设备数
            'total_devices': device_count + expired_device_count,  # 总设备数
            'created_at': account.created_at.isoformat() if account.created_at else None,
            'updated_at': account.updated_at.isoformat() if account.updated_at else None,
            'subscription_instance_id': account.subscription_instance_id,
            'cookies': account.cookies,  # 注意：实际使用时可能需要控制访问权限
            'description': account.description,
            
            # 运行时状态
            'runtime_status': runtime_status,
            
            # 综合健康状态
            'health_status': health_info['status'],
            'health_message': health_info['message'],
            'health_details': health_info['details']
        }
    
    @staticmethod
    def get_all_accounts_info() -> List[Dict[str, Any]]:
        """
        获取所有账号的信息列表
        
        Returns:
            包含所有账号信息的列表
        """
        accounts = AdspowerAccount.query.all()
        result = []
        
        for account in accounts:
            try:
                info = AdsPowerAccountInfoService.get_account_full_info(account.id)
                if info:
                    result.append(info)
            except Exception as e:
                logger.error(f"获取账号 {account.id} 信息时出错: {e}", exc_info=True)
                # 即使出错也返回基本信息
                result.append({
                    'id': account.id,
                    'username': account.username,
                    'is_active': account.is_active,
                    'health_status': 'error',
                    'health_message': f'获取信息失败: {str(e)}'
                })
        
        return result
    
    @staticmethod
    def get_account_summary(account_id: int) -> Optional[Dict[str, Any]]:
        """
        获取账号的摘要信息（轻量级，用于列表显示）
        
        Args:
            account_id: 账号ID
            
        Returns:
            账号摘要信息
        """
        account = AdspowerAccount.query.get(account_id)
        if not account:
            return None
        
        device_count = Device.query.filter_by(adspower_account_id=account_id).count()
        runtime_status = AdsPowerAccountInfoService._get_runtime_status(account_id)
        
        return {
            'id': account.id,
            'username': account.username,
            'is_active': account.is_active,
            'current_devices': device_count,
            'max_devices': account.max_devices,
            'health_status': runtime_status.get('health_status', 'unknown'),
            'health_message': runtime_status.get('health_message', ''),
            'webdriver_instances': runtime_status.get('total_instances', 0)
        }
    
    @staticmethod
    def _get_runtime_status(account_id: int) -> Dict[str, Any]:
        """
        获取运行时状态，使用协议模式获取健康状态
        
        Args:
            account_id: 账号ID
            
        Returns:
            运行时状态信息
        """
        try:
            # 获取账号对象
            account = AdspowerAccount.query.get(account_id)
            if not account:
                return {
                    'managed': False,
                    'health_status': 'unknown',
                    'health_message': '账号不存在',
                    'total_instances': 0,
                    'status_counts': {},
                    'ready_count': 0,
                    'in_use_count': 0,
                    'unhealthy_count': 0
                }
            
            # 如果账号被禁用，直接返回禁用状态，不进行健康检查
            if not account.is_active:
                return {
                    'managed': False,
                    'health_status': 'disabled',
                    'health_message': '账号已被禁用',
                    'total_instances': 0,
                    'status_counts': {'disabled': 1},
                    'ready_count': 0,
                    'in_use_count': 0,
                    'unhealthy_count': 0
                }
            
            # 使用协议模式获取健康状态
            api = get_adspower_api()
            is_healthy, status_msg, health_info = api.check_account_health(account)
            
            # 转换为统一的状态格式
            if is_healthy:
                health_status = 'healthy'
            elif '危险' in status_msg:
                health_status = 'danger'
            else:
                health_status = 'warning'
            
            return {
                'managed': True,  # 协议模式始终为“管理”状态
                'health_status': health_status,
                'health_message': status_msg,
                'total_instances': 1 if is_healthy else 0,  # 协议模式不使用实例概念
                'status_counts': {'healthy': 1} if is_healthy else {'unhealthy': 1},
                'ready_count': 1 if is_healthy else 0,
                'in_use_count': 0,
                'unhealthy_count': 0 if is_healthy else 1,
                'health_info': health_info  # 额外的健康信息
            }
        except Exception as e:
            logger.warning(f"获取账号 {account_id} 运行时状态失败: {e}")
            # 返回默认状态
            return {
                'managed': False,
                'health_status': 'unknown',
                'health_message': f'获取状态失败: {str(e)}',
                'total_instances': 0,
                'status_counts': {},
                'ready_count': 0,
                'in_use_count': 0,
                'unhealthy_count': 0
            }
    
    @staticmethod
    def _calculate_health_status(
        account: AdspowerAccount, 
        device_count: int, 
        runtime_status: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        根据多个因素计算账号的综合健康状态
        
        Args:
            account: 账号模型实例
            device_count: 当前设备数
            runtime_status: 运行时状态
            
        Returns:
            健康状态信息
        """
        issues = []
        warnings = []
        
        # 检查账号是否被禁用
        if not account.is_active:
            return {
                'status': 'disabled',
                'message': '账号已被禁用',
                'details': {'issues': ['账号已被禁用'], 'warnings': []}
            }
        
        # 检查协议模式的健康状态（只关注登录状态）
        if not runtime_status.get('managed'):
            warnings.append('账号状态不可用')
        else:
            # 使用协议模式的健康状态
            if runtime_status.get('health_status') == 'danger':
                issues.append(runtime_status.get('health_message', '账号状态危险'))
            elif runtime_status.get('health_status') == 'warning':
                warnings.append(runtime_status.get('health_message', '账号状态需要注意'))
            elif runtime_status.get('health_status') == 'unknown':
                warnings.append('账号健康状态未知')
        
        # 不检查设备容量 - 健康状态只反映账号登录状态
        
        # 确定最终状态
        if issues:
            status = 'unhealthy'
            message = '存在问题: ' + '; '.join(issues[:2])  # 只显示前两个问题
        elif warnings:
            status = 'warning'
            message = '需要注意: ' + '; '.join(warnings[:2])
        else:
            status = 'healthy'
            message = '状态良好'
        
        return {
            'status': status,
            'message': message,
            'details': {
                'issues': issues,
                'warnings': warnings,
                'device_usage': f'{device_count}/{account.max_devices}',
                'webdriver_status': runtime_status.get('health_status', 'unknown')
            }
        }
    
    @staticmethod
    def refresh_account_status(account_id: int) -> bool:
        """
        刷新账号状态（预留接口，可用于触发状态检查）
        
        Args:
            account_id: 账号ID
            
        Returns:
            是否刷新成功
        """
        try:
            # 这里可以添加主动刷新逻辑，比如：
            # - 触发WebDriver健康检查
            # - 更新Cookie有效性
            # - 检查设备连接状态等
            
            logger.info(f"刷新账号 {account_id} 状态")
            return True
        except Exception as e:
            logger.error(f"刷新账号 {account_id} 状态失败: {e}", exc_info=True)
            return False
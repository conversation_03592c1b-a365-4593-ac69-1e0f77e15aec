import logging
import re
import random
import string
import pyotp
import jwt
from datetime import datetime, timedelta
from flask import current_app, url_for
from sqlalchemy import func
from ..models import db, User, AdspowerAccount, Device, Subscription, LoginSession
from .device_audit_service import DeviceAuditService
# from config import (
#     JWT_SECRET_KEY as FALLBACK_JWT_SECRET_KEY,
#     JWT_ACCESS_TOKEN_EXPIRES, JWT_REFRESH_TOKEN_EXPIRES,
#     PASSWORD_MIN_LENGTH, PASSWORD_REQUIRE_SPECIAL_CHAR, PASSWORD_REQUIRE_NUMBER,
#     SECRET_KEY as FALLBACK_SECRET_KEY
# )
# from .email_service import EmailService
import secrets
import json

# logger = logging.getLogger(__name__)
# 配置日志，使用中文
logger = logging.getLogger(__name__)
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class AuthService:
    """认证服务，处理用户注册、登录等认证相关功能"""
    
    def __init__(self, app_context=None):
        """初始化认证服务
        
        Args:
            app_context: Flask app 实例
        """
        self._app_context = app_context
        # self.email_service = EmailService()
        # self.require_email_verification = REQUIRE_EMAIL_VERIFICATION
        
        # if app_context and hasattr(app_context, 'config'): # Commented out this block
        #     if hasattr(self.email_service, 'init_app') and callable(getattr(self.email_service, 'init_app')):
        #         self.email_service.init_app(app_context)
        #     logger.info(f"认证服务在应用上下文中初始化完成 (AuthService init with app_context: {app_context.name if app_context else 'N/A'})")
        # elif current_app:
        #     if hasattr(self.email_service, 'init_app') and callable(getattr(self.email_service, 'init_app')):
        #         self.email_service.init_app(current_app)
        #     logger.info(f"认证服务在 current_app 上下文中初始化完成 (AuthService init with current_app: {current_app.name})")
    
    def secret_key(self):
        if current_app:
            key = current_app.config.get('SECRET_KEY')
            if key:
                return key
        logger.error("[安全配置缺失] SECRET_KEY未配置 - 影响:会话安全性受损, 用户登录状态无法保存")
        return None # 或者 raise ConfigurationError("SECRET_KEY 未配置")

    @property
    def jwt_secret_key(self):
        if current_app:
            key = current_app.config.get('JWT_SECRET_KEY')
            if key:
                return key
            # 回退到 SECRET_KEY 如果 JWT_SECRET_KEY 未明确设置
            key = current_app.config.get('SECRET_KEY')
            if key:
                logger.warning("[安全配置警告] JWT_SECRET_KEY未设置 - 使用SECRET_KEY替代, 建议:明确设置JWT密钥以提高安全性")
                return key
        logger.error("[安全配置严重错误] JWT密钥完全缺失 - 影响:用户无法登录, API认证失效")
        return None # 或者 raise ConfigurationError("JWT_SECRET_KEY 或 SECRET_KEY 未配置")
    
    def init_app(self, app):
        """通过应用实例配置认证服务
        
        Args:
            app: Flask应用实例
        """
        self._app_context = app
        # if hasattr(self.email_service, 'init_app') and callable(getattr(self.email_service, 'init_app')): # Commented out this block
        #     self.email_service.init_app(app)
        # self.require_email_verification = app.config.get('REQUIRE_EMAIL_VERIFICATION', self.require_email_verification)
        logger.info(f"[认证服务初始化] 完成配置 - 应用:{app.name if app else 'N/A'}, 功能:用户登录/注册/JWT签发")
    
    # def validate_password(self, password):
    #     if len(password) < self.password_min_length:
    #         return False, f"密码长度不能少于{self.password_min_length}位"
    #     if self.password_complexity_regex and not re.match(self.password_complexity_regex, password):
    #         return False, "密码未满足复杂度要求（例如，需要包含大小写字母和数字）"
    #     return True, "密码有效"

    # def register_user(self, email, password, verification_code):
    #     with self.app.app_context(): # 确保在应用上下文中操作数据库和配置
    #         if User.query.filter_by(email=email).first():
    #             return None, "用户已存在"
        
    #         # 验证邮箱验证码
    #         email_service = self.email_service # 使用实例变量
    #         is_code_valid, code_message = email_service.verify_code(email, verification_code, 'register')
    #         if not is_code_valid:
    #             return None, code_message

    #         is_valid, message = self.validate_password(password)
    #         if not is_valid:
    #             return None, message

    #         hashed_password = generate_password_hash(password)
    #         new_user = User(email=email, password_hash=hashed_password, is_email_verified=True) # 注册时通过验证码，邮箱已验证
            
    #         # 检查是否有默认订阅类型
    #         default_sub_type = SubscriptionType.query.filter_by(is_default=True).first()
    #         if default_sub_type:
    #             # 创建新订阅
    #             new_subscription = Subscription(
    #                 user_id=None, # 将在用户创建后设置
    #                 subscription_type_id=default_sub_type.id,
    #                 start_date=datetime.utcnow(),
    #                 end_date=datetime.utcnow() + timedelta(days=default_sub_type.duration_days),
    #                 is_active=True,
    #                 payment_status='default_free' # 标记为默认免费
    #             )
    #             new_user.subscriptions.append(new_subscription) # 将订阅与用户关联
    #             logger.info(f"为新用户 {email} 分配了默认订阅: {default_sub_type.name}")
            
    #         db.session.add(new_user)
            
    #         try:
    #             db.session.commit()
    #             logger.info(f"用户 {email} 注册成功并通过邮箱验证。")
    #             # 此处可以选择是否发送欢迎邮件
    #             # self.email_service.send_welcome_email(email)
    #             return new_user, "注册成功，邮箱已验证。"
    #         except Exception as e:
    #             db.session.rollback()
    #             logger.error(f"注册用户 {email} 时发生数据库错误: {e}")
    #             return None, "注册过程中发生错误，请稍后重试。"

    def generate_token(self, user):
        """为用户生成JWT访问令牌和可选的刷新令牌。"""
        try:
            jwt_expires_config = current_app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 15)
            if isinstance(jwt_expires_config, timedelta):
                access_token_expires = jwt_expires_config
            elif isinstance(jwt_expires_config, (int, float)):
                access_token_expires = timedelta(minutes=jwt_expires_config)
            else:
                logger.warning(f"JWT_ACCESS_TOKEN_EXPIRES 配置格式不正确 ({type(jwt_expires_config)})，将使用默认15分钟。")
                access_token_expires = timedelta(minutes=15)
            
            payload = {
                'user_id': user.id,
                'email': user.email,
                'is_admin': user.is_admin,
                'exp': datetime.utcnow() + access_token_expires,
                'iat': datetime.utcnow(),
                'type': 'access' # 声明这是访问令牌
            }
            access_token = jwt.encode(payload, self.jwt_secret_key, algorithm='HS256')
            logger.info(f"已为用户 {user.email} (ID: {user.id}) 生成访问令牌。")
            return access_token
        except Exception as e:
            logger.error(f"为用户 {user.email} 生成令牌时出错: {e}", exc_info=True)
            return None

    def verify_token(self, token):
        """验证JWT令牌并返回用户对象或错误信息。"""
        try:
            payload = jwt.decode(token, self.jwt_secret_key, algorithms=['HS256'])
            user_id = payload.get('user_id')
            if not user_id:
                logger.warning(f"令牌验证失败：payload 中缺少 user_id。Payload: {payload}")
                return None, "令牌无效 (缺少用户信息)"
            
            user = User.query.get(user_id)
            if not user:
                logger.warning(f"令牌验证失败：找不到用户 ID {user_id}。")
                return None, "令牌关联的用户不存在"
            
            if not user.is_active:
                logger.warning(f"令牌验证失败：用户 {user.email} (ID: {user_id}) 账户已禁用。")
                return None, "用户账户已被禁用"

            # 令牌类型检查 (可选，但推荐)
            # token_type = payload.get('type')
            # if token_type != 'access':
            #     logger.warning(f"令牌验证失败：无效的令牌类型 '{token_type}'。")
            #     return None, "无效的令牌类型"

            logger.info(f"用户 {user.email} (ID: {user.id}) 已通过令牌验证。")
            return user, "令牌有效"
            
        except jwt.ExpiredSignatureError:
            logger.warning(f"令牌验证失败：令牌已过期。令牌开头: {token[:10]}...")
            return None, "令牌已过期"
        except jwt.InvalidTokenError as e:
            logger.warning(f"令牌验证失败：令牌无效。错误: {e}。令牌开头: {token[:10]}...")
            return None, f"令牌无效 ({str(e)})"
        except Exception as e:
            logger.error(f"令牌验证过程中发生意外错误: {e}", exc_info=True)
            return None, "令牌验证时发生服务器错误"

    def login_user(self, email, password, verification_code=None):
        user = User.query.filter_by(email=email).first()
        if not user:
            logger.warning(f"[登录失败-用户不存在] 邮箱:{email}, IP:{device_ip if 'device_ip' in locals() else '未知'}, 可能:输入错误或恶意尝试")
            return None, None, "用户不存在或密码错误"

        if not user.is_active:
            logger.warning(f"[登录失败-账户禁用] 邮箱:{email}, 状态:账户被管理员禁用, 影响:无法提供服务")
            return None, None, "您的账户已被禁用，请联系管理员。"

        # 检查密码
        if not user.check_password(password):
            logger.warning(f"[登录失败-密码错误] 邮箱:{email}, 尝试次数:需跟踪, 风险:可能存在暴力破解")
            return None, None, "用户不存在或密码错误"

        # 更新用户登录状态
        user.last_login = datetime.utcnow()
        db.session.commit()

        token = self.generate_token(user)
        logger.info(f"[登录成功] 用户:{email}, 状态:已颁发JWT令牌, 有效期:{current_app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 3600)}秒")
        return user, token, "登录成功"

    # def verify_email(self, email, code):
    #     with self.app.app_context():
    #         user = User.query.filter_by(email=email).first()
    #         if not user:
    #             return False, "用户不存在"
            
    #         if user.is_email_verified:
    #             return True, "邮箱已验证"
            
    #         email_service = self.email_service # 使用实例变量
    #         is_code_valid, message = email_service.verify_code(email, code, 'register') # 假设验证类型为'register'或通用

    #         if is_code_valid:
    #             user.is_email_verified = True
    #             user.email_verified_at = datetime.utcnow()
    #             db.session.commit()
    #             logger.info(f"用户 {email} 的邮箱已成功验证。")
    #             return True, "邮箱验证成功！"
    #         else:
    #             logger.warning(f"用户 {email} 邮箱验证失败：{message}")
    #             return False, message

    # def reset_password_request(self, email):
    #     with self.app.app_context():
    #         user = User.query.filter_by(email=email).first()
    #         if not user:
    #             logger.warning(f"密码重置请求失败：用户 {email} 不存在。")
    #             return False, "用户不存在"
        
    #         if not user.is_active:
    #             logger.warning(f"密码重置请求失败：用户 {email} 账户已被禁用。")
    #             return False, "账户已被禁用，无法重置密码。"

    #         email_service = self.email_service # 使用实例变量
    #         success, message, _ = email_service.send_verification_email(email, 'reset')
    #         if success:
    #             logger.info(f"已向用户 {email} 发送密码重置邮件。")
    #             return True, "密码重置邮件已发送，请检查您的邮箱。"
    #         else:
    #             logger.error(f"向用户 {email} 发送密码重置邮件失败：{message}")
    #             return False, f"发送密码重置邮件失败：{message}"

    # def reset_password(self, email, code, new_password):
    #     with self.app.app_context():
    #         user = User.query.filter_by(email=email).first()
    #         if not user:
    #             logger.warning(f"密码重置失败：用户 {email} 不存在。")
    #             return False, "用户不存在或验证码无效"
        
    #         email_service = self.email_service # 使用实例变量
    #         is_code_valid, message = email_service.verify_code(email, code, 'reset')
    #         if not is_code_valid:
    #             logger.warning(f"用户 {email} 密码重置验证码错误：{message}")
    #             return False, message

    #         is_valid, message = self.validate_password(new_password)
    #         if not is_valid:
    #             return False, message

    #         user.password_hash = generate_password_hash(new_password)
    #         user.is_email_verified = True # 重置密码通常也意味着邮箱是有效的
    #         # 清除可能存在的旧验证码记录，虽然当前 EmailVerification 是临时的
    #         EmailVerification.query.filter_by(email=email, code_type='reset').delete()
    #         db.session.commit()
    #         logger.info(f"用户 {email} 的密码已成功重置。")
    #         return True, "密码已成功重置，您现在可以用新密码登录。"
    
    def get_user_subscription(self, user_id):
        """获取用户当前有效的订阅"""
        active_sub = Subscription.query.filter(
            Subscription.user_id == user_id,
            Subscription.end_date > datetime.utcnow()
        ).order_by(
            Subscription.end_date.desc()
        ).first()
        
        # Optionally log if subscription found or not
        # if subscription:
        #     logger.debug(f"用户 {user_id} 找到有效订阅，结束日期: {subscription.end_date}")
        # else:
        #     logger.debug(f"用户 {user_id} 未找到有效订阅")
        
        return active_sub

    def get_adspower_login_info(self, user_id):
        """获取AdsPower账号的登录信息 (已简化 - 不再执行实时验证和会话创建)
        
        主要职责是根据用户订阅查找一个合适的、活跃的账号记录。
        实时验证和会话创建由 DirectLoginService 处理。
        """
        try:
            user = User.query.get(user_id)
            if not user:
                logger.error(f"获取AdsPower登录信息失败：用户ID '{user_id}' 不存在")
                return {
                    'success': False,
                    'message': '用户不存在',
                    'error_code': 'user_not_found'
                }

            # 检查用户是否有有效订阅
            subscription = self.get_user_subscription(user_id)
            if not subscription:
                logger.warning(f"获取AdsPower登录信息失败：用户 '{user_id}' 没有有效订阅") # Changed log level to warning
                return {
                    'success': False,
                    'message': '您没有有效的订阅',
                    'error_code': 'no_subscription'
                }

            # 获取用户的订阅类型
            subscription_type_code = subscription.plan

            # --- 简化查找逻辑 --- 
            # 只查找数据库记录，不执行实时检查
            adspower_account = AdspowerAccount.query.filter(
                AdspowerAccount.subscription_type == subscription_type_code,
                AdspowerAccount.is_active == True
            ).order_by(
                AdspowerAccount.created_at  # 按创建时间排序
            ).first()

            if not adspower_account:
                logger.warning(f"查找账号记录失败：没有找到订阅类型为 '{subscription_type_code}' 的可用AdsPower账号记录")
                return {
                    'success': False,
                    'message': f"数据库中没有可用的 '{subscription_type_code}' 类型账号记录",
                    'error_code': 'no_account_record_available'
                }

            # --- 构造简化的返回信息 (仅账号凭据) ---
            # 不再生成 login_url 或 login_token 在这里
            logger.info(f"成功为用户 '{user_id}' 找到AdsPower账号记录 '{adspower_account.username}' (订阅类型: {subscription_type_code})")
            return {
                'success': True,
                'message': '找到账号登录信息记录', # Changed message
                'data': {
                    'username': adspower_account.username,
                    'password': adspower_account.password,
                    'totp_secret': adspower_account.totp_secret,
                    'account_id': adspower_account.id
                }
            }

        except Exception as e:
            logger.exception(f"查找AdsPower登录信息记录时发生错误：{str(e)}")
            # Database rollback might not be needed if only reads were performed
            # try: db.session.rollback()
            # except: pass
            return {
                'success': False,
                'message': "系统内部错误，请联系管理员",
                'error_code': 'system_error'
            }


class DeviceAuthService:
    """设备认证服务，处理设备登录验证和分配"""
    
    def __init__(self):
        self.auth_service = AuthService()
    
    def register_device(self, user_id, device_id, device_info):
        """注册新设备"""
        ip_address = device_info.get('ip_address')
        user_agent = device_info.get('user_agent')
        
        
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            # Add logging
            logger.warning(f"设备注册失败：用户ID '{user_id}' 不存在")
            return False, None, "用户不存在"
        
        # 检查设备ID是否已存在
        existing_device = Device.query.filter_by(device_id=device_id).first()
        if existing_device:
            # 如果设备已存在且属于该用户，则直接返回
            if existing_device.user_id == user_id:
                # Add logging
                logger.info(f"设备 '{device_id}' 已由用户 '{user_id}' 注册，无需重复操作。")
                return True, existing_device, "设备已注册"
            # 如果设备已被其他用户占用，拒绝
            # Add logging
            logger.warning(f"设备注册失败：设备ID '{device_id}' 已被其他用户 ({existing_device.user_id}) 使用。")
            return False, None, "设备ID已被其他用户使用"
        
        # 检查用户是否有有效订阅
        subscription = self.auth_service.get_user_subscription(user_id)
        if not subscription:
            # Add logging
            logger.warning(f"设备注册失败：用户 '{user_id}' 没有有效订阅。")
            return False, None, "用户没有有效订阅"
        
        # 检查用户设备数量是否已达上限
        device_count = Device.query.filter_by(user_id=user_id).count()
        # Use >= for comparison
        if device_count >= subscription.max_devices:
            # Add logging
            logger.warning(f"设备注册失败：用户 '{user_id}' 设备数量 ({device_count}) 已达订阅上限 ({subscription.max_devices})。")
            return False, None, f"设备数量已达上限({subscription.max_devices}台)"
        
        # 获取用户订阅的实例ID
        subscription_instance_id = subscription.subscription_instance_id
        
        if not subscription_instance_id:
            logger.error(f"设备注册失败：用户 '{user_id}' 的订阅没有关联订阅实例")
            return False, None, "订阅配置错误，请联系管理员"
        
        # 查询该订阅实例下的可用AdsPower账号
        adspower_account = AdspowerAccount.query.filter(
            AdspowerAccount.subscription_instance_id == subscription_instance_id,
            AdspowerAccount.is_active == True,
            # AdspowerAccount.current_devices < AdspowerAccount.max_devices  # 注释掉，因为current_devices字段可能不存在
        ).order_by(
            func.random()  # 随机选择一个可用账号
        ).first()
        
        if not adspower_account:
            logger.warning(f"设备注册失败：订阅实例 '{subscription_instance_id}' 下没有可用的AdsPower账号")
            return False, None, "没有可用的账号，请联系管理员"
        
        # 创建设备记录
        device = Device(
            user_id=user_id,
            adspower_account_id=adspower_account.id,
            device_id=device_id,
            device_name=device_info.get('name', f"设备_{device_id[:8]}"),
            device_ip=device_info.get('ip_address'),
            device_type=device_info.get('type', 'unknown'),
            created_at=datetime.utcnow()
        )
        
        # 注：设备计数通过关联自动维护，无需手动更新
        
        db.session.add(device)
        # Add adspower_account to the session as well since we modified it
        db.session.add(adspower_account)
        try:
            db.session.commit()
            
            # 记录设备注册审计日志
            device_snapshot = {
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_type,
                'device_ip': device.device_ip,
                'adspower_account_id': adspower_account.id,
                'adspower_username': adspower_account.username,
                'subscription_instance_id': subscription_instance_id
            }
            
            DeviceAuditService.log_device_action(
                device_id=device.id,
                user_id=user_id,
                action=DeviceAuditService.ACTION_REGISTER,
                action_source='user',  # 用户自己注册设备
                description=f"用户 {user.email} 注册新设备 {device.device_name or device_id}",
                device_snapshot=device_snapshot,
                new_value={'status': 'active', 'adspower_account_id': adspower_account.id},
                ip_address=ip_address,
                user_agent=user_agent
            )
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"设备注册时数据库提交失败：用户 '{user_id}', 设备 '{device_id}'。错误: {e}", exc_info=True)
            return False, None, "设备注册时数据库错误"
        
        # 检查订阅类型（修复未定义的变量）
        subscription_type_code = subscription.subscription_type_details.code if subscription.subscription_type_details else 'unknown'
        logger.info(f"用户 '{user_id}' 注册新设备 '{device_id}' 成功 (关联AdsPower账号: {adspower_account.username}, 订阅类型: {subscription_type_code})。")
        return True, device, "设备注册成功"
    
    def verify_device(self, user_id, device_id):
        """验证设备是否属于用户且有效"""
        device = Device.query.filter_by(
            user_id=user_id,
            device_id=device_id
        ).first()
        
        if not device:
            # Add logging
            logger.warning(f"设备验证失败：设备 '{device_id}' 未注册或不属于用户 '{user_id}'。")
            return False, None, "设备未注册"
        
        # 检查用户订阅是否有效
        subscription = self.auth_service.get_user_subscription(user_id)
        if not subscription:
            # Add logging
            logger.warning(f"设备验证失败：用户 '{user_id}' (设备 '{device_id}') 订阅无效或已过期。")
            # Should we deactivate the device here?
            # device.status = 'inactive'
            # db.session.add(device)
            # db.session.commit()
            return False, None, "用户订阅已过期"
        
        # 设备验证成功
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            # Log commit error but proceed with verification success
            logger.error(f"验证设备 '{device_id}' (用户 '{user_id}') 时数据库提交失败: {e}", exc_info=True)
        
        # Log successful verification
        # logger.info(f"设备验证成功：设备 '{device_id}' (用户 '{user_id}')")
        return True, device, "设备验证成功"
    
    def get_device_info(self, device_id):
        """获取设备详细信息，包括关联的账号"""
        device = Device.query.filter_by(device_id=device_id).first()
        if not device:
            logger.warning(f"获取设备信息失败：设备ID '{device_id}' 未注册。")
            return False, None, "设备未注册"
        
        adspower_account = AdspowerAccount.query.get(device.adspower_account_id)
        if not adspower_account:
            # This indicates data inconsistency
            logger.error(f"获取设备信息失败：设备 '{device_id}' 关联的AdsPower账号ID '{device.adspower_account_id}' 不存在。")
            return False, None, "关联的ADSpower账号不存在"
        
        result = {
            "device": {
                "id": device.id,
                "device_id": device.device_id,
                "device_name": device.device_name,
                "device_type": device.device_type,
                "created_at": device.created_at.isoformat() if device.created_at else None
            },
            "adspower_account": {
                "id": adspower_account.id,
                "username": adspower_account.username,
                # "api_key": adspower_account.api_key # Consider if exposing API key is safe here
            }
        }
        
        logger.info(f"获取设备信息成功：设备ID '{device_id}' (用户: {device.user_id}, AdsPower账号: {adspower_account.username})。")
        return True, result, "获取设备信息成功"


class TwoFactorAuthService:
    """二因素认证服务，处理TOTP验证码和备用码生成"""
    
    def generate_totp_secret(self):
        """生成TOTP密钥"""
        return pyotp.random_base32()
    
    def get_totp_uri(self, secret, account_name, issuer="ADSpower共享平台"):
        """获取TOTP URI，用于生成二维码"""
        return pyotp.totp.TOTP(secret).provisioning_uri(
            name=account_name, issuer_name=issuer)
    
    def verify_totp(self, secret, code):
        """验证TOTP验证码"""
        totp = pyotp.TOTP(secret)
        verified = totp.verify(code, valid_window=1) # valid_window=1 allows for a 30s clock drift either way
        if verified:
            logger.info(f"TOTP验证成功：密钥开头 '{secret[:4]}...'。")
        else:
            # Try checking one window before and after explicitly for more tolerance if valid_window=0 or 1 doesn't work as expected
            # For example, if clock is off by more than 30s but less than 60s.
            # However, relying on a large valid_window can be a security risk.
            # It's better to ensure server and client times are synchronized.
            logger.warning(f"TOTP验证失败：密钥开头 '{secret[:4]}...', 代码 '{code}' (使用默认验证窗口)。")
        return verified
    
    def generate_backup_codes(self, count=10):
        """生成备用码"""
        codes = [''.join(random.choices(string.ascii_uppercase + string.digits, k=8)) for _ in range(count)]
        logger.info(f"已生成 {len(codes)} 个备用码。")
        return codes

    @staticmethod
    def generate_2fa_code(secret):
        """生成2FA验证码
        
        Args:
            secret: TOTP密钥
            
        Returns:
            (code, expires_in): 验证码和有效期（秒）
        """
        if not secret:
            logger.warning("生成2FA验证码失败：未提供TOTP密钥。")
            return None, 0
        
        try:
            totp = pyotp.TOTP(secret)
            code = totp.now()
            
            # interval = totp.interval # This is usually 30s
            # time_remaining = interval - (datetime.now().timestamp() % interval)
            time_remaining = totp.interval - (datetime.utcnow().timestamp() % totp.interval)

            
            logger.debug(f"为密钥 '{secret[:4]}...' 生成2FA验证码 '{code}'，有效期剩余 {int(time_remaining)} 秒。")
            return code, int(time_remaining)
        except Exception as e:
            logger.error(f"生成2FA验证码时发生异常：密钥 '{secret[:4]}...' ({e})", exc_info=True)
            return None, 0 
"""
设备审计服务
用于记录和查询设备操作历史，防止设备额度滥用
"""
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy import and_, or_, desc, case
from extensions import db
from adspower_manager.models import DeviceAudit, Device, User
import logging

logger = logging.getLogger(__name__)


class DeviceAuditService:
    """设备审计服务类"""
    
    # 操作类型常量
    ACTION_REGISTER = 'register'
    ACTION_DELETE = 'delete'
    ACTION_LOGIN = 'login'
    ACTION_LOGOUT = 'logout'
    ACTION_VERIFY_SUCCESS = 'verify_success'
    ACTION_VERIFY_FAILED = 'verify_failed'
    ACTION_UPDATE = 'update'
    ACTION_SUSPICIOUS = 'suspicious'
    
    
    @classmethod
    def log_device_action(cls, 
                         device_id: Optional[int],
                         user_id: int,
                         action: str,
                         action_source: str = 'user',
                         description: str = None,
                         device_snapshot: Dict[str, Any] = None,
                         old_value: Dict[str, Any] = None,
                         new_value: Dict[str, Any] = None,
                         operator_id: int = None,
                         ip_address: str = None,
                         user_agent: str = None) -> DeviceAudit:
        """
        记录设备操作审计日志
        
        Args:
            device_id: 设备ID（可选，设备删除后为NULL）
            user_id: 用户ID
            action: 操作类型
            action_source: 操作来源（user/admin/system）
            description: 操作描述
            device_snapshot: 设备信息快照
            old_value: 修改前的值
            new_value: 修改后的值
            operator_id: 操作者ID（管理员操作时）
            ip_address: 操作IP地址
            user_agent: 浏览器信息
            
        Returns:
            DeviceAudit: 创建的审计记录
        """
        try:
            audit = DeviceAudit(
                device_id=device_id,
                user_id=user_id,
                action=action,
                action_source=action_source,
                description=description,
                device_snapshot=device_snapshot,
                old_value=old_value,
                new_value=new_value,
                operator_id=operator_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.session.add(audit)
            db.session.commit()
            
            logger.info(f"设备审计日志已记录: {action} - User:{user_id}, Device:{device_id}")
            return audit
            
        except Exception as e:
            logger.error(f"记录设备审计日志失败: {str(e)}")
            db.session.rollback()
            raise
    
    @classmethod
    def get_device_audit_logs(cls,
                             device_id: int = None,
                             user_id: int = None,
                             action: str = None,
                             start_date: datetime = None,
                             end_date: datetime = None,
                             page: int = 1,
                             per_page: int = 20) -> tuple:
        """
        查询设备审计日志
        
        Args:
            device_id: 设备ID
            user_id: 用户ID
            action: 操作类型
            start_date: 开始时间
            end_date: 结束时间
            page: 页码
            per_page: 每页记录数
            
        Returns:
            tuple: (审计记录列表, 总记录数)
        """
        query = DeviceAudit.query
        
        if device_id:
            query = query.filter(DeviceAudit.device_id == device_id)
        if user_id:
            query = query.filter(DeviceAudit.user_id == user_id)
        if action:
            query = query.filter(DeviceAudit.action == action)
        if start_date:
            query = query.filter(DeviceAudit.created_at >= start_date)
        if end_date:
            query = query.filter(DeviceAudit.created_at <= end_date)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(DeviceAudit.created_at))
        
        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return pagination.items, pagination.total
    
    @classmethod
    def get_user_device_activities(cls, user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取用户设备活动统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            dict: 活动统计信息
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 查询指定时间范围内的审计记录
        audits = DeviceAudit.query.filter(
            and_(
                DeviceAudit.user_id == user_id,
                DeviceAudit.created_at >= start_date
            )
        ).all()
        
        # 统计各类操作
        stats = {
            'total_actions': len(audits),
            'register_count': 0,
            'delete_count': 0,
            'login_count': 0,
            'logout_count': 0,
            'unique_devices': set(),
            'unique_ips': set(),
            'daily_actions': {}
        }
        
        for audit in audits:
            # 统计操作类型
            if audit.action == cls.ACTION_REGISTER:
                stats['register_count'] += 1
            elif audit.action == cls.ACTION_DELETE:
                stats['delete_count'] += 1
            elif audit.action == cls.ACTION_LOGIN:
                stats['login_count'] += 1
            elif audit.action == cls.ACTION_LOGOUT:
                stats['logout_count'] += 1
            
            # 统计唯一设备和IP
            if audit.device_id:
                stats['unique_devices'].add(audit.device_id)
            if audit.ip_address:
                stats['unique_ips'].add(audit.ip_address)
            
            # 按日期统计
            date_key = audit.created_at.strftime('%Y-%m-%d')
            if date_key not in stats['daily_actions']:
                stats['daily_actions'][date_key] = 0
            stats['daily_actions'][date_key] += 1
        
        # 转换set为数量
        stats['unique_devices_count'] = len(stats['unique_devices'])
        stats['unique_ips_count'] = len(stats['unique_ips'])
        del stats['unique_devices']
        del stats['unique_ips']
        
        return stats
    
    
    @classmethod
    def get_device_change_statistics(cls, days: int = 30, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取设备更换次数统计（按用户）
        
        Args:
            days: 统计天数
            limit: 返回前N个用户
            
        Returns:
            list: 用户设备更换统计列表
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 查询设备注册和删除操作
        device_changes = db.session.query(
            DeviceAudit.user_id,
            db.func.count(DeviceAudit.id).label('total_changes'),
            db.func.sum(case((DeviceAudit.action == cls.ACTION_REGISTER, 1), else_=0)).label('register_count'),
            db.func.sum(case((DeviceAudit.action == cls.ACTION_DELETE, 1), else_=0)).label('delete_count'),
            db.func.count(db.distinct(DeviceAudit.ip_address)).label('unique_ips'),
            db.func.max(DeviceAudit.created_at).label('last_action_time')
        ).filter(
            and_(
                DeviceAudit.created_at >= start_date,
                DeviceAudit.action.in_([cls.ACTION_REGISTER, cls.ACTION_DELETE])
            )
        ).group_by(DeviceAudit.user_id).order_by(db.desc('total_changes')).limit(limit).all()
        
        result = []
        for record in device_changes:
            user = User.query.get(record.user_id)
            if user:
                # 计算更换频率（每天平均更换次数）
                change_frequency = record.total_changes / days if days > 0 else 0
                
                result.append({
                    'user_id': record.user_id,
                    'email': user.email,
                    'total_changes': record.total_changes,
                    'register_count': record.register_count or 0,
                    'delete_count': record.delete_count or 0,
                    'unique_ips': record.unique_ips or 0,
                    'last_action_time': record.last_action_time.isoformat() if record.last_action_time else None,
                    'change_frequency': round(change_frequency, 2),
                    'created_at': user.created_at.isoformat() if user.created_at else None
                })
        
        return result
import logging
import time
import uuid
from datetime import datetime, timedelta
from flask import url_for, current_app
from ..models import db, Payment, Subscription, User, SubscriptionType
import random
import string
import json
from .subscription_service import SubscriptionService
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP

logger = logging.getLogger(__name__)

class PaymentService:
    """支付服务基类"""
    
    def __init__(self):
        pass # Keep __init__ if empty, or add other initializations if needed
    
    def create_subscription(self, user_id, subscription_type_id, payment_id, amount):
        """创建订阅"""
        from ..models import SubscriptionType # Import here to avoid circular dependency issues
        sub_type = SubscriptionType.query.get(subscription_type_id)
        
        if not sub_type:
            logger.error(f"[业务错误-订阅创建失败] 订阅类型ID:{subscription_type_id}不存在, 影响:用户付款后无法获得服务")
            return None
        
        # 创建订阅记录
        now = datetime.utcnow()
        subscription = Subscription(
            user_id=user_id,
            subscription_type_id=sub_type.id,
            start_date=now,
            end_date=now + timedelta(days=sub_type.days),
            payment_id=payment_id,
            max_devices=sub_type.max_devices,
            created_at=now
        )
        
        db.session.add(subscription)
        db.session.commit()
        
        logger.info(f"[订阅创建成功] 用户ID:{user_id}, 订阅类型:{sub_type.name}, 有效期:{sub_type.days}天, 到期时间:{subscription.end_date}")
        return subscription
    
    def extend_subscription(self, subscription, subscription_type_id, payment_id, amount):
        """延长订阅"""
        from ..models import SubscriptionType
        sub_type = SubscriptionType.query.get(subscription_type_id)
        if not sub_type:
            logger.error(f"[业务错误-订阅延长失败] 订阅类型ID:{subscription_type_id}不存在, 影响:用户续费失败")
            return None
        
        # Use sub_type details for extension logic
        if subscription.end_date > datetime.utcnow():
            subscription.end_date = subscription.end_date + timedelta(days=sub_type.days)
        else:
            subscription.start_date = datetime.utcnow()
            subscription.end_date = subscription.start_date + timedelta(days=sub_type.days)
        
        subscription.payment_id = payment_id
        subscription.max_devices = max(subscription.max_devices, sub_type.max_devices)
        subscription.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"[订阅续费成功] 用户ID:{subscription.user_id}, 延长天数:{sub_type.days}天, 新到期时间:{subscription.end_date}")
        return subscription

    def create_payment(self, user_id, amount, payment_method, subscription_type_id):
        """创建支付记录
        
        Args:
            user_id: 用户ID
            amount: 金额
            payment_method: 支付方式（alipay, wechat等）
            subscription_type_id: 订阅计划的ID
            
        Returns:
            Payment对象，或None表示失败
        """
        try:
            # Find SubscriptionType by ID to get its details (like days)
            subscription_type = SubscriptionType.query.get(subscription_type_id)
            if not subscription_type:
                logger.error(f"[支付创建失败] 订阅类型ID:{subscription_type_id}不存在, 用户ID:{user_id}, 影响:无法生成支付订单")
                return None
            
            days = subscription_type.days

            # 生成唯一支付号
            payment_order_id = self._generate_payment_id()
            
            # 创建支付记录
            payment = Payment(
                payment_id=payment_order_id,
                order_id=payment_order_id,
                user_id=user_id,
                amount=amount,
                payment_method=payment_method,
                status='pending',
                created_at=datetime.utcnow(),
                subscription_days=days,
                subscription_type_id=subscription_type.id
            )
            
            db.session.add(payment)
            db.session.commit()
            
            logger.info(f"[支付订单创建] 用户ID:{user_id}, 订单号:{payment_order_id}, 金额:{amount}元, 订阅:{subscription_type.name}({days}天), 支付方式:{payment_method}")
            return payment
            
        except Exception as e:
            logger.error(f"[支付创建异常] 错误:{str(e)}, 影响:用户无法进入支付流程", exc_info=True)
            db.session.rollback()
            return None
    
    def _generate_payment_id(self):
        """生成唯一支付ID (类时间戳格式)
        
        Returns:
            支付ID字符串 (格式: YYYYMMDDHHMMSS + 4位随机数)
        """
        # 新格式: YYYYMMDDHHMMSS + 4位随机数
        timestamp_part = datetime.utcnow().strftime('%Y%m%d%H%M%S')
        random_part = ''.join(random.choices(string.digits, k=4)) # 4位纯数字
        payment_id = f"{timestamp_part}{random_part}"
        logger.debug(f"生成新的支付/订单ID: {payment_id}")
        return payment_id
    
    def process_payment_success(self, payment_id):
        """核心处理支付成功逻辑 (供内部调用)
        
        Args:
            payment_id: 支付ID (通常等于 out_trade_no)
            
        Returns:
            (bool, str): 是否处理成功, 消息
        """
        try:
            # 查找支付记录 (使用 payment_id，它等于 order_id)
            payment = Payment.query.filter_by(payment_id=payment_id).first()
            if not payment:
                logger.error(f"[支付处理失败] 订单号:{payment_id}不存在, 可能:重复回调或非法请求")
                return False, f"找不到支付记录 {payment_id}"

            # !! 检查状态，防止重复处理 !!
            if payment.status != 'pending':
                 logger.warning(f"[支付重复处理] 订单号:{payment_id}, 当前状态:{payment.status}, 决策:跳过处理避免重复发货")
                 # 认为是成功的，因为订单已经处理过
                 return True, f"订单状态为 {payment.status}，无需重复处理"
            
            # 更新支付状态
            payment.status = 'paid' # 使用 'paid' 表示已付款，区别于可能的 'completed'
            payment.paid_at = datetime.utcnow()
            
            # --- 创建或延长订阅 ---
            # 获取 subscription_type_id
            subscription_type_id = payment.subscription_type_id
            if not subscription_type_id:
                logger.error(f"核心支付处理失败：订单 {payment_id} 缺少 subscription_type_id 信息")
                payment.status = 'error'
                payment.remarks = "缺少 subscription_type_id"
                db.session.commit()
                return False, f"订单 {payment_id} 缺少 subscription_type_id"

            # 调用 SubscriptionService 处理订阅
            subscription, message = SubscriptionService.create_or_extend_subscription(
                user_id=payment.user_id,
                subscription_type_id=subscription_type_id,
                payment_id=payment.id  # 使用payment.id而不是payment.payment_id
            )
            
            if not subscription:
                logger.error(f"核心支付处理失败：处理支付 {payment_id} 时创建/延长订阅失败: {message}")
                payment.status = 'error' # 标记为错误状态
                payment.remarks = f"订阅处理失败: {message}"
                db.session.commit()
                return False, f"创建/延长订阅失败: {message}"
            
            # 更新支付记录关联的订阅ID
            payment.subscription_id = subscription.id
            # payment.status 保持 'paid'
            db.session.commit()
            logger.info(f"[支付成功] 订单号:{payment_id}, 用户ID:{payment.user_id}, 金额:{payment.amount}元, 订阅ID:{subscription.id}, 状态:已发货")
            return True, "支付成功并已处理订阅"

        except Exception as e:
            logger.exception(f"核心支付处理失败：处理支付 {payment_id} 时发生异常")
            db.session.rollback()
            # 尝试再次查找 payment 并标记为错误 (如果回滚前能找到)
            try:
                 payment = Payment.query.filter_by(payment_id=payment_id).first()
                 if payment and payment.status == 'pending': # 只有 pending 才标记 error
                     payment.status = 'error'
                     payment.remarks = f"内部处理异常: {str(e)[:100]}" # 记录部分错误信息
                     db.session.commit()
            except Exception as finalize_err:
                 logger.error(f"核心支付处理失败：标记订单 {payment_id} 为错误时再次失败: {finalize_err}", exc_info=True)

            return False, f"内部处理异常: {e}"

    # --- 新增: 处理易支付特定逻辑 ---
    def process_epay_payment(self, out_trade_no: str, epay_trade_no: str, amount_str: str) -> (bool, str):
        """处理易支付成功回调的业务逻辑。

        Args:
            out_trade_no: 商户订单号 (我们的 payment_id / order_id)
            epay_trade_no: 易支付平台的订单号
            amount_str: 回调中收到的金额字符串

        Returns:
            (bool, str): 处理是否成功, 消息
        """
        logger.info(f"[易支付处理] 开始处理订单: {out_trade_no}, 易支付单号: {epay_trade_no}, 金额: {amount_str}")

        # 1. 查找支付记录
        payment = Payment.query.filter_by(order_id=out_trade_no).first()
        if not payment:
            logger.error(f"[易支付处理] 失败：找不到订单 {out_trade_no}")
            return False, f"找不到订单 {out_trade_no}"

        # 2. 验证金额
        try:
            # 将数据库金额和回调金额都转为 Decimal 进行比较
            expected_amount = Decimal(payment.amount).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
            received_amount = Decimal(amount_str).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
            
            if expected_amount != received_amount:
                 logger.error(f"[易支付处理] 失败：订单 {out_trade_no} 金额不匹配。预期: {expected_amount},收到: {received_amount}")
                 # 可选：将订单标记为异常状态
                 payment.status = 'error'
                 payment.remarks = f"金额不匹配: 预期={expected_amount}, 收到={received_amount}"
                 db.session.commit()
                 return False, "订单金额不匹配"
        except (InvalidOperation, TypeError) as e:
            logger.error(f"[易支付处理] 失败：订单 {out_trade_no} 金额格式无效。预期值: '{payment.amount}', 收到值: '{amount_str}'. 错误: {e}", exc_info=True)
            payment.status = 'error'
            payment.remarks = f"金额格式无效: 收到='{amount_str}'"
            db.session.commit()
            return False, "金额格式无效"
        except Exception as e: # Catch unexpected errors during decimal conversion/comparison
            logger.exception(f"[易支付处理] 失败：订单 {out_trade_no} 金额比较时发生未知错误。")
            payment.status = 'error'
            payment.remarks = f"金额比较错误: {str(e)[:100]}"
            db.session.commit()
            return False, "金额比较时发生错误"

        # 3. 检查状态 (再次检查，process_payment_success 内部也会检查)
        if payment.status != 'pending':
            logger.warning(f"[易支付处理] 订单 {out_trade_no} 状态为 {payment.status}，无需处理。")
            return True, f"订单状态为 {payment.status}，无需重复处理"

        # 4. 更新易支付交易号
        payment.transaction_id = epay_trade_no # 记录易支付的 trade_no
        # 先提交 transaction_id，即使后续订阅失败也保留凭证
        try:
            db.session.commit()
            logger.info(f"[易支付处理] 已更新订单 {out_trade_no} 的 transaction_id 为 {epay_trade_no}")
        except Exception as e:
            logger.exception(f"[易支付处理] 更新订单 {out_trade_no} transaction_id 时失败，回滚")
            db.session.rollback()
            # 如果更新失败，后续处理也无法进行
            return False, "更新支付凭证失败"

        # 5. 调用核心支付成功处理逻辑 (处理状态更新和订阅)
        # 使用 payment.payment_id (等于 order_id)
        success, message = self.process_payment_success(payment.payment_id) 
        
        # process_payment_success 内部会提交或回滚，这里只需返回结果
        return success, message


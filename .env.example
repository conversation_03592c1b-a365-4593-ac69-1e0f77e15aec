# Flask配置
# APP_ENV: testing (测试环境,本地登录) 或 production (生产环境,单点登录)
APP_ENV=production
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
PORT=5000

# 数据库配置
DATABASE_URL=sqlite:///instance/app.db

# JWT配置
JWT_ACCESS_TOKEN_EXPIRES_HOURS=24
JWT_REFRESH_TOKEN_EXPIRES_DAYS=30

# AdsPower配置
ADSPOWER_API_BASE=https://api-global.adspower.net/v1
ADSPOWER_API_KEY=your-adspower-api-key

# 邮件配置
MAIL_SERVER=smtp.163.com
MAIL_PORT=465
MAIL_USE_TLS=False
MAIL_USE_SSL=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER="AI服务拼车共享平台 <<EMAIL>>"
EMAIL_CODE_EXPIRY_MINUTES=10

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_NUMBER=True
PASSWORD_REQUIRE_SPECIAL_CHAR=False
FAILED_LOGIN_MAX_ATTEMPTS=5
FAILED_LOGIN_LOCKOUT_TIME=300

# 支付宝配置
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payments/alipay/notify
ALIPAY_RETURN_URL=https://yourdomain.com/payments/alipay/result

# 易支付配置
EPAY_PID=your-epay-pid
EPAY_KEY=your-epay-key
EPAY_SUBMIT_URL=https://pay.netzz.net/submit.php
EPAY_API_URL=https://pay.netzz.net/mapi.php
EPAY_NOTIFY_URL=https://yourdomain.com/api/payments/epay/notify
EPAY_RETURN_URL=https://yourdomain.com/payments/epay/result

# OIDC (Keycloak) 配置
OIDC_ISSUER_URL=https://auth.example.com/realms/myrealm
OIDC_CLIENT_ID=my-flask-client
OIDC_CLIENT_SECRET=your-oidc-client-secret
OIDC_REDIRECT_URI=https://yourdomain.com/api/oidc/callback
OIDC_SCOPES=openid email profile
OIDC_JIT_USER_PROVISIONING=True

# WebDriver配置
WEBDRIVER_POOL_SIZE=10
WEBDRIVER_DRIVER_TIMEOUT=1800
WEBDRIVER_CHECK_INTERVAL=300

# 默认用户配置（仅用于初始化）
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=changeme123
DEFAULT_USER_EMAIL=<EMAIL>
DEFAULT_USER_PASSWORD=userpass123
CREATE_DEFAULT_USER=False

# 默认订阅类型配置
CREATE_DEFAULT_SUB_TYPE=True
DEFAULT_SUB_MAX_DEVICES=1
DEFAULT_SUB_PRICE=1.0
DEFAULT_SUB_DISCOUNT=100
DEFAULT_SUB_DAYS=1

# 维护模式
MAINTENANCE_MODE=false